# AGENTS.md

## Build/Test Commands
- **Build**: `pnpm run build` (turbo build across workspaces)
- **Dev**: `pnpm run dev` (turbo dev), `pnpm run dev:pwa` (with backend)
- **Lint**: `pnpm run lint` (turbo lint), `pnpm run lint:fix` (auto-fix)
- **Type Check**: `pnpm run type-check` (turbo type-check)
- **Test**: `pnpm run test` (turbo test), `pnpm run test:run`, `pnpm run test:coverage`
- **Format**: `pnpm run format`, `pnpm run format:check`
- **Docker**: `pnpm run docker:backend`, `pnpm run docker:down`
- **Mobile**: `cd voicecode-fe-1 && pnpm run cap:ios` (iOS), `pnpm run cap:android` (Android)

## Database Migrations
- **Start DB**: `docker compose -f docker-compose.yml up -d postgres`
- **Check Current**: `cd voicecode/fastapi-app && DATABASE_URL=postgresql://voicecode:voicecode@localhost:9432/voicecode uv run alembic current`
- **Migration History**: `cd voicecode/fastapi-app && DATABASE_URL=postgresql://voicecode:voicecode@localhost:9432/voicecode uv run alembic history`
- **Upgrade**: `cd voicecode/fastapi-app && DATABASE_URL=postgresql://voicecode:voicecode@localhost:9432/voicecode uv run alembic upgrade head`
- **Downgrade**: `cd voicecode/fastapi-app && DATABASE_URL=postgresql://voicecode:voicecode@localhost:9432/voicecode uv run alembic downgrade -1`
- **Specific Revision**: `cd voicecode/fastapi-app && DATABASE_URL=postgresql://voicecode:voicecode@localhost:9432/voicecode uv run alembic upgrade/downgrade [revision]`

## Code Style Guidelines
- **TypeScript**: Strict mode, use `@/` path aliases for src imports
- **React**: Functional components with hooks, use `React.ComponentProps<"element">` for props
- **Imports**: Group by external → internal → relative, use `@/` for src paths
- **Naming**: camelCase for variables/functions, PascalCase for components/types, kebab-case for files
- **State**: Zustand stores with devtools/persist, interface separation (State/Actions)
- **UI**: Radix UI + Tailwind, use `cn()` utility for className merging
- **Error Handling**: Try/catch with proper error types, console.log with emojis for debugging
- **Types**: Define interfaces separately, use `type` for unions, explicit return types for functions

## Project Structure
- Monorepo with Turbo, workspaces: `voicecode-fe-1` (React/Vite), `voicecode/fastapi-app` (Python)
- Frontend: React 19 + TypeScript + Vite + Tailwind + Zustand + React Router