{"name": "voicecode-monorepo", "version": "1.0.0", "private": true, "description": "VoiceCode monorepo with frontend PWA and backend services", "directories": {"doc": "docs"}, "scripts": {"dev": "turbo dev", "dev:pwa": "npm run docker:backend && cd voicecode-pwa && npm run dev", "docker:backend": "docker compose up -d voicecode-api postgres redis", "docker:down": "docker compose down", "build": "turbo build", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "type-check": "turbo type-check", "test": "turbo test", "test:run": "turbo test:run", "test:coverage": "turbo test:coverage", "format": "turbo format", "format:check": "turbo format:check", "clean": "turbo clean"}, "keywords": ["voicecode", "monorepo", "pwa", "voice", "development"], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "devDependencies": {"turbo": "^2.5.4"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "next-themes": "^0.4.6", "react-file-icon": "^1.6.0", "react-highlight-words": "^0.21.0", "react-medium-image-zoom": "^5.3.0"}}