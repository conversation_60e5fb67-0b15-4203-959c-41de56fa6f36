# VoiceCode Landing Page

This package contains the public-facing landing page for the VoiceCode application, built with React, Vite, and TypeScript.

## Overview

The landing page is designed to attract potential users, explain the value proposition of VoiceCode, and encourage them to sign up for the early access waitlist. It is a separate frontend application focused on marketing and conversion optimization.

## Key Features

*   **Waitlist Signup**: A clear and compelling call-to-action to capture user interest.
*   **FAQ Section**: Addresses common questions and concerns to build trust.
*   **Social Proof**: Includes elements like a waitlist counter to demonstrate community interest.
*   **Analytics Integration**: A comprehensive analytics system to track user engagement and conversion rates.
*   **Responsive Design**: Fully responsive for a seamless experience on both desktop and mobile devices.

## Technology Stack

*   **Framework**: [React](https://react.dev/) with [Vite](https://vitejs.dev/)
*   **Language**: [TypeScript](https://www.typescriptlang.org/)
*   **Styling**: [Tailwind CSS](https://tailwindcss.com/)
*   **UI Components**: [shadcn/ui](https://ui.shadcn.com/)
*   **Database**: [Supabase](https://supabase.com/) with PostgreSQL
*   **Deployment**: [Vercel](https://vercel.com/) with automatic CI/CD
*   **Analytics**: [PostHog](https://posthog.com/) + Google Analytics

## Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (v18 or later)
*   [pnpm](https://pnpm.io/)

### Setup

1.  **Install dependencies** from the root of the monorepo:
    ```bash
    pnpm install
    ```

2.  **Set up environment variables**:

    Configure your environment variables by updating the `.env` file with your Supabase credentials:

    ```bash
    # Database Backend Configuration
    VITE_DATABASE_BACKEND=2  # Use Supabase backend

    # Supabase Configuration
    VITE_SUPABASE_URL=https://your-project.supabase.co
    VITE_SUPABASE_ANON_KEY=your-anon-key-here

    # Analytics Configuration (optional)
    VITE_POSTHOG_KEY=your-posthog-key
    VITE_GA_MEASUREMENT_ID=your-ga-id
    ```

3.  **Set up Supabase database**:

    Follow the instructions in [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) to create the required database tables and configure Row Level Security policies.

### Running the Application

To start the development server, run the following command from the root of the monorepo:

```bash
pnpm dev
```

This will start the landing page application, typically on `http://localhost:9101`.

## 🚀 Deployment

### Production Deployment

The application is deployed on Vercel with automatic CI/CD. For detailed deployment instructions, see [VERCEL_DEPLOYMENT.md](./VERCEL_DEPLOYMENT.md).

**Live Production URL**: https://voicecode-landing-page-j36t44mav-hoang-9071s-projects.vercel.app

### Quick Deploy

```bash
# Deploy to production
vercel --prod

# Deploy to preview
vercel
```

### Environment Variables for Production

Configure these environment variables in your Vercel project:

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_DATABASE_BACKEND` | Backend type (2 for Supabase) | ✅ |
| `VITE_SUPABASE_URL` | Supabase project URL | ✅ |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | ✅ |
| `VITE_ENVIRONMENT` | Environment (production) | ✅ |
| `VITE_POSTHOG_KEY` | PostHog analytics key | ❌ |
| `VITE_GA_MEASUREMENT_ID` | Google Analytics ID | ❌ |

## 📊 Analytics

The landing page is equipped with a detailed analytics framework found in `src/lib/analytics.ts`. This system tracks key user interactions to measure the effectiveness of the page and inform optimization efforts. For more details, see the [Analytics Setup Guide](./docs/analytics-setup.md).

## 🛡️ Trust Signals

To build confidence with a developer audience, the landing page incorporates several trust signals. The strategy behind these elements is detailed in the [Trust Signals Strategy document](./docs/trust-signals-strategy.md).

## 📋 Database Schema

The application uses Supabase with a simple waitlist table:

```sql
CREATE TABLE waitlist (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

For complete database setup instructions, see [SUPABASE_SETUP.md](./SUPABASE_SETUP.md).

## 🔧 Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build locally
- `pnpm lint` - Run ESLint

### Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── FAQ.tsx         # FAQ section
│   ├── JoinWaitList.tsx # Waitlist signup form
│   └── ...
├── lib/                # Utilities and configurations
│   ├── supabase.ts     # Supabase client and operations
│   ├── analytics.ts    # Analytics tracking
│   └── utils.ts        # Helper functions
├── hooks/              # Custom React hooks
└── services/           # API and external services
```

## 🚨 Troubleshooting

### Common Issues

1. **Supabase Connection Error**: Verify environment variables and RLS policies
2. **Build Failures**: Check TypeScript errors and missing dependencies
3. **Deployment Issues**: See [VERCEL_DEPLOYMENT.md](./VERCEL_DEPLOYMENT.md) troubleshooting section

### Getting Help

- Check the [troubleshooting section](./VERCEL_DEPLOYMENT.md#troubleshooting) in deployment docs
- Review Vercel deployment logs: `vercel logs`
- Test locally: `pnpm build && pnpm preview`