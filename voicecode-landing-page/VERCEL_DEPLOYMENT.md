# Vercel Deployment Guide

This document provides comprehensive instructions for deploying the VoiceCode Landing Page to Vercel.

## 🚀 Quick Deployment

### Prerequisites

- [Vercel CLI](https://vercel.com/cli) installed globally
- Supabase project set up (see [SUPABASE_SETUP.md](./SUPABASE_SETUP.md))
- Environment variables configured

### One-Command Deploy

```bash
vercel --prod
```

## 📋 Detailed Setup

### 1. Install Vercel CLI

```bash
npm install -g vercel
```

### 2. Login to Vercel

```bash
vercel login
```

Follow the email authentication process.

### 3. Environment Variables Setup

The following environment variables must be configured in your Vercel project:

| Variable | Description | Example Value |
|----------|-------------|---------------|
| `VITE_DATABASE_BACKEND` | Backend type (2 for Supabase) | `2` |
| `VITE_SUPABASE_URL` | Your Supabase project URL | `https://xrqfwnqyepuaiwinxhlr.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `VITE_ENVIRONMENT` | Environment type | `production` |
| `VITE_ANALYTICS_ENABLED` | Enable analytics | `true` |
| `VITE_POSTHOG_KEY` | PostHog analytics key | `phc_VFZuYnUpY6L6kUj5zK4vAalV6XDH5zO8...` |

#### Adding Environment Variables via CLI

```bash
# Add each environment variable
vercel env add VITE_DATABASE_BACKEND production
vercel env add VITE_SUPABASE_URL production
vercel env add VITE_SUPABASE_ANON_KEY production
vercel env add VITE_ENVIRONMENT production
```

#### Adding Environment Variables via Dashboard

1. Go to [vercel.com](https://vercel.com)
2. Navigate to your project
3. Go to **Settings** → **Environment Variables**
4. Add each variable for **Production**, **Preview**, and **Development** environments

### 4. Project Configuration

The project uses a streamlined `vercel.json` configuration:

```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

This configuration:
- ✅ Enables SPA routing
- ✅ Serves static assets with correct MIME types
- ✅ Lets Vercel auto-detect the Vite build process

### 5. Deploy to Production

```bash
# Deploy to production
vercel --prod

# Or deploy to preview
vercel
```

## 🔧 Build Configuration

### Automatic Detection

Vercel automatically detects this as a Vite project and:
- Runs `npm install` (or detects pnpm via packageManager field)
- Executes `npm run build`
- Serves files from the `dist/` directory

### Build Script

The build process runs:
```bash
vite build
```

Which generates optimized assets in the `dist/` directory.

### Build Output

```
dist/
├── index.html
├── assets/
│   ├── index-[hash].css
│   └── index-[hash].js
└── vite.svg
```

## 📊 Analytics Integration

The deployment includes:
- **PostHog Analytics**: Real-time user behavior tracking
- **Google Analytics**: Page views and conversion tracking
- **Custom Analytics**: Backend integration for detailed metrics

Analytics are controlled by the `VITE_ANALYTICS_ENABLED` environment variable.

## 🔒 Security Configuration

### Environment Variables

- All sensitive keys are stored as encrypted Vercel environment variables
- No secrets are committed to the repository
- Environment variables are scoped to specific environments

### Supabase Security

- Uses Row Level Security (RLS) for database access
- Anonymous key has limited INSERT permissions only
- Production database is isolated from development

## 🌐 Custom Domain Setup

### Adding a Custom Domain

1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Domains**
3. Add your custom domain (e.g., `voicecode.io`)
4. Configure DNS records as instructed by Vercel

### SSL Certificate

Vercel automatically provides SSL certificates for all domains.

## 📈 Performance Optimization

### Build Optimizations

The build includes:
- **Code Splitting**: Automatic chunk splitting for better loading
- **Asset Optimization**: CSS and JS minification
- **Gzip Compression**: Automatic compression for faster delivery

### Current Bundle Sizes

```
dist/index.html                     0.46 kB │ gzip:   0.30 kB
dist/assets/index-[hash].css       100.45 kB │ gzip:  15.67 kB  
dist/assets/index-[hash].js      1,243.47 kB │ gzip: 348.74 kB
```

### Performance Recommendations

- Consider code splitting for the large JS bundle
- Implement dynamic imports for non-critical components
- Use Vercel's Edge Network for global CDN distribution

## 🔍 Monitoring and Debugging

### Vercel Dashboard

Monitor your deployment through:
- **Functions**: View serverless function logs
- **Analytics**: Traffic and performance metrics  
- **Speed Insights**: Core Web Vitals tracking
- **Real-time Logs**: Live deployment and runtime logs

### Environment Debugging

Check environment variables are loaded correctly:

```bash
# List all environment variables
vercel env list

# Pull environment variables locally  
vercel env pull .env.local
```

## 🚨 Troubleshooting

### Common Issues

#### 1. MIME Type Error

**Error**: `Failed to load module script: Expected a JavaScript module script but the server responded with a MIME type of "text/html"`

**Solution**: Ensure `vercel.json` uses `rewrites` instead of `routes`:

```json
{
  "rewrites": [
    {
      "source": "/(.*)", 
      "destination": "/index.html"
    }
  ]
}
```

#### 2. Environment Variable Not Found

**Error**: `Environment Variable "VITE_X" references Secret "vite_x", which does not exist`

**Solution**: Remove secret references from `vercel.json` and use environment variables directly via the dashboard or CLI.

#### 3. Build Failures

**Error**: Build fails during `npm run build`

**Solutions**:
- Check all environment variables are set
- Verify dependencies in `package.json`
- Test build locally: `npm run build`

#### 4. Supabase Connection Issues

**Error**: 401 errors or RLS policy violations

**Solutions**:
- Verify `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are correct
- Check Supabase RLS policies allow anonymous inserts
- Test database connection locally

### Deployment Logs

View detailed deployment logs:

```bash
# View latest deployment
vercel logs

# View specific deployment  
vercel logs [deployment-url]
```

## 📚 Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html#vercel)
- [Supabase + Vercel Integration](https://supabase.com/docs/guides/getting-started/tutorials/with-vercel)

## 🔄 Continuous Deployment

### Git Integration

Connect your repository to Vercel for automatic deployments:

1. Push code to GitHub/GitLab/Bitbucket
2. Import repository in Vercel dashboard
3. Every push to `main` branch triggers production deployment
4. Pull requests create preview deployments

### Branch Deployments

- `main` branch → Production deployment
- Feature branches → Preview deployments
- Each deployment gets a unique URL for testing

## 📞 Support

For deployment issues:
1. Check this documentation
2. Review Vercel deployment logs
3. Test locally with `vercel dev`
4. Contact team for assistance

---

**Current Production URL**: https://voicecode-landing-page-j36t44mav-hoang-9071s-projects.vercel.app

**Last Updated**: July 28, 2025