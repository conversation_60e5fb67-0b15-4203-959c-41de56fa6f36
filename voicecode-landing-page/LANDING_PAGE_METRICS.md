# Landing Page Analytics & Optimization Guide

## PostHog Setup & Configuration

### Environment Variables
Add these to your `.env` file:
```bash
VITE_POSTHOG_KEY=phc_your_project_key_here
VITE_POSTHOG_HOST=https://us.i.posthog.com  # Optional, defaults to US cloud
VITE_ANALYTICS_ENABLED=true  # Set to false to disable all analytics
```

### PostHog Project Configuration
1. Create a PostHog project at https://posthog.com
2. Copy your project API key from Project Settings
3. Enable the following features in PostHog:
   - Session recordings (for user behavior analysis)
   - Feature flags (for A/B testing)
   - Cohorts (for user segmentation)
   - Funnels (for conversion analysis)

## Core Conversion Metrics

### 1. Primary Conversion Funnel
**Objective**: Track the complete user journey from landing to email signup

**Key Events to Monitor:**
- `$pageview` - Initial page load
- `page_view` - Our custom page view tracking
- `hero_video_play` - Video engagement start
- `hero_video_complete` - Full video watched
- `faq_expand` - FAQ interaction (shows user interest)
- `email_signup_attempt` - User starts signup process
- `email_signup_complete` - Successful email capture

**PostHog Analysis:**
1. **Funnel Analysis**: Create a funnel in PostHog:
   - Step 1: Page View
   - Step 2: Any Engagement (video play OR FAQ expand)
   - Step 3: Email Signup Complete

2. **Conversion Rate Calculation**:
   ```
   Overall Conversion Rate = (Email Signups / Page Views) × 100
   Target: 3-5% for landing pages, 8-12% for optimized pages
   ```

### 2. Email Signup Conversion Rates by Traffic Source
**Objective**: Identify highest-quality traffic sources

**PostHog Setup:**
- Filter funnel by `utm_source`, `utm_medium`, `utm_campaign`
- Create cohorts for each traffic source
- Compare conversion rates across sources

**Key Metrics:**
- **Organic Search**: Usually 2-4% conversion
- **Direct Traffic**: Often 4-8% (high intent users)
- **Social Media**: Typically 1-3% (awareness stage)
- **Paid Ads**: Should be 5-10% (targeted traffic)
- **Referrals**: Varies widely, 3-15%

**Optimization Actions:**
- Increase budget for highest-converting sources
- Create source-specific landing page variants
- Adjust messaging based on traffic source intent

### 3. Video Engagement Impact Analysis
**Objective**: Measure how video engagement affects conversion rates

**PostHog Analysis:**
1. Create cohort of users who played video
2. Create cohort of users who completed video
3. Compare conversion rates:
   - No video interaction: X%
   - Started video: Y%  
   - Completed video: Z%

**Expected Results:**
- Video starters convert 2-3x better than non-viewers
- Video completers convert 4-6x better than non-viewers

**Optimization Actions:**
- A/B test video thumbnails and autoplay settings
- Test different video lengths (30s vs 60s vs 90s)
- Add video CTAs at key moments

## User Behavior Analytics

### 4. Page Engagement Patterns
**Objective**: Understand how users interact with your landing page

**PostHog Features to Use:**
- **Session Recordings**: Watch actual user sessions
- **Heatmaps**: See where users click and scroll
- **Scroll Depth Tracking**: Measure content consumption

**Key Metrics:**
- **Average Session Duration**: Target 60-90 seconds
- **Scroll Depth**: 
  - 25% scroll: ~90% of visitors
  - 50% scroll: ~70% of visitors  
  - 75% scroll: ~40% of visitors
  - 100% scroll: ~20% of visitors
- **Bounce Rate**: Target under 60%

**Custom Events to Add:**
```javascript
// Add these to your components
posthog.capture('scroll_25_percent')
posthog.capture('scroll_50_percent') 
posthog.capture('scroll_75_percent')
posthog.capture('time_on_page_30s')
posthog.capture('time_on_page_60s')
```

### 5. FAQ Interaction Analysis
**Objective**: Identify user concerns and optimize content

**Current Tracking**: `faq_expand` with question text and category

**PostHog Dashboards to Create:**
1. **Most Opened FAQ Questions** (shows biggest user concerns)
2. **FAQ Category Performance** (technical vs pricing vs trust)
3. **FAQ Impact on Conversions** (do FAQ interactions help or hurt?)

**Analysis Questions:**
- Which questions are opened most? (Address these in hero copy)
- Do users who open FAQs convert better? (Shows engagement)
- Which FAQ categories correlate with signups?

**Optimization Actions:**
- Move popular questions higher up
- Create dedicated sections for popular concerns
- A/B test FAQ placement (before vs after signup form)

### 6. Social Proof Element Effectiveness
**Objective**: Measure impact of trust signals on conversions

**Current Tracking**: `social_proof_click` for waitlist counter interactions

**Additional Events to Track:**
```javascript
posthog.capture('social_proof_view', { element: 'waitlist_counter' })
posthog.capture('trust_badge_click', { badge_type: 'security' })
posthog.capture('testimonial_interaction', { testimonial_id: 1 })
```

**A/B Tests to Run:**
- Different waitlist numbers (127 vs 200+ vs 500+)
- With/without growth indicators ("23 this week")
- Trust badge variations and placement

## Traffic & Acquisition Analytics

### 7. UTM Campaign Performance
**Objective**: Optimize marketing spend and messaging

**PostHog Setup:**
- All events automatically capture UTM parameters
- Create dashboard filtering by campaign/source/medium

**Key Reports:**
1. **Conversion Rate by Campaign**
2. **Cost Per Conversion** (if you have ad spend data)
3. **Lifetime Value by Source** (track post-signup behavior)

**Campaign Attribution Window**: 
- First-touch attribution (initial discovery)
- Last-touch attribution (final decision)
- Multi-touch attribution (full journey)

### 8. Referral Source Quality Analysis
**Objective**: Identify and nurture best referral partnerships

**Metrics to Track:**
- **Referral Volume**: Number of visitors from each source
- **Referral Quality**: Conversion rate from each source  
- **Referral Engagement**: Time on site, pages viewed
- **Referral Growth**: Month-over-month trends

**PostHog Analysis:**
```javascript
// Create cohorts for top referral sources
// Compare behavior patterns between sources
// Track long-term user value by referral source
```

## Performance & Technical Metrics

### 9. Page Load Time Impact
**Objective**: Measure technical performance impact on conversions

**PostHog Web Analytics Features:**
- Automatic page load timing
- Core Web Vitals tracking
- Device/browser performance breakdowns

**Key Correlations to Analyze:**
- Load time vs conversion rate
- Mobile vs desktop performance
- Browser-specific conversion differences

**Performance Targets:**
- **First Contentful Paint**: < 1.5 seconds
- **Largest Contentful Paint**: < 2.5 seconds  
- **Cumulative Layout Shift**: < 0.1

### 10. Mobile vs Desktop Optimization
**Objective**: Optimize experience for each device type

**PostHog Device Breakdown:**
- Conversion rates by device type
- Feature usage differences (video play rates)
- Form completion patterns

**Mobile-Specific Metrics:**
- Touch vs click interactions
- Scroll behavior differences
- Form field completion rates

## A/B Testing Framework

### 11. PostHog Feature Flags Setup
**Objective**: Continuously optimize through systematic testing

**Test Ideas by Priority:**

**High Impact Tests:**
1. **Headline Variations**
   - Current: "VoiceCode: Speak Your Code Into Existence"  
   - Test: "Code With Your Voice, Not Your Keyboard"
   - Test: "Voice-Powered Development for Modern Developers"

2. **CTA Button Copy**
   - Current: "Join Waitlist"
   - Test: "Get Early Access"
   - Test: "Reserve My Spot"

3. **Video vs Static Hero**
   - Control: Demo video
   - Test: Static mockup with play button
   - Test: Animated GIF

**Medium Impact Tests:**
4. **Social Proof Positioning**
   - Above fold vs below email form
   - Different count displays
   - With/without growth indicators

5. **FAQ Section Placement**
   - Before email signup vs after
   - Expanded vs collapsed by default
   - Different question ordering

**Low Impact Tests:**
6. **Color Scheme Variations**
7. **Font/Typography Changes**
8. **Button Size/Shape Variations**

### PostHog A/B Test Implementation:
```javascript
// In your React components:
import { useFeatureFlagEnabled } from 'posthog-js/react'

function HeroSection() {
  const showNewHeadline = useFeatureFlagEnabled('new-headline-test')
  
  return (
    <h1>
      {showNewHeadline 
        ? "Code With Your Voice, Not Your Keyboard"
        : "VoiceCode: Speak Your Code Into Existence"
      }
    </h1>
  )
}
```

## Dashboard Setup & Monitoring

### 12. PostHog Dashboard Configuration

**Executive Dashboard** (Daily monitoring):
- Total conversions (today, this week)
- Conversion rate trend (7-day rolling average)
- Top traffic sources
- Performance alerts (conversion rate drops)

**Marketing Dashboard** (Campaign optimization):
- UTM campaign performance
- Traffic source breakdown
- Cost per conversion (if ad spend integrated)
- Referral source analysis

**Product Dashboard** (User experience):
- Feature engagement rates (video, FAQ, social proof)
- User journey funnel
- Session recordings of converted vs non-converted users
- Technical performance metrics

**Weekly Review Dashboard**:
- Week-over-week growth metrics
- A/B test results and statistical significance
- User feedback and session recording insights
- Conversion rate by day/hour (identify optimal posting times)

### 13. Alert Setup
Configure PostHog alerts for:
- Conversion rate drops below 2%
- Daily signup count drops by 30%
- Page load time increases above 3 seconds
- Error rate increases (JavaScript errors)

### 14. Data Export & Integration
**Weekly/Monthly Reports:**
- Export cohort data for email marketing segmentation
- Share performance reports with stakeholders
- Integrate with your email platform for user journey tracking

## Optimization Playbook

### Quick Wins (Implement First):
1. **Add scroll depth tracking** - Easy to implement, high insight value
2. **Set up conversion funnel** - Immediately identify drop-off points  
3. **Create traffic source cohorts** - Optimize marketing spend
4. **Enable session recordings** - Direct user behavior insights

### Medium-Term Optimizations:
1. **A/B test hero section** - Highest impact on conversions
2. **Optimize FAQ content** - Address common user concerns
3. **Mobile experience optimization** - 50%+ of traffic is mobile
4. **Video engagement optimization** - High-converting users watch video

### Advanced Optimizations:
1. **Multi-touch attribution modeling** - Complex user journeys
2. **Cohort-based landing page personalization** - Dynamic content
3. **Predictive analytics** - Identify high-value prospects
4. **Cross-platform user journey tracking** - Email to landing page

## Success Metrics Timeline

**Week 1-2**: Baseline establishment
- Implement all tracking events
- Gather 1000+ page views for statistical significance
- Set up core dashboards

**Week 3-4**: Initial optimizations  
- Identify biggest conversion blockers
- Implement quick wins (copy changes, layout tweaks)
- Start first A/B test

**Month 2**: Data-driven improvements
- Analyze A/B test results
- Optimize based on session recordings
- Refine traffic acquisition strategy

**Month 3+**: Advanced optimization
- Implement personalization based on traffic source
- Advanced funnel analysis and optimization
- Scale successful marketing channels

**Target Goals:**
- **Month 1**: Establish 3-5% conversion rate
- **Month 2**: Improve to 5-7% through optimization
- **Month 3**: Achieve 7-10% with advanced techniques
- **Ongoing**: Maintain 10%+ conversion rate through continuous testing

## Technical Implementation Checklist

- [x] PostHog SDK installed and configured
- [x] All tracking events implemented
- [x] Environment variables configured
- [ ] PostHog dashboards created
- [ ] Feature flags enabled for A/B testing
- [ ] Session recordings enabled
- [ ] Alert system configured
- [ ] Weekly review process established

Remember: Always ensure statistical significance before making decisions based on A/B test results. Generally need 100+ conversions per variation for reliable results.