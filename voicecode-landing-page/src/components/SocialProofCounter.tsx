import { useEffect, useState } from "react"
import { Users, TrendingUp } from "lucide-react"
import { useAnalytics } from "./AnalyticsProvider"

interface SocialProofCounterProps {
  initialCount?: number
  targetCount?: number
  animationDuration?: number
  showTrending?: boolean
}

export function SocialProofCounter({ 
  initialCount = 127, 
  targetCount = 127,
  animationDuration = 2000,
  showTrending = true 
}: SocialProofCounterProps) {
  const [currentCount, setCurrentCount] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const { trackSocialProofClick } = useAnalytics()

  useEffect(() => {
    // Trigger animation when component mounts
    const timer = setTimeout(() => setIsVisible(true), 100)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (!isVisible) return

    const increment = Math.ceil(targetCount / (animationDuration / 50))
    let current = initialCount

    const counter = setInterval(() => {
      current += increment
      if (current >= targetCount) {
        setCurrentCount(targetCount)
        clearInterval(counter)
      } else {
        setCurrentCount(current)
      }
    }, 50)

    return () => clearInterval(counter)
  }, [isVisible, targetCount, animationDuration, initialCount])

  // Format number with commas for larger numbers
  const formatCount = (count: number) => {
    return count.toLocaleString()
  }

  // Calculate recent growth (simulated)
  const recentGrowth = Math.floor(targetCount * 0.08) // 8% recent growth

  return (
    <div className="flex flex-col items-center gap-2">
      {/* Main Counter Display */}
      <div 
        className="flex items-center gap-2 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-full px-4 py-2 border border-green-200/50 dark:border-green-800/50 cursor-pointer hover:scale-105 transition-transform"
        onClick={() => trackSocialProofClick('waitlist_counter')}
      >
        <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
        <span className="text-sm font-semibold text-green-700 dark:text-green-300">
          <span className="tabular-nums">
            {formatCount(currentCount)}+
          </span>
          <span className="ml-1">developers waiting</span>
        </span>
      </div>

      {/* Trending Indicator */}
      {showTrending && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <TrendingUp className="h-3 w-3 text-orange-500" />
          <span>+{recentGrowth} joined this week</span>
        </div>
      )}

      {/* Trust Signals */}
      <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Growing daily</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>From 47 countries</span>
        </div>
      </div>
    </div>
  )
}

// Alternative compact version for smaller spaces
export function CompactSocialProof({ count = 127 }: { count?: number }) {
  return (
    <div className="inline-flex items-center gap-2 bg-green-50 dark:bg-green-900/20 rounded-full px-3 py-1 text-sm">
      <div className="flex -space-x-1">
        {/* Simulated user avatars */}
        <div className="w-5 h-5 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full border border-white dark:border-gray-800"></div>
        <div className="w-5 h-5 bg-gradient-to-br from-green-400 to-green-600 rounded-full border border-white dark:border-gray-800"></div>
        <div className="w-5 h-5 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full border border-white dark:border-gray-800"></div>
        <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-full border border-white dark:border-gray-800 flex items-center justify-center">
          <span className="text-xs font-medium text-gray-600 dark:text-gray-300">+</span>
        </div>
      </div>
      <span className="font-medium text-green-700 dark:text-green-300">
        {count.toLocaleString()}+ waiting
      </span>
    </div>
  )
}