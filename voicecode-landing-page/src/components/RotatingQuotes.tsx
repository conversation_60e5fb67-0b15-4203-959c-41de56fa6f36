import { useState, useEffect } from "react"

const quotes = [
  "Why tap when you can talk? Your thumbs deserve a break! 📱",
  "Coding on the subway? Just whisper your functions like sweet nothings 🚇",
  "Finally, a legitimate reason to talk to yourself in public 🗣️",
  "Who needs a mechanical keyboard when you have vocal cords? 🎤",
  "Debug by dictation - because shouting at your phone IS productive! 📢",
  "Mobile development: Where 'speaking in code' is literally speaking code 💬",
  "Your daily standup just got a whole new meaning 🏃‍♂️",
  "Code reviews? More like voice memos to your future self 🎵",
  "Breaking: Local developer seen having heated argument with smartphone about semicolons 📰",
  "Warning: May cause strange looks when you start flirting with your IDE in public 😍",
  "Rubber duck debugging? Try rubber duck dictating! 🦆",
  "The only framework where 'talking dirty' means discussing memory leaks 🧠",
  "Autocomplete but with attitude - your phone now argues back about variable names 🤖",
  "Finally! A way to code that doesn't require pants (or a desk, or dignity) 👖",
  "Version control through voice control - git commit -m 'fixed it, probably' 🎯"
]

export function RotatingQuotes() {
  const [currentQuote, setCurrentQuote] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false)
      
      setTimeout(() => {
        setCurrentQuote((prev) => (prev + 1) % quotes.length)
        setIsVisible(true)
      }, 300)
    }, Math.random() * 5000 + 5000) // Random interval between 5-10 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div 
        className={`bg-gradient-to-r from-orange-100/50 to-amber-100/50 dark:from-orange-900/20 dark:to-amber-900/20 backdrop-blur-sm border border-orange-200/30 dark:border-orange-800/30 rounded-xl p-4 text-center transition-all duration-300 ${
          isVisible ? 'opacity-100 scale-100' : 'opacity-70 scale-95'
        }`}
      >
        <p className="text-sm font-medium text-orange-800 dark:text-orange-200 leading-relaxed">
          {quotes[currentQuote]}
        </p>
      </div>
    </div>
  )
}