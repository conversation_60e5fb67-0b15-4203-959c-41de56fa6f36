import { useState } from "react"
import { Play, Pause } from "lucide-react"

export function VideoDemo() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handlePlay = () => {
    setIsPlaying(true)
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const handleLoadedData = () => {
    setIsLoading(false)
  }

  const handleCanPlay = () => {
    setIsLoading(false)
  }

  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
  }

  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-border/20 rounded-2xl shadow-lg p-6 w-full max-w-4xl relative before:absolute before:inset-0 before:rounded-2xl before:border before:border-transparent before:bg-gradient-to-r before:from-orange-500/20 before:via-orange-400/20 before:to-amber-400/20 before:p-[1px] before:-z-10 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-orange-500 via-orange-400 to-amber-400 bg-clip-text text-transparent">
          See VoiceCode in Action
        </h2>
        <p className="text-muted-foreground">
          Watch how VoiceCode transforms voice commands into powerful code operations
        </p>
      </div>
      
      <div className="relative rounded-xl overflow-hidden bg-black/5 dark:bg-white/5">
        {isLoading && !hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/10 dark:bg-white/10 z-10">
            <div className="flex items-center space-x-2 text-muted-foreground">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">Loading demo...</span>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/10 dark:bg-white/10 z-10">
            <div className="text-center text-muted-foreground">
              <p className="text-sm">Unable to load video demo</p>
              <p className="text-xs mt-1">Check browser console for details</p>
            </div>
          </div>
        )}
        
        <video
          className="w-full h-auto max-h-[500px] object-contain"
          controls
          preload="metadata"
          playsInline
          muted
          onPlay={handlePlay}
          onPause={handlePause}
          onLoadedData={handleLoadedData}
          onCanPlay={handleCanPlay}
          onError={handleError}
          poster=""
        >
          <source src="/VoiceCode.webm" type="video/webm" />
          <source src="/VoiceCode.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        {!isPlaying && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-orange-500/90 rounded-full p-4">
              <Play className="h-8 w-8 text-white ml-1" />
            </div>
          </div>
        )}
      </div>
      
      <div className="mt-4 text-center">
        <p className="text-xs text-muted-foreground">
          Experience the future of development - code naturally with your voice
        </p>
      </div>
    </div>
  )
}