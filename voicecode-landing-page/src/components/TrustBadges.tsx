import { Shield, Lock, Zap, Globe, Users, Code } from "lucide-react"
import { useAnalytics } from "./AnalyticsProvider"

interface TrustBadgesProps {
  variant?: "minimal" | "detailed" | "security"
  className?: string
}

export function TrustBadges({ variant = "minimal", className = "" }: TrustBadgesProps) {
  const { track } = useAnalytics()

  const trackTrustClick = (element: string) => {
    track('trust_signal_click', {
      element_type: element,
      variant: variant,
      location: 'trust_badges'
    })
  }
  if (variant === "minimal") {
    return (
      <div className={`flex items-center justify-center gap-4 text-xs text-muted-foreground ${className}`}>
        <div 
          className="flex items-center gap-1 cursor-pointer hover:text-green-600 transition-colors"
          onClick={() => trackTrustClick('privacy_first')}
        >
          <Shield className="h-3 w-3 text-green-500" />
          <span>Privacy first</span>
        </div>
        <div 
          className="flex items-center gap-1 cursor-pointer hover:text-blue-600 transition-colors"
          onClick={() => trackTrustClick('secure_design')}
        >
          <Lock className="h-3 w-3 text-blue-500" />
          <span>Secure by design</span>
        </div>
        <div 
          className="flex items-center gap-1 cursor-pointer hover:text-purple-600 transition-colors"
          onClick={() => trackTrustClick('built_for_devs')}
        >
          <Code className="h-3 w-3 text-purple-500" />
          <span>Built for devs</span>
        </div>
      </div>
    )
  }

  if (variant === "security") {
    return (
      <div className={`grid grid-cols-2 gap-3 text-xs ${className}`}>
        <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Shield className="h-4 w-4 text-green-600" />
          <div>
            <div className="font-medium text-green-700 dark:text-green-300">Local Processing</div>
            <div className="text-green-600 dark:text-green-400">Code never leaves your machine</div>
          </div>
        </div>
        
        <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Lock className="h-4 w-4 text-blue-600" />
          <div>
            <div className="font-medium text-blue-700 dark:text-blue-300">End-to-End Encryption</div>
            <div className="text-blue-600 dark:text-blue-400">Military-grade security</div>
          </div>
        </div>
        
        <div className="flex items-center gap-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <Users className="h-4 w-4 text-purple-600" />
          <div>
            <div className="font-medium text-purple-700 dark:text-purple-300">Open Source</div>
            <div className="text-purple-600 dark:text-purple-400">Community audited</div>
          </div>
        </div>
        
        <div className="flex items-center gap-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <Globe className="h-4 w-4 text-orange-600" />
          <div>
            <div className="font-medium text-orange-700 dark:text-orange-300">GDPR Compliant</div>
            <div className="text-orange-600 dark:text-orange-400">Privacy protected</div>
          </div>
        </div>
      </div>
    )
  }

  // detailed variant
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center">
        <h4 className="font-semibold text-sm mb-2">Trusted by developers worldwide</h4>
        <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3 text-yellow-500" />
            <span>Lightning fast</span>
          </div>
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3 text-green-500" />
            <span>Privacy protected</span>
          </div>
          <div className="flex items-center gap-1">
            <Code className="h-3 w-3 text-blue-500" />
            <span>Production ready</span>
          </div>
        </div>
      </div>
      
      {/* Company/Integration Logos Placeholder */}
      <div className="flex items-center justify-center gap-4 opacity-60">
        <div className="text-xs text-muted-foreground">Works with:</div>
        <div className="flex items-center gap-3">
          <div className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">VS Code</div>
          <div className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">Cursor</div>
          <div className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">Vim</div>
        </div>
      </div>
      
      {/* Sponsored by Daytona Badge */}
      <div className="flex items-center justify-center mt-4">
        <a 
          href="https://daytona.io/startups?utm_source=voicecode"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block hover:opacity-80 transition-opacity"
          onClick={() => trackTrustClick('daytona_sponsor')}
        >
          <img 
            src="https://img.shields.io/badge/SPONSORED%20BY-DAYTONA%20STARTUP%20GRID-2ECC71?style=for-the-badge" 
            alt="Sponsored by Daytona Startup Grid"
            className="h-6"
          />
        </a>
      </div>
    </div>
  )
}

// Compact version for footer or smaller spaces
export function CompactTrustSignals() {
  return (
    <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
      <span className="flex items-center gap-1">
        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
        Privacy first
      </span>
      <span className="flex items-center gap-1">
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
        Local processing
      </span>
      <span className="flex items-center gap-1">
        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
        Open source
      </span>
    </div>
  )
}