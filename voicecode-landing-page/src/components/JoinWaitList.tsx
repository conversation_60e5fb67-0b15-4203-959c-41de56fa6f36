import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send, Loader2 } from "lucide-react"
import { useState } from "react"
import { databaseService } from "@/services/database"

export function JoinWaitList() {
  const [email, setEmail] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async () => {
    if (!email.trim()) return
    
    setIsSubmitting(true)
    setError("")
    
    // Basic client-side email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email.trim())) {
      setError("Please enter a valid email address")
      setIsSubmitting(false)
      return
    }
    
    try {
      const result = await databaseService.addToWaitlist({
        email: email.trim(),
        website: "", // Honeypot field - should always be empty
        metadata: {
          source: 'landing_page',
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          referrer: document.referrer || 'direct'
        }
      })
      
      if (result.success) {
        setIsSubmitted(true)
      } else {
        setError(result.error || "Something went wrong. Please try again.")
      }
    } catch (error) {
      console.error('Unexpected error during waitlist signup:', error)
      setError("Network error. Please check your connection and try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  if (isSubmitted) {
    return (
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-border/20 rounded-2xl shadow-lg p-6 w-full max-w-2xl relative before:absolute before:inset-0 before:rounded-2xl before:border before:border-transparent before:bg-gradient-to-r before:from-orange-500/20 before:via-orange-400/20 before:to-amber-400/20 before:p-[1px] before:-z-10 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300">
        <div className="text-center">
          <div className="text-3xl mb-3">🚀</div>
          <h3 className="text-xl font-bold text-green-600 mb-2">Welcome to the Future of Coding!</h3>
          <p className="text-sm text-muted-foreground mb-3">
            You're now on the VoiceCode early access list. We'll notify you as soon as we're ready to revolutionize your development workflow.
          </p>
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-xs text-orange-700 dark:text-orange-300">
            <strong>What's next?</strong> Follow us for updates and join our Discord community for early previews and developer discussions.
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-border/20 rounded-2xl shadow-lg p-6 w-full max-w-2xl relative before:absolute before:inset-0 before:rounded-2xl before:border before:border-transparent before:bg-gradient-to-r before:from-orange-500/20 before:via-orange-400/20 before:to-amber-400/20 before:p-[1px] before:-z-10 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300">
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold mb-3 bg-gradient-to-r from-orange-500 via-orange-400 to-amber-400 bg-clip-text text-transparent">
          VoiceCode
        </h1>
        
        <p className="text-lg text-muted-foreground mb-6">
          Code with your voice. Join the waitlist.
        </p>
      </div>
      
      <div className="space-y-4">
        {/* Honeypot field - hidden from users but visible to bots */}
        <input 
          type="text" 
          name="website" 
          style={{ display: "none" }} 
          tabIndex={-1} 
          autoComplete="off"
        />
        
        <div className="flex items-center gap-3">
          <div className="flex-1 relative">
            <Textarea
              placeholder="Enter your email for early access..."
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyDown={handleKeyDown}
              className="min-h-[48px] resize-none border-0 bg-transparent p-3 placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg border border-border/40"
              disabled={isSubmitting}
            />
          </div>

          <Button 
            onClick={handleSubmit} 
            disabled={!email.trim() || isSubmitting} 
            size="sm"
            className="h-12 w-12 p-0 rounded-full"
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 text-center p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
            {error}
          </div>
        )}
        
        <p className="text-xs text-muted-foreground text-center">
          No spam, just early access notifications.
        </p>
      </div>
    </div>
  )
}