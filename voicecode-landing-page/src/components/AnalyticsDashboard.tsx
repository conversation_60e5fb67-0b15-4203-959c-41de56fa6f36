import { useState, useEffect } from "react"
import { useAnalytics } from "./AnalyticsProvider"
import { BarChart3, TrendingUp, Users, Mail } from "lucide-react"

// This component is for development/testing - remove before production
export function AnalyticsDashboard() {
  const { getFunnelData, getConversionRate } = useAnalytics()
  const [funnelData, setFunnelData] = useState(getFunnelData())
  const [conversionRate, setConversionRate] = useState(getConversionRate())
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setFunnelData(getFunnelData())
      setConversionRate(getConversionRate())
    }, 1000)

    return () => clearInterval(interval)
  }, [getFunnelData, getConversionRate])

  if (import.meta.env.PROD) {
    return null // Hide in production
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Toggle Analytics Dashboard"
      >
        <BarChart3 className="h-5 w-5" />
      </button>

      {/* Dashboard Panel */}
      {isVisible && (
        <div className="fixed bottom-20 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 w-80 z-50">
          <div className="flex items-center gap-2 mb-4">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-sm">Analytics Dashboard</h3>
          </div>

          {/* Conversion Rate */}
          <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Conversion Rate</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {conversionRate.toFixed(2)}%
            </div>
            <div className="text-xs text-muted-foreground">
              {funnelData.email_signup_completions} / {funnelData.page_view} visitors
            </div>
          </div>

          {/* Funnel Metrics */}
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <Users className="h-3 w-3" />
                <span>Page Views</span>
              </div>
              <span className="font-medium">{funnelData.page_view}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                <span>Video Engagement</span>
              </div>
              <span className="font-medium">{funnelData.hero_video_engagement}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                <span>FAQ Interactions</span>
              </div>
              <span className="font-medium">{funnelData.faq_interactions}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 bg-orange-500 rounded-full"></div>
                <span>Signup Attempts</span>
              </div>
              <span className="font-medium">{funnelData.email_signup_attempts}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <Mail className="h-3 w-3 text-green-600" />
                <span>Completions</span>
              </div>
              <span className="font-medium text-green-600">{funnelData.email_signup_completions}</span>
            </div>
          </div>

          {/* Funnel Visualization */}
          <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="text-xs text-muted-foreground mb-2">Conversion Funnel</div>
            <div className="space-y-1">
              {[
                { label: 'Views', value: funnelData.page_view, color: 'bg-gray-400' },
                { label: 'Attempts', value: funnelData.email_signup_attempts, color: 'bg-orange-400' },
                { label: 'Signups', value: funnelData.email_signup_completions, color: 'bg-green-400' }
              ].map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-xs w-16">{item.label}</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`${item.color} h-2 rounded-full transition-all duration-300`}
                      style={{ 
                        width: `${funnelData.page_view > 0 ? (item.value / funnelData.page_view) * 100 : 0}%` 
                      }}
                    ></div>
                  </div>
                  <span className="text-xs w-8">{item.value}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-600 text-xs text-muted-foreground">
            Updates every second • Dev only
          </div>
        </div>
      )}
    </>
  )
}