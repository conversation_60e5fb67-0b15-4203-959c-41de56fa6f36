import { createContext, useContext, useEffect, type ReactNode } from 'react'
import { analytics } from '../lib/analytics'

interface AnalyticsContextType {
  track: (event: string, properties?: Record<string, any>) => void
  trackPageView: (page?: string) => void
  trackEmailSignup: (email?: string) => void
  trackFAQInteraction: (question: string, index: number) => void
  trackSocialProofClick: (element: string) => void
  getConversionRate: () => number
  getFunnelData: () => any
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null)

export function AnalyticsProvider({ children }: { children: ReactNode }) {
  useEffect(() => {
    // Initialize analytics on mount
    analytics.init()
  }, [])

  const contextValue: AnalyticsContextType = {
    track: analytics.track.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackEmailSignup: analytics.trackEmailSignupComplete.bind(analytics),
    trackFAQInteraction: analytics.trackFAQExpand.bind(analytics),
    trackSocialProofClick: analytics.trackSocialProofClick.bind(analytics),
    getConversionRate: analytics.getConversionRate.bind(analytics),
    getFunnelData: analytics.getFunnelData.bind(analytics)
  }

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  )
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext)
  if (!context) {
    throw new Error('useAnalytics must be used within AnalyticsProvider')
  }
  return context
}