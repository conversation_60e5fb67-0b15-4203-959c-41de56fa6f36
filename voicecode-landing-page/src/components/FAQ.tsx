import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { useAnalytics } from "./AnalyticsProvider"

interface FAQItem {
  question: string
  answer: string
}

const faqData: FAQItem[] = [
  {
    question: "How does VoiceCode work with my GitHub repositories?",
    answer: "VoiceCode integrates seamlessly with GitHub through OAuth 2.0 authentication. Once connected, you can access any of your repositories, create branches, and submit pull requests using voice commands. The app uses GitHub Apps with secure token management to ensure safe repository access."
  },
  {
    question: "How secure is code execution in VoiceCode?",
    answer: "VoiceCode executes all code in isolated Daytona sandboxes - secure, ephemeral environments that are completely separate from your local machine. Each session runs in a fresh sandbox with Claude Code CLI pre-installed. Your code is never stored permanently, and sandboxes are destroyed after use."
  },
  {
    question: "What is Claude Code CLI and how does VoiceCode use it?",
    answer: "Claude Code CLI is <PERSON><PERSON><PERSON>'s official coding assistant. VoiceCode bridges your voice commands to Claude Code CLI running in secure sandboxes. When you speak a coding task, VoiceCode converts it to text, sends it to Claude Code CLI in the sandbox, and returns the results to your mobile device."
  },
  {
    question: "Can I work with any programming language or framework?",
    answer: "Yes! Since VoiceCode uses Claude Code CLI in the backend, it supports all the languages and frameworks that Claude Code supports - including JavaScript, TypeScript, Python, Go, Rust, Java, C++, React, Next.js, Django, and many more. The sandbox environment can be customized for your specific tech stack."
  },
  {
    question: "How does voice processing work and is it private?",
    answer: "VoiceCode uses on-device speech recognition through native iOS/Android capabilities via Capacitor. Your voice is processed entirely on your device - no audio data leaves your phone. This ensures maximum privacy and works offline, providing fast, secure speech-to-text conversion without any external API dependencies."
  },
  {
    question: "What happens if I lose internet connection?",
    answer: "VoiceCode requires internet connectivity to access GitHub repositories, create sandboxes, and process voice commands. However, the app gracefully handles connection issues with offline queuing and automatic retry mechanisms. Your work progress is preserved across connection interruptions."
  },
  {
    question: "How do I get started with VoiceCode?",
    answer: "Simply join our early access waitlist! Once accepted, you'll receive the mobile app, connect your GitHub account, and start coding with voice commands immediately. No complex setup required - we handle the sandbox creation and Claude Code CLI integration automatically."
  }
]

export function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([])
  const { trackFAQInteraction } = useAnalytics()

  const toggleItem = (index: number) => {
    const isOpening = !openItems.includes(index)
    
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )

    // Track FAQ interaction only when opening (not closing)
    if (isOpening) {
      trackFAQInteraction(faqData[index].question, index)
    }
  }

  return (
    <div className="w-full max-w-2xl mx-auto mt-8">
      <h3 className="text-xl font-semibold text-center mb-6">
        Frequently Asked Questions
      </h3>
      <div className="space-y-3">
        {faqData.map((item, index) => (
          <div 
            key={index}
            className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-border/20 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => toggleItem(index)}
              className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors"
            >
              <span className="font-medium text-sm">
                {item.question}
              </span>
              {openItems.includes(index) ? (
                <ChevronUp className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              ) : (
                <ChevronDown className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              )}
            </button>
            {openItems.includes(index) && (
              <div className="px-4 pb-3 pt-0">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {item.answer}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}