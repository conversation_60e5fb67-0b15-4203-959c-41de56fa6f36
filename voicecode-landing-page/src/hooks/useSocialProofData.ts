import { useState, useEffect } from 'react'

interface SocialProofData {
  waitlistCount: number
  weeklyGrowth: number
  countries: number
  lastUpdated: string
}

// This can be connected to your actual API later
const MOCK_DATA: SocialProofData = {
  waitlistCount: 127,
  weeklyGrowth: 23,
  countries: 47,
  lastUpdated: new Date().toISOString()
}

export function useSocialProofData() {
  const [data, setData] = useState<SocialProofData>(MOCK_DATA)
  const [isLoading] = useState(false)

  useEffect(() => {
    // TODO: Replace with actual API call
    // fetchSocialProofData().then(setData)
    
    // For now, simulate slight growth over time
    const interval = setInterval(() => {
      setData(prev => ({
        ...prev,
        waitlistCount: prev.waitlistCount + Math.floor(Math.random() * 3),
        lastUpdated: new Date().toISOString()
      }))
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const updateCount = (newCount: number) => {
    setData(prev => ({
      ...prev,
      waitlistCount: newCount,
      lastUpdated: new Date().toISOString()
    }))
  }

  return {
    ...data,
    isLoading,
    updateCount
  }
}

// Utility function to format social proof messaging
export function formatSocialProofMessage(count: number): string {
  if (count < 100) return `Join ${count}+ developers on the waitlist`
  if (count < 500) return `${count}+ developers already waiting`
  if (count < 1000) return `${count}+ developers ready for early access`
  if (count < 5000) return `${(count / 1000).toFixed(1)}k+ developers in the community`
  return `${Math.floor(count / 1000)}k+ developers worldwide`
}