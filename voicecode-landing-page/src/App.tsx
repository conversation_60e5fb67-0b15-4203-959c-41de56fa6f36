import { Join<PERSON><PERSON><PERSON><PERSON> } from './components/JoinWaitList'
import { FAQ } from './components/FAQ'
import { RotatingQuotes } from './components/RotatingQuotes'
import { VideoDemo } from './components/VideoDemo'
import { TrustBadges } from './components/TrustBadges'
import { AnalyticsProvider } from './components/AnalyticsProvider'
import { AnalyticsDashboard } from './components/AnalyticsDashboard'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import ReactPlugin from '@stagewise-plugins/react'
import './App.css'

function App() {
  return (
    <AnalyticsProvider>
      <div className="w-full min-h-screen flex flex-col items-center justify-start py-8 px-4 space-y-8">
        <RotatingQuotes />
        <VideoDemo />
        <JoinWaitList />
        <TrustBadges variant="detailed" />
        <FAQ />
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
        <AnalyticsDashboard />
      </div>
    </AnalyticsProvider>
  )
}

export default App
