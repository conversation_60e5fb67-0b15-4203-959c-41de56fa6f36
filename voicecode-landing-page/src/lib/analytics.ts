// Analytics tracking system for conversion optimization
// Uses PostHog for comprehensive product analytics

import posthog from 'posthog-js'

interface AnalyticsEvent {
  event: string
  properties?: Record<string, any>
  timestamp?: number
}

interface ConversionFunnelData {
  page_view: number
  hero_video_engagement: number
  faq_interactions: number
  email_signup_attempts: number
  email_signup_completions: number
}

class Analytics {
  private isInitialized = false
  private isEnabled = import.meta.env.VITE_ANALYTICS_ENABLED !== 'false'
  private conversionFunnel: ConversionFunnelData = {
    page_view: 0,
    hero_video_engagement: 0,
    faq_interactions: 0,
    email_signup_attempts: 0,
    email_signup_completions: 0
  }

  // Initialize PostHog
  init() {
    if (this.isInitialized || !this.isEnabled) return

    const posthogKey = import.meta.env.VITE_POSTHOG_KEY
    const posthogHost = import.meta.env.VITE_POSTHOG_HOST || 'https://us.i.posthog.com'


    if (typeof window !== 'undefined' && posthogKey) {
      try {
        posthog.init(posthog<PERSON><PERSON>, {
          api_host: posthogHost,
          person_profiles: 'identified_only',
          capture_pageview: false, // We'll handle this manually
          capture_pageleave: true,
          // Development mode settings
          ...(import.meta.env.DEV && {
            opt_out_capturing_by_default: false,
            disable_session_recording: false,
            debug: true,
            // Allow localhost in development
            cross_subdomain_cookie: false,
          }),
          loaded: (posthog) => {
            if (import.meta.env.DEV) {
              // posthog.debug()
            }
          }
        })
      } catch (error) {
        // PostHog initialization failed - continue silently
      }
    } else {
    }

    this.isInitialized = true
    this.trackPageView()
  }

  // Core tracking method
  track(event: string, properties: Record<string, any> = {}) {
    if (!this.isEnabled) {
      return
    }

    const eventData: AnalyticsEvent = {
      event,
      properties: {
        ...properties,
        timestamp: Date.now(),
        url: typeof window !== 'undefined' ? window.location.href : '',
        referrer: typeof document !== 'undefined' ? document.referrer : ''
      }
    }

    // Update conversion funnel
    this.updateConversionFunnel(event)

    // Send to PostHog
    if (typeof window !== 'undefined' && posthog.__loaded) {
      try {
        posthog.capture(event, eventData.properties)
      } catch (error) {
      }
    } else {
    }

  }

  // Page view tracking
  trackPageView(page?: string) {
    const pageData = {
      page: page || (typeof window !== 'undefined' ? window.location.pathname : '/'),
      utm_source: this.getUTMParameter('utm_source'),
      utm_medium: this.getUTMParameter('utm_medium'),
      utm_campaign: this.getUTMParameter('utm_campaign')
    }
    
    
    // Use PostHog's built-in pageview tracking
    if (typeof window !== 'undefined' && posthog.__loaded) {
      try {
        posthog.capture('$pageview', pageData)
      } catch (error) {
      }
    } else {
    }
    
    // Also track in our internal funnel
    this.track('page_view', pageData)
  }

  // Conversion funnel specific events
  trackHeroVideoPlay() {
    this.track('hero_video_play', {
      video_type: 'hero_demo',
      position: 'above_fold'
    })
  }

  trackHeroVideoComplete() {
    this.track('hero_video_complete', {
      video_type: 'hero_demo',
      position: 'above_fold'
    })
  }

  trackFAQExpand(question: string, questionIndex: number) {
    this.track('faq_expand', {
      question: question,
      question_index: questionIndex,
      question_category: this.categorizeFAQQuestion(question)
    })
  }

  trackEmailSignupAttempt(email?: string) {
    this.track('email_signup_attempt', {
      has_email: !!email,
      email_domain: email ? email.split('@')[1] : null,
      form_location: 'hero_section'
    })
  }

  trackEmailSignupComplete(email?: string) {
    const properties = {
      has_email: !!email,
      email_domain: email ? email.split('@')[1] : null,
      form_location: 'hero_section'
    }
    
    // Identify user in PostHog if email provided
    if (email && typeof window !== 'undefined' && posthog.__loaded) {
      posthog.identify(email, { email })
    }
    
    this.track('email_signup_complete', properties)
  }

  trackSocialProofClick(element: string) {
    this.track('social_proof_click', {
      element_type: element,
      social_proof_count: this.getSocialProofCount()
    })
  }

  trackBenefitHover(benefit: string, benefitIndex: number) {
    this.track('benefit_hover', {
      benefit_text: benefit,
      benefit_index: benefitIndex
    })
  }

  trackCTAClick(ctaText: string, location: string) {
    this.track('cta_click', {
      cta_text: ctaText,
      cta_location: location
    })
  }

  // Conversion rate calculations
  getConversionRate(): number {
    if (!this.isEnabled) return 0
    const { page_view, email_signup_completions } = this.conversionFunnel
    return page_view > 0 ? (email_signup_completions / page_view) * 100 : 0
  }

  getFunnelData(): ConversionFunnelData {
    if (!this.isEnabled) {
      return {
        page_view: 0,
        hero_video_engagement: 0,
        faq_interactions: 0,
        email_signup_attempts: 0,
        email_signup_completions: 0
      }
    }
    return { ...this.conversionFunnel }
  }

  // Private helper methods
  private updateConversionFunnel(event: string) {
    if (event in this.conversionFunnel) {
      this.conversionFunnel[event as keyof ConversionFunnelData]++
    }
  }

  private getUTMParameter(param: string): string | null {
    if (typeof window === 'undefined') return null
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get(param)
  }

  private categorizeFAQQuestion(question: string): string {
    if (question.toLowerCase().includes('accurate') || question.toLowerCase().includes('accuracy')) {
      return 'technical_capability'
    }
    if (question.toLowerCase().includes('noise') || question.toLowerCase().includes('background')) {
      return 'usage_conditions'
    }
    if (question.toLowerCase().includes('language') || question.toLowerCase().includes('framework')) {
      return 'compatibility'
    }
    if (question.toLowerCase().includes('privacy') || question.toLowerCase().includes('security')) {
      return 'trust_security'
    }
    if (question.toLowerCase().includes('pricing') || question.toLowerCase().includes('cost')) {
      return 'pricing_business'
    }
    return 'general'
  }

  private getSocialProofCount(): number {
    // Get current waitlist count from social proof component
    return 127 // This should be dynamic based on your actual count
  }
}

// Global analytics instance
export const analytics = new Analytics()

// Initialize on client side
if (typeof window !== 'undefined') {
  analytics.init()
}

// TypeScript declarations
declare global {
  interface Window {
    posthog?: any
  }
}

// Hook for using analytics in components
import { useEffect } from 'react'

export function useAnalytics() {
  useEffect(() => {
    analytics.init()
  }, [])

  return {
    track: analytics.track.bind(analytics),
    trackPageView: analytics.trackPageView.bind(analytics),
    trackHeroVideoPlay: analytics.trackHeroVideoPlay.bind(analytics),
    trackHeroVideoComplete: analytics.trackHeroVideoComplete.bind(analytics),
    trackFAQExpand: analytics.trackFAQExpand.bind(analytics),
    trackEmailSignupAttempt: analytics.trackEmailSignupAttempt.bind(analytics),
    trackEmailSignupComplete: analytics.trackEmailSignupComplete.bind(analytics),
    trackSocialProofClick: analytics.trackSocialProofClick.bind(analytics),
    trackBenefitHover: analytics.trackBenefitHover.bind(analytics),
    trackCTAClick: analytics.trackCTAClick.bind(analytics),
    getConversionRate: analytics.getConversionRate.bind(analytics),
    getFunnelData: analytics.getFunnelData.bind(analytics)
  }
}