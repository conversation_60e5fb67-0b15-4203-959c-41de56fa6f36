import { createClient } from '@supabase/supabase-js'

// Database types for TypeScript support
export interface WaitlistEntry {
  id?: string
  email: string
  metadata?: {
    source?: string
    timestamp?: string
    user_agent?: string
    referrer?: string
  }
  created_at?: string
}

// Supabase client instance
let supabase: ReturnType<typeof createClient> | null = null

export function getSupabaseClient() {
  if (!supabase) {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
    const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY
    
    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase URL and Anon Key must be provided in environment variables')
    }
    
    supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    })
  }
  
  return supabase
}

// Supabase-specific waitlist operations
export const supabaseService = {
  async addToWaitlist(entry: WaitlistEntry): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const client = getSupabaseClient()
      
      const payload = {
        email: entry.email,
        metadata: entry.metadata || {}
      }

      const { data, error } = await client
        .from('waitlist')
        .insert([payload])
        .select()
      
      if (error) {
        // Handle duplicate email error (PostgreSQL unique violation)
        if (error.code === '23505') {
          return { success: false, error: 'Email already exists in waitlist' }
        }
        
        // Handle RLS policy violation
        if (error.code === '42501') {
          return { 
            success: false, 
            error: `RLS Policy Error: ${error.message || 'Permission denied'}. Hint: ${error.hint || 'Check your database RLS policies'}` 
          }
        }
        
        return { success: false, error: error.message || `Database error (${error.code})` }
      }

      return { success: true, data }
    } catch (error) {
      console.error('Supabase service error:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }
}