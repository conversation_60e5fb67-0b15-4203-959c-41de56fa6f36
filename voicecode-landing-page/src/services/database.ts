import { supabaseService } from '@/lib/supabase'

// Database backend types
export interface DatabaseResponse {
  success: boolean
  error?: string
  data?: any
}

export interface WaitlistData {
  email: string
  website?: string // Honeypot field
  metadata?: {
    source?: string
    timestamp?: string
    user_agent?: string
    referrer?: string
  }
}

// Get the database backend to use from environment
const DATABASE_BACKEND = import.meta.env.VITE_DATABASE_BACKEND || '1'
const CURRENT_BACKEND_URL = import.meta.env.VITE_CURRENT_BACKEND_URL || 'http://localhost:9100'

// Current backend service (original implementation)
const currentBackendService = {
  async addToWaitlist(data: WaitlistData): Promise<DatabaseResponse> {
    try {
      const response = await fetch(`${CURRENT_BACKEND_URL}/api/waitlist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        const responseData = await response.json()
        return { success: true, data: responseData }
      } else if (response.status === 429) {
        return { success: false, error: "Too many attempts. Please try again later." }
      } else if (response.status === 400) {
        const errorData = await response.json()
        return { success: false, error: errorData.detail || "Invalid email address" }
      } else {
        const errorData = await response.json()
        console.error('Current backend signup failed:', errorData)
        return { success: false, error: "Something went wrong. Please try again." }
      }
    } catch (error) {
      console.error('Network error during waitlist signup:', error)
      return { success: false, error: "Network error. Please check your connection and try again." }
    }
  }
}

// Database abstraction layer
export const databaseService = {
  async addToWaitlist(data: WaitlistData): Promise<DatabaseResponse> {
    // Check for honeypot field
    if (data.website && data.website.trim() !== '') {
      return { success: false, error: "Invalid request" }
    }

    // Route to appropriate backend based on environment variable
    if (DATABASE_BACKEND === '2') {
      // Use Supabase
      const supabaseEntry = {
        email: data.email,
        metadata: data.metadata
      }
      return await supabaseService.addToWaitlist(supabaseEntry)
    } else {
      // Use current backend (default)
      return await currentBackendService.addToWaitlist(data)
    }
  }
}

export default databaseService