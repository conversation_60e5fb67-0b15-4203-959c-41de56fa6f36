# Supabase Setup for VoiceCode Landing Page

## Database Schema

Create the following table in your Supabase project:

```sql
-- Create waitlist table
CREATE TABLE waitlist (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on email for faster lookups
CREATE INDEX idx_waitlist_email ON waitlist(email);

-- Create index on created_at for sorting
CREATE INDEX idx_waitlist_created_at ON waitlist(created_at);

-- First, ensure the anon role has proper permissions
GRANT USAGE ON SCHEMA public TO anon;
GRANT INSERT ON public.waitlist TO anon;

-- Enable RLS for secure anonymous submissions
ALTER TABLE public.waitlist ENABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies to start completely fresh
DO $$ 
BEGIN
    -- Drop all policies on the waitlist table
    FOR policy_rec IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'waitlist' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_rec.policyname) || ' ON public.waitlist';
    END LOOP;
END $$;

-- Create a simple, permissive policy for inserts
CREATE POLICY "waitlist_insert_policy" 
ON public.waitlist 
FOR INSERT 
WITH CHECK (true);

-- Create policy to allow authenticated users to read waitlist (admin access)
CREATE POLICY "waitlist_select_policy" 
ON public.waitlist 
FOR SELECT 
TO authenticated
USING (true);
```

## Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
# Set to 2 to use Supabase backend
VITE_DATABASE_BACKEND=2

# Add your Supabase project details
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

## Vercel Deployment Environment Variables

When deploying to Vercel, add these environment variables in your project settings:

- `VITE_DATABASE_BACKEND` = `2`
- `VITE_SUPABASE_URL` = Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` = Your Supabase anonymous key

## How to Get Supabase Credentials

1. Go to [supabase.com](https://supabase.com)
2. Create a new project or select an existing one
3. Go to Settings → API
4. Copy the Project URL and anon/public key
5. Run the SQL schema above in the SQL Editor

## Testing the Setup

1. Set `VITE_DATABASE_BACKEND=2` in your `.env.local`
2. Add your Supabase credentials
3. Run the development server: `pnpm dev`
4. Test the waitlist signup form
5. Check your Supabase dashboard to see the new entries

## Backend Switching

The application supports switching between backends via the `VITE_DATABASE_BACKEND` environment variable:

- `VITE_DATABASE_BACKEND=1` (default): Uses current backend at localhost:9100
- `VITE_DATABASE_BACKEND=2`: Uses Supabase backend

This allows for seamless migration and testing without code changes.

## Troubleshooting

### "new row violates row-level security policy" Error

If you get this error, it means RLS is enabled but the policy isn't allowing inserts. Solutions:

1. **Quick fix**: Disable RLS entirely for the waitlist table:
   ```sql
   ALTER TABLE waitlist DISABLE ROW LEVEL SECURITY;
   ```

2. **Secure fix**: Use the correct RLS policy from the main setup above:
   ```sql
   -- Enable RLS
   ALTER TABLE public.waitlist ENABLE ROW LEVEL SECURITY;
   
   -- Drop all existing policies
   DROP POLICY IF EXISTS "Allow publics to insert to Waitlist" ON public.waitlist;
   DROP POLICY IF EXISTS "Allow public inserts on waitlist" ON public.waitlist;
   DROP POLICY IF EXISTS "allow_all_inserts" ON public.waitlist;
   
   -- Create the correct policy for anonymous inserts
   CREATE POLICY "anonymous_can_insert_waitlist" 
   ON public.waitlist 
   FOR INSERT 
   TO anon, authenticated
   WITH CHECK (true);
   ```

3. **Check your table permissions** in Supabase Dashboard → Authentication → Policies

### Complete Debugging Steps

If you're still getting the RLS error after running the policy SQL above, follow these steps in order:

**Step 1: Check if the table exists and has correct schema**
```sql
-- Check table structure
\d waitlist

-- Or use this query:
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'waitlist' AND table_schema = 'public';
```

**Step 2: Check current RLS status and policies**
```sql
-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'waitlist';

-- Check ALL existing policies (this might show conflicting policies)
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
FROM pg_policies 
WHERE tablename = 'waitlist';

-- Check for any restrictive policies that might be blocking
SELECT * FROM pg_policies WHERE tablename = 'waitlist' AND permissive = 'RESTRICTIVE';
```

**Step 3: Reset RLS with proper anonymous policy**
```sql
-- Drop ALL existing policies first
DROP POLICY IF EXISTS "Allow publics to insert to Waitlist" ON public.waitlist;
DROP POLICY IF EXISTS "Allow public inserts on waitlist" ON public.waitlist;
DROP POLICY IF EXISTS "Allow authenticated reads on waitlist" ON public.waitlist;
DROP POLICY IF EXISTS "allow_all_inserts" ON public.waitlist;

-- Enable RLS
ALTER TABLE public.waitlist ENABLE ROW LEVEL SECURITY;

-- Create the correct policy for anonymous users
CREATE POLICY "anonymous_can_insert_waitlist" 
ON public.waitlist 
FOR INSERT 
TO anon, authenticated
WITH CHECK (true);

-- Test insert manually
INSERT INTO public.waitlist (email, metadata) 
VALUES ('<EMAIL>', '{"source": "manual_test"}');

-- If successful, add read policy for authenticated users
CREATE POLICY "authenticated_can_read_waitlist" 
ON public.waitlist 
FOR SELECT 
TO authenticated
USING (true);
```

**Step 4: Nuclear option - Table recreation**
```sql
-- Drop the table and recreate (WARNING: This deletes all data!)
DROP TABLE IF EXISTS public.waitlist CASCADE;

-- Recreate with exact schema
CREATE TABLE public.waitlist (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Start with RLS disabled
ALTER TABLE public.waitlist DISABLE ROW LEVEL SECURITY;
```

**Step 5: Test via API**
After completing the policy reset above, test your application again with detailed console logging.