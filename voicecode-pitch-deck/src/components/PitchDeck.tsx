import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './ui/button'
import { Progress } from './ui/progress'

// Import slide components
import HeroSlide from './slides/HeroSlide'
import ProblemSlide from './slides/ProblemSlide'
import MarketOpportunitySlide from './slides/MarketOpportunitySlide'
import SolutionSlide from './slides/SolutionSlide'
import CompetitiveSlide from './slides/CompetitiveSlide'
import ProductDemoSlide from './slides/ProductDemoSlide'
import TechnologySlide from './slides/TechnologySlide'
import GoToMarketSlide from './slides/GoToMarketSlide'
import BusinessModelSlide from './slides/BusinessModelSlide'
import TractionSlide from './slides/TractionSlide'
import TeamSlide from './slides/TeamSlide'
import InvestmentSlide from './slides/InvestmentSlide'

const slides = [
  { id: 'hero', title: 'VoiceCode', component: HeroSlide },
  { id: 'problem', title: 'The Problem', component: ProblemSlide },
  { id: 'market', title: 'Market Opportunity', component: MarketOpportunitySlide },
  { id: 'solution', title: 'Our Solution', component: SolutionSlide },
  { id: 'competitive', title: 'Competitive Landscape', component: CompetitiveSlide },
  { id: 'demo', title: 'Product Demo', component: ProductDemoSlide },
  { id: 'technology', title: 'Technology Stack', component: TechnologySlide },
  { id: 'gtm', title: 'Go-to-Market', component: GoToMarketSlide },
  { id: 'business', title: 'Business Model', component: BusinessModelSlide },
  { id: 'traction', title: 'Traction & Validation', component: TractionSlide },
  { id: 'team', title: 'Team & Vision', component: TeamSlide },
  { id: 'investment', title: 'Investment Ask', component: InvestmentSlide },
]

export default function PitchDeck() {
  const [currentSlide, setCurrentSlide] = useState(0)
  
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }
  
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }
  
  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'ArrowRight' || event.key === ' ') {
        nextSlide()
      } else if (event.key === 'ArrowLeft') {
        prevSlide()
      } else if (event.key >= '1' && event.key <= '9') {
        const slideIndex = parseInt(event.key) - 1
        if (slideIndex < slides.length) {
          goToSlide(slideIndex)
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  const CurrentSlideComponent = slides[currentSlide].component
  const progress = ((currentSlide + 1) / slides.length) * 100

  return (
    <div className="min-h-screen bg-background text-foreground overflow-hidden">
      {/* Header with navigation */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-card/80 backdrop-blur-sm border-b border-border">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-3">
            <img src="/voicecode-transparent.png" alt="VoiceCode" className="w-8 h-8" />
            <span className="text-xl font-bold text-foreground">VoiceCode</span>
          </div>
          
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">
              {currentSlide + 1} / {slides.length}
            </span>
            <Progress value={progress} className="w-32" />
          </div>
        </div>
      </header>

      {/* Main slide content */}
      <main className="pt-20 pb-16">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="h-full"
          >
            <CurrentSlideComponent />
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Navigation controls */}
      <footer className="fixed bottom-0 left-0 right-0 z-50 bg-card/80 backdrop-blur-sm border-t border-border">
        <div className="flex items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={prevSlide}
            disabled={currentSlide === 0}
            className="text-foreground hover:bg-accent"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          {/* Slide dots */}
          <div className="flex gap-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentSlide
                    ? 'bg-primary w-8'
                    : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                }`}
              />
            ))}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={nextSlide}
            disabled={currentSlide === slides.length - 1}
            className="text-foreground hover:bg-accent"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </footer>

      {/* Keyboard shortcuts hint */}
      <div className="fixed top-24 right-6 text-xs text-muted-foreground bg-card/80 px-3 py-2 rounded-lg backdrop-blur-sm border border-border">
        <div>← → arrows or space to navigate</div>
        <div>1-9 to jump to slides</div>
      </div>
    </div>
  )
}