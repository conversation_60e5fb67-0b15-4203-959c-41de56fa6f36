import { motion } from 'framer-motion'
import { Smartphone, X, Clock, Zap } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function ProblemSlide() {
  const problems = [
    {
      icon: Smartphone,
      title: "Complex Mobile GitHub Operations",
      description: "Traditional mobile GitHub clients require dozens of taps for simple operations like creating branches or reviewing PRs",
      stat: "14M+ daily GitHub users"
    },
    {
      icon: Clock,
      title: "Slow Mobile Development Workflow",
      description: "Developers spend 70% of their time on mobile devices but struggle with inefficient development tools",
      stat: "70% mobile time usage"
    },
    {
      icon: X,
      title: "No Voice Interface Solution",
      description: "GitHub discontinued Copilot Voice in April 2024, leaving a massive gap in voice-controlled development tools",
      stat: "Market gap created"
    }
  ]

  return (
    <SlideLayout 
      title="The Mobile Development Gap"
      subtitle="Developers are trapped between mobile-first lifestyles and desktop-bound development tools"
    >
      <div className="space-y-12">
        {/* Problem cards */}
        <div className="grid md:grid-cols-3 gap-8">
          {problems.map((problem, index) => (
            <motion.div
              key={problem.title}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className="bg-card border-border hover:bg-accent/50 transition-all duration-300 h-full">
                <CardContent className="p-6 text-center space-y-4">
                  <div className="mx-auto w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                    <problem.icon className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground">{problem.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{problem.description}</p>
                  <Badge variant="destructive">
                    {problem.stat}
                  </Badge>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Pain point scenarios */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-card rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            Current Developer Pain Points
          </h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-primary flex items-center gap-2">
                <Smartphone className="w-5 h-5" />
                Mobile Reality
              </h4>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start gap-3">
                  <X className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                  <span>15+ taps to create a simple pull request</span>
                </li>
                <li className="flex items-start gap-3">
                  <X className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                  <span>No voice interface for complex operations</span>
                </li>
                <li className="flex items-start gap-3">
                  <X className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                  <span>Limited code editing capabilities</span>
                </li>
                <li className="flex items-start gap-3">
                  <X className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                  <span>Context switching between apps</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-primary flex items-center gap-2">
                <Zap className="w-5 h-5" />
                What Developers Want
              </h4>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start gap-3">
                  <Zap className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>Natural voice commands for GitHub operations</span>
                </li>
                <li className="flex items-start gap-3">
                  <Zap className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>AI-powered code assistance on mobile</span>
                </li>
                <li className="flex items-start gap-3">
                  <Zap className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>Seamless mobile-first development workflow</span>
                </li>
                <li className="flex items-start gap-3">
                  <Zap className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>Secure execution in isolated environments</span>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Market impact */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="text-center bg-card rounded-xl p-8 border border-border"
        >
          <h3 className="text-3xl font-bold text-foreground mb-4">
            The Opportunity
          </h3>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            With GitHub's Copilot Voice discontinued and no mobile-first voice coding solutions available, 
            there's a <span className="text-primary font-semibold">massive market gap</span> waiting to be filled 
            by the right solution.
          </p>
        </motion.div>
      </div>
    </SlideLayout>
  )
}