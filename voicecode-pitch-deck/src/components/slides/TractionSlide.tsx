import { motion } from 'framer-motion'
import { CheckCircle, Target, TrendingUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Progress } from '../ui/progress'
import SlideLayout from '../SlideLayout'

export default function TractionSlide() {
  const validationItems = [
    { item: "Technical Architecture Design", status: "complete", progress: 100 },
    { item: "Competitive Market Research", status: "complete", progress: 100 },
    { item: "Claude Code CLI Integration POC", status: "complete", progress: 100 },
    { item: "Daytona Sandbox Implementation", status: "complete", progress: 100 },
    { item: "Mobile App MVP Development", status: "complete", progress: 100 }
  ]

  return (
    <SlideLayout 
      title="Traction & Validation"
      subtitle="Strong foundation with clear technical validation roadmap"
    >
      <div className="space-y-12">
        <div className="grid md:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="bg-muted/50 border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center gap-2">
                  <Target className="w-5 h-5 text-primary" />
                  Technical Validation Progress
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {validationItems.map((item, index) => (
                  <motion.div
                    key={item.item}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-sm">{item.item}</span>
                      {item.status === 'complete' && (
                        <CheckCircle className="w-4 h-4 " />
                      )}
                    </div>
                    <Progress value={item.progress} className="h-2" />
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-muted/50 border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 " />
                  Market Validation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold ">100M+</div>
                  <div className="text-muted-foreground">GitHub Developers</div>
                </div>
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary">$80B+</div>
                  <div className="text-muted-foreground">Combined TAM by 2030</div>
                </div>
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary">0</div>
                  <div className="text-muted-foreground">Direct Competitors</div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center bg-muted rounded-xl p-8 border border-primary"
        >
          <h3 className="text-2xl font-bold text-foreground mb-4">Next Milestones</h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="text-primary font-semibold">Q3 2025</div>
              <div className="text-muted-foreground">MVP Launch & Beta Testing</div>
            </div>
            <div className="space-y-2">
              <div className="text-primary font-semibold">Q4 2025</div>
              <div className="text-muted-foreground">1000+ Active Users</div>
            </div>
            <div className="space-y-2">
              <div className="text-primary font-semibold">Q1 2026</div>
              <div className="text-muted-foreground">Enterprise Partnerships</div>
            </div>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}