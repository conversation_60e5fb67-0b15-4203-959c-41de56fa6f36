import { motion } from 'framer-motion'
import { TrendingUp, Users, DollarSign, Zap } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function MarketOpportunitySlide() {
  const marketData = [
    {
      title: "AI Code Tools Market",
      current: "$4.86B",
      future: "$26.03B",
      year: "2030",
      cagr: "27.1%"
    },
    {
      title: "Voice Recognition Market", 
      current: "$20.25B",
      future: "$53.67B", 
      year: "2030",
      cagr: "14.6%"
    },
    {
      title: "Mobile App Market",
      current: "$476B",
      future: "Growing",
      year: "2024",
      cagr: "Stable"
    }
  ]

  const developerStats = [
    { label: "GitHub Developers Worldwide", value: "100M+", icon: Users },
    { label: "GitHub Mobile Daily Visitors", value: "14M", icon: TrendingUp },
    { label: "Voice Search Daily Usage", value: "40%", icon: Zap },
    { label: "AI Tool Productivity Boost", value: "88%", icon: DollarSign }
  ]

  return (
    <SlideLayout 
      title="Massive Growth Convergence"
      subtitle="Three rapidly expanding markets converging to create unprecedented opportunity"
    >
      <div className="space-y-12">
        {/* Market size cards */}
        <div className="grid md:grid-cols-3 gap-8">
          {marketData.map((market, index) => (
            <motion.div
              key={market.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className="bg-card border-border hover:bg-accent/50 transition-all duration-300 h-full">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 mx-auto rounded-full bg-primary flex items-center justify-center mb-4">
                    <TrendingUp className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <CardTitle className="text-lg text-foreground">{market.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <div className="space-y-2">
                    <div className="text-3xl font-bold text-foreground">
                      {market.current}
                      <span className="text-lg text-muted-foreground ml-2">(2023)</span>
                    </div>
                    <div className="text-4xl font-bold text-primary">
                      {market.future}
                    </div>
                    <div className="text-sm text-muted-foreground">by {market.year}</div>
                  </div>
                  <Badge className="bg-primary text-primary-foreground">
                    {market.cagr} CAGR
                  </Badge>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Developer statistics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-card rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            Developer Market Validation
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {developerStats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1 + index * 0.1 }}
                className="text-center space-y-3"
              >
                <div className="w-12 h-12 mx-auto bg-primary/20 rounded-full flex items-center justify-center">
                  <stat.icon className="w-6 h-6 text-primary" />
                </div>
                <div className="text-3xl font-bold text-foreground">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Market convergence visualization */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            VoiceCode's Sweet Spot
          </h3>
          
          <div className="flex justify-center">
            {/* Three overlapping circles representing market convergence */}
            <div className="relative h-64 w-80">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 1.4 }}
                className="w-32 h-32 rounded-full bg-primary/20 border-2 border-primary flex items-center justify-center absolute top-8 left-8"
              >
                <span className="text-xs font-medium text-foreground text-center">AI Coding<br/>Tools</span>
              </motion.div>
              
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 1.6 }}
                className="w-32 h-32 rounded-full bg-secondary/20 border-2 border-secondary flex items-center justify-center absolute top-8 right-8"
              >
                <span className="text-xs font-medium text-foreground text-center">Voice<br/>Recognition</span>
              </motion.div>
              
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 1.8 }}
                className="w-32 h-32 rounded-full bg-accent/20 border-2 border-accent flex items-center justify-center absolute bottom-8 left-1/2 transform -translate-x-1/2"
              >
                <span className="text-xs font-medium text-foreground text-center">Mobile<br/>Development</span>
              </motion.div>

              {/* Center intersection */}
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 2.2 }}
                className="w-20 h-20 rounded-full bg-primary flex items-center justify-center absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 border-4 border-background"
              >
                <span className="text-xs font-bold text-foreground text-center">VoiceCode</span>
              </motion.div>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 2.4 }}
            className="text-center mt-12"
          >
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              VoiceCode sits at the <span className="text-primary font-semibold">intersection</span> of three 
              rapidly growing markets, creating a unique <span className="text-primary font-semibold">blue ocean opportunity</span> 
              with minimal direct competition.
            </p>
          </motion.div>
        </motion.div>

        {/* Total addressable market */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2.6 }}
          className="text-center bg-muted rounded-xl p-8 border border-destructive"
        >
          <h3 className="text-3xl font-bold text-foreground mb-4">
            Total Addressable Market
          </h3>
          <div className="text-6xl font-bold text-primary mb-4">
            $80B+
          </div>
          <p className="text-xl text-muted-foreground">
            Combined market size by 2030 across AI coding tools, voice recognition, and mobile development
          </p>
        </motion.div>
      </div>
    </SlideLayout>
  )
}