import { motion } from 'framer-motion'
import { <PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function CompetitiveSlide() {
  const competitors = [
    {
      name: "Serenade.ai",
      type: "Direct Competitor",
      strengths: ["Voice coding", "IDE integration", "Speech recognition"],
      weaknesses: ["Desktop only", "No mobile support", "No GitHub focus"],
      status: "Active",
      color: "border-primary bg-primary/10"
    },
    {
      name: "GitHub Copilot Voice", 
      type: "Direct Competitor",
      strengths: ["GitHub integration", "AI assistance", "Official support"],
      weaknesses: ["Discontinued April 2024", "Desktop only", "Limited scope"],
      status: "Discontinued",
      color: "border-muted-foreground bg-muted/50"
    },
    {
      name: "GitHub Mobile",
      type: "Indirect Competitor", 
      strengths: ["Official app", "14M users", "Full features"],
      weaknesses: ["No voice interface", "Touch only", "Complex operations"],
      status: "Active",
      color: "border-accent bg-accent/10"
    },
    {
      name: "Working Copy",
      type: "Indirect Competitor",
      strengths: ["iOS premium", "Local repos", "Full Git support"],
      weaknesses: ["iOS only", "No voice", "Traditional UI"],
      status: "Active", 
      color: "border-accent bg-accent/10"
    }
  ]

  return (
    <SlideLayout 
      title="Blue Ocean Positioning"
      subtitle="No existing solution combines voice interfaces, mobile optimization, and AI-powered GitHub operations"
    >
      <div className="space-y-12">
        {/* Competitive landscape grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {competitors.map((competitor, index) => (
            <motion.div
              key={competitor.name}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className={`${competitor.color} border transition-all duration-300 hover:scale-105`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-foreground text-lg">{competitor.name}</CardTitle>
                    <Badge variant={competitor.status === 'Discontinued' ? 'secondary' : 'default'} 
                           className={competitor.status === 'Discontinued' ? 'bg-muted text-muted-foreground' : 'bg-primary/20 text-primary'}>
                      {competitor.status}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground text-sm">{competitor.type}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Strengths */}
                  <div>
                    <h4 className=" font-medium mb-2 flex items-center gap-2">
                      <Check className="w-4 h-4" />
                      Strengths
                    </h4>
                    <ul className="space-y-1">
                      {competitor.strengths.map((strength, i) => (
                        <li key={i} className="text-muted-foreground text-sm flex items-center gap-2">
                          <div className="w-1 h-1 bg-accent rounded-full" />
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Weaknesses */}
                  <div>
                    <h4 className="text-destructive font-medium mb-2 flex items-center gap-2">
                      <X className="w-4 h-4" />
                      Limitations
                    </h4>
                    <ul className="space-y-1">
                      {competitor.weaknesses.map((weakness, i) => (
                        <li key={i} className="text-muted-foreground text-sm flex items-center gap-2">
                          <div className="w-1 h-1 bg-primary rounded-full" />
                          {weakness}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Positioning map */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            Competitive Positioning Map
          </h3>
          
          <div className="relative bg-muted rounded-lg p-8 h-96">
            {/* Axes labels */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-muted-foreground text-sm">
              Mobile Optimization →
            </div>
            <div className="absolute top-1/2 left-4 transform -translate-y-1/2 -rotate-90 text-muted-foreground text-sm">
              Voice Interface ↑
            </div>
            
            {/* Grid lines */}
            <div className="absolute inset-8 border-l border-b border-border"></div>
            
            {/* Competitor positions */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1.2 }}
              className="absolute bottom-12 left-12 w-3 h-3 bg-primary rounded-full"
              title="Serenade.ai"
            />
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1.4 }}
              className="absolute bottom-12 left-16 w-3 h-3 bg-primary rounded-full opacity-50"
              title="GitHub Copilot Voice (Discontinued)"
            />
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1.6 }}
              className="absolute bottom-16 right-16 w-3 h-3 bg-primary rounded-full"
              title="GitHub Mobile"
            />
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1.8 }}
              className="absolute bottom-20 right-20 w-3 h-3 bg-secondary rounded-full"
              title="Working Copy"
            />
            
            {/* VoiceCode position - top right quadrant */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, delay: 2 }}
              className="absolute top-12 right-12 w-6 h-6 bg-primary rounded-full flex items-center justify-center border-2 border-background"
            >
              <Star className="w-3 h-3 text-foreground" />
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 2.2 }}
              className="absolute top-8 right-8 text-foreground font-semibold text-sm"
            >
              VoiceCode
            </motion.div>
          </div>
        </motion.div>

        {/* Key insights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2.4 }}
          className="text-center bg-muted rounded-xl p-8 border border-accent"
        >
          <h3 className="text-3xl font-bold text-foreground mb-4">
            Market Opportunity
          </h3>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-6">
            VoiceCode occupies a unique position with <span className="text-primary font-semibold">high mobile optimization</span> and 
            <span className="text-primary font-semibold"> advanced voice interface</span> - a combination no competitor offers.
          </p>
          <div className="flex justify-center gap-4 flex-wrap">
            <Badge className="bg-primary/20 text-primary border-primary px-4 py-2">
              First-Mover Advantage
            </Badge>
            <Badge className="bg-primary/20 text-primary border-primary px-4 py-2">
              Blue Ocean Market
            </Badge>
            <Badge className="bg-accent/20  border-accent px-4 py-2">
              High Barriers to Entry
            </Badge>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}