import { motion } from 'framer-motion'
import { DollarSign, Users, Building } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function BusinessModelSlide() {
  const pricingTiers = [
    {
      name: "Free",
      price: "$0",
      features: ["Basic voice commands", "5 operations/day", "Community support"],
      target: "Individual developers",
      icon: Users
    },
    {
      name: "Pro", 
      price: "$9.99",
      period: "/month",
      features: ["Unlimited operations", "Advanced AI features", "Priority support"],
      target: "Professional developers",
      icon: DollarSign,
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      features: ["Team management", "SSO integration", "Dedicated support"],
      target: "Organizations", 
      icon: Building
    }
  ]

  return (
    <SlideLayout 
      title="Business Model"
      subtitle="Sustainable revenue streams with clear value proposition"
    >
      <div className="space-y-12">
        <div className="grid md:grid-cols-3 gap-8">
          {pricingTiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className={`${tier.popular ? 'border-primary bg-primary/20' : 'bg-muted/50 border-border'} hover:bg-muted/70 transition-all duration-300 h-full relative`}>
                {tier.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-foreground">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center mb-4">
                    <tier.icon className="w-8 h-8 text-foreground" />
                  </div>
                  <CardTitle className="text-foreground text-2xl">{tier.name}</CardTitle>
                  <div className="text-4xl font-bold text-primary">
                    {tier.price}
                    {tier.period && <span className="text-lg text-muted-foreground">{tier.period}</span>}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground text-center text-sm">{tier.target}</p>
                  <ul className="space-y-2">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="text-muted-foreground text-sm flex items-center gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center bg-muted rounded-xl p-8 border border-accent"
        >
          <h3 className="text-2xl font-bold text-foreground mb-4">Revenue Projections</h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <div className="text-3xl font-bold ">$2M</div>
              <div className="text-muted-foreground">Year 1 ARR</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">$10M</div>
              <div className="text-muted-foreground">Year 2 ARR</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary">$50M</div>
              <div className="text-muted-foreground">Year 3 ARR</div>
            </div>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}