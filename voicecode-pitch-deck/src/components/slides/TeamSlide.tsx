import { motion } from 'framer-motion'
import { User, Target, Lightbulb } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import SlideLayout from '../SlideLayout'

export default function TeamSlide() {
  const vision = [
    "Transform mobile development through voice interfaces",
    "Bridge the gap between AI coding tools and mobile workflows", 
    "Create the most intuitive developer experience on mobile",
    "Enable coding accessibility for all developers"
  ]

  return (
    <SlideLayout 
      title="Team & Vision"
      subtitle="Building the future of voice-controlled development"
    >
      <div className="space-y-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground flex items-center justify-center gap-3">
            <Target className="w-6 h-6 text-primary" />
            Our Vision
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {vision.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                className="flex items-start gap-3"
              >
                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Lightbulb className="w-3 h-3 text-foreground" />
                </div>
                <span className="text-muted-foreground">{item}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-muted/50 border-border max-w-md mx-auto">
            <CardContent className="p-8">
              <div className="w-24 h-24 mx-auto bg-primary rounded-full flex items-center justify-center mb-6">
                <User className="w-12 h-12 text-foreground" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">Founding Team</h3>
              <p className="text-muted-foreground mb-4">
                Experienced developers passionate about revolutionizing mobile development workflows
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• 15+ years combined development experience</div>
                <div>• Background in AI/ML and mobile technologies</div>
                <div>• Previous startup and enterprise experience</div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center bg-muted/50 rounded-xl p-8 border border-secondary"
        >
          <h3 className="text-3xl font-bold text-foreground mb-4">
            Join Our Mission
          </h3>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Help us create a world where developers can code naturally, anywhere, anytime, 
            through the power of voice and AI.
          </p>
        </motion.div>
      </div>
    </SlideLayout>
  )
}