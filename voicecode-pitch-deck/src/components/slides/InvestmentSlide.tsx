import { motion } from 'framer-motion'
import { DollarSign, TrendingUp, Zap, Mail } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import SlideLayout from '../SlideLayout'

export default function InvestmentSlide() {
  const fundingBreakdown = [
    { category: "Product Development", amount: "$800K", percentage: 40 },
    { category: "Team Expansion", amount: "$600K", percentage: 30 },
    { category: "Marketing & Sales", amount: "$400K", percentage: 20 },
    { category: "Operations & Legal", amount: "$200K", percentage: 10 }
  ]

  const milestones = [
    { milestone: "MVP Launch", timeline: "6 months", funding: "$500K" },
    { milestone: "1000+ Users", timeline: "12 months", funding: "$1M" },
    { milestone: "Enterprise Ready", timeline: "18 months", funding: "$2M" }
  ]

  return (
    <SlideLayout 
      title="Investment Ask"
      subtitle="Join the revolution in voice-controlled development"
      centered
    >
      <div className="space-y-12">
        {/* Funding ask */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="text-center bg-muted rounded-2xl p-12 border border-primary"
        >
          <div className="text-8xl font-bold text-primary mb-4">
            $2M
          </div>
          <h3 className="text-3xl font-semibold text-foreground mb-6">Seed Round</h3>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            To accelerate development, expand our team, and capture the blue ocean opportunity 
            in voice-controlled mobile development
          </p>
        </motion.div>

        {/* Funding breakdown and milestones */}
        <div className="grid lg:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-muted/50 border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center gap-2">
                  <DollarSign className="w-5 h-5 " />
                  Funding Allocation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {fundingBreakdown.map((item, index) => (
                  <motion.div
                    key={item.category}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <div className="text-foreground font-medium">{item.category}</div>
                      <div className="text-muted-foreground text-sm">{item.percentage}% of total</div>
                    </div>
                    <div className="text-primary font-bold text-lg">{item.amount}</div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-muted/50 border-border">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  Key Milestones
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {milestones.map((item, index) => (
                  <motion.div
                    key={item.milestone}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                    className="border-l-2 border-primary pl-4 space-y-1"
                  >
                    <div className="text-foreground font-medium">{item.milestone}</div>
                    <div className="text-muted-foreground text-sm">{item.timeline}</div>
                    <div className="text-primary text-sm font-medium">{item.funding} deployed</div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Why invest */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            Why Invest in VoiceCode Now?
          </h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary/20 rounded-full flex items-center justify-center">
                <Zap className="w-8 h-8 text-primary" />
              </div>
              <h4 className="text-lg font-medium text-foreground">First-Mover Advantage</h4>
              <p className="text-muted-foreground text-sm">
                Enter an uncontested market before big tech catches up
              </p>
            </div>
            
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-secondary/20 rounded-full flex items-center justify-center">
                <TrendingUp className="w-8 h-8 text-primary" />
              </div>
              <h4 className="text-lg font-medium text-foreground">Massive Growth Market</h4>
              <p className="text-muted-foreground text-sm">
                $80B+ combined TAM with 27%+ annual growth rates
              </p>
            </div>
            
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-accent/20 rounded-full flex items-center justify-center">
                <DollarSign className="w-8 h-8 " />
              </div>
              <h4 className="text-lg font-medium text-foreground">Clear Revenue Model</h4>
              <p className="text-muted-foreground text-sm">
                Proven SaaS model with enterprise upsell potential
              </p>
            </div>
          </div>
        </motion.div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="text-center bg-muted rounded-xl p-8 border border-accent"
        >
          <h3 className="text-3xl font-bold text-foreground mb-6">
            Ready to Transform Mobile Development?
          </h3>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-foreground px-8 py-6 text-lg font-semibold rounded-xl"
            >
              <Mail className="w-5 h-5 mr-2" />
              Schedule a Meeting
            </Button>
            <div className="text-muted-foreground">
              <div className="font-medium">Contact:<EMAIL></div>
              <div className="text-sm">Let's build the future together</div>
            </div>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}