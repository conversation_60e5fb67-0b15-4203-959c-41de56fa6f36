import { motion } from 'framer-motion'
import { Brain, Smartphone, Shield, Zap, Database, Cloud } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function TechnologySlide() {
  const techStack = [
    {
      category: "Mobile App",
      icon: Smartphone,
      technologies: ["React Native", "TypeScript", "Capacitor"],
      color: "bg-primary"
    },
    {
      category: "AI Integration", 
      icon: Brain,
      technologies: ["Claude Code CLI", "Anthropic API", "Natural Language Processing"],
      color: "bg-secondary"
    },
    {
      category: "Backend Services",
      icon: Database,
      technologies: ["Node.js", "Express", "PostgreSQL", "Redis"],
      color: "bg-accent"
    },
    {
      category: "Sandbox Environment",
      icon: Shield,
      technologies: ["Daytona", "Docker", "Secure Isolation"],
      color: "bg-primary"
    },
    {
      category: "Voice Processing",
      icon: Zap,
      technologies: ["Speech Recognition", "Audio Processing", "Command Parsing"],
      color: "bg-primary"
    },
    {
      category: "Cloud Infrastructure",
      icon: Cloud,
      technologies: ["OAuth 2.0", "GitHub Apps", "Real-time Sync"],
      color: "bg-primary"
    }
  ]

  return (
    <SlideLayout 
      title="Technology Stack"
      subtitle="Built for excellence with enterprise-grade architecture and cutting-edge AI integration"
    >
      <div className="space-y-12">
        {/* Tech stack grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {techStack.map((tech, index) => (
            <motion.div
              key={tech.category}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="bg-muted/50 border-border hover:bg-muted/70 transition-all duration-300 h-full">
                <CardHeader className="text-center">
                  <div className={`w-16 h-16 mx-auto rounded-full ${tech.color} flex items-center justify-center mb-4 shadow-lg`}>
                    <tech.icon className="w-8 h-8 text-foreground" />
                  </div>
                  <CardTitle className="text-foreground text-lg">{tech.category}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {tech.technologies.map((technology, i) => (
                    <Badge key={i} variant="secondary" className="mr-2 mb-2 bg-muted text-muted-foreground">
                      {technology}
                    </Badge>
                  ))}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Architecture diagram */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-muted rounded-xl p-8 border border-primary"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            System Architecture
          </h3>
          
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8 text-center">
            <div className="flex-1 space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center">
                <Smartphone className="w-8 h-8 text-primary-foreground" />
              </div>
              <h4 className="text-lg font-semibold text-foreground">Mobile App</h4>
              <p className="text-muted-foreground text-sm">React Native with voice recognition</p>
            </div>
            
            <div className="flex-1 space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center">
                <Brain className="w-8 h-8 text-primary-foreground" />
              </div>
              <h4 className="text-lg font-semibold text-foreground">Claude Code CLI</h4>
              <p className="text-muted-foreground text-sm">AI-powered command processing</p>
            </div>
            
            <div className="flex-1 space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center">
                <Shield className="w-8 h-8 text-primary-foreground" />
              </div>
              <h4 className="text-lg font-semibold text-foreground">Daytona Sandbox</h4>
              <p className="text-muted-foreground text-sm">Secure execution environment</p>
            </div>
          </div>
        </motion.div>

        {/* Key technical features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            Technical Highlights
          </h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-primary">Performance & Security</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Sub-3 second voice processing</li>
                <li>• End-to-end encryption</li>
                <li>• Sandboxed execution environment</li>
                <li>• Real-time log streaming</li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-primary">AI & Integration</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Advanced NLP for dev terminology</li>
                <li>• GitHub Apps deep integration</li>
                <li>• Claude Code CLI authentication bridge</li>
                <li>• Cross-platform compatibility</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}