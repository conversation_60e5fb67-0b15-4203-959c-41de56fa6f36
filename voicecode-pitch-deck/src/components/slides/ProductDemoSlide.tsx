import { motion } from 'framer-motion'
import { Mic, Play, Code, CheckCircle } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import SlideLayout from '../SlideLayout'

export default function ProductDemoSlide() {
  const demoSteps = [
    "Create a new branch called 'feature-auth'",
    "Add authentication middleware to the API",
    "Create a pull request with detailed description",
    "Merge the PR after review approval"
  ]

  return (
    <SlideLayout 
      title="See VoiceCode in Action"
      subtitle="Experience the power of voice-controlled GitHub operations"
    >
      <div className="space-y-12">
        {/* Demo mockup */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="bg-muted rounded-2xl p-8 border border-border shadow-2xl max-w-4xl mx-auto"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-primary rounded-full"></div>
              <div className="w-3 h-3 bg-primary rounded-full"></div>
              <div className="w-3 h-3 bg-accent rounded-full"></div>
            </div>
            <div className="text-muted-foreground text-sm">VoiceCode Demo</div>
          </div>
          
          <div className="space-y-6">
            {/* Voice input simulation */}
            <div className="bg-primary/30 rounded-lg p-4 border border-primary">
              <div className="flex items-center gap-3 mb-3">
                <Mic className="w-5 h-5 text-primary" />
                <span className="text-primary font-medium">Voice Input</span>
              </div>
              <p className="text-muted-foreground italic">
                "Create a new branch called feature-user-authentication from main"
              </p>
            </div>
            
            {/* Processing */}
            <div className="bg-secondary/30 rounded-lg p-4 border border-secondary">
              <div className="flex items-center gap-3 mb-3">
                <Code className="w-5 h-5 text-primary" />
                <span className="text-primary font-medium">Claude Code CLI Processing</span>
              </div>
              <div className="font-mono text-sm text-muted-foreground space-y-1">
                <div>✓ Parsing voice command...</div>
                <div>✓ Validating repository access...</div>
                <div>✓ Generating git command: git checkout -b feature-user-authentication</div>
              </div>
            </div>
            
            {/* Result */}
            <div className="bg-accent/30 rounded-lg p-4 border border-accent">
              <div className="flex items-center gap-3 mb-3">
                <CheckCircle className="w-5 h-5 " />
                <span className=" font-medium">Execution Result</span>
              </div>
              <div className="font-mono text-sm text-muted-foreground">
                Branch 'feature-user-authentication' created successfully ✓
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* Demo scenarios */}
        <div className="grid md:grid-cols-2 gap-8">
          {demoSteps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
            >
              <Card className="bg-muted/50 border-border hover:bg-muted/70 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-foreground font-bold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="text-foreground font-medium mb-2">Voice Command</h3>
                      <p className="text-muted-foreground italic">"{step}"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
        
        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.4 }}
          className="text-center"
        >
          <Button size="lg" className="bg-primary hover:bg-primary/90 text-foreground px-8 py-6 text-lg font-semibold rounded-xl">
            <Play className="w-5 h-5 mr-2" />
            Request Live Demo
          </Button>
        </motion.div>
      </div>
    </SlideLayout>
  )
}