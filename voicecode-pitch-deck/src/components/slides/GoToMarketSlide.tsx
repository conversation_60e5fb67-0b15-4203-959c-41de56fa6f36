import { motion } from 'framer-motion'
import { Users, Building, Zap } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import SlideLayout from '../SlideLayout'

export default function GoToMarketSlide() {
  const markets = [
    {
      title: "Primary Market",
      subtitle: "Individual Developers",
      description: "AI-savvy developers seeking mobile productivity",
      size: "50M+ developers",
      icon: Users,
      color: "bg-primary"
    },
    {
      title: "Secondary Market", 
      subtitle: "Development Teams",
      description: "Remote-first teams adopting mobile workflows",
      size: "10M+ teams",
      icon: Zap,
      color: "bg-secondary"
    },
    {
      title: "Enterprise Market",
      subtitle: "Organizations",
      description: "Companies needing accessible dev tools",
      size: "100K+ orgs",
      icon: Building,
      color: "bg-accent"
    }
  ]

  return (
    <SlideLayout 
      title="Go-to-Market Strategy"
      subtitle="Three-tier approach targeting the expanding developer ecosystem"
    >
      <div className="grid md:grid-cols-3 gap-8">
        {markets.map((market, index) => (
          <motion.div
            key={market.title}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
          >
            <Card className="bg-muted/50 border-border hover:bg-muted/70 transition-all duration-300 h-full">
              <CardHeader className="text-center">
                <div className={`w-16 h-16 mx-auto rounded-full ${market.color} flex items-center justify-center mb-4`}>
                  <market.icon className="w-8 h-8 text-foreground" />
                </div>
                <CardTitle className="text-foreground">{market.title}</CardTitle>
                <p className="text-primary font-medium">{market.subtitle}</p>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-muted-foreground">{market.description}</p>
                <div className="text-2xl font-bold text-foreground">{market.size}</div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </SlideLayout>
  )
}