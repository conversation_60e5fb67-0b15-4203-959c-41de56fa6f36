import { motion } from 'framer-motion'
import { Mic, Smartphone, Brain, Shield, Zap, Code, Github } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import SlideLayout from '../SlideLayout'

export default function SolutionSlide() {
  const coreFeatures = [
    {
      icon: Mic,
      title: "Advanced Voice Recognition",
      description: "Natural speech-to-code optimized for development terminology"
    },
    {
      icon: Smartphone,
      title: "Mobile-First Design", 
      description: "Purpose-built for mobile devices with optimized interfaces"
    },
    {
      icon: Brain,
      title: "Claude Code CLI Integration",
      description: "Powered by <PERSON><PERSON><PERSON>'s sophisticated AI coding assistant"
    },
    {
      icon: Shield,
      title: "Sandboxed Execution",
      description: "Secure command execution in isolated Daytona environments"
    }
  ]

  const keyDifferentiators = [
    "First mobile app with voice-controlled GitHub operations",
    "Deep Claude Code CLI integration for AI-powered assistance", 
    "Secure sandboxed execution environment",
    "Natural language processing for development terminology",
    "Cross-platform React Native architecture"
  ]

  return (
    <SlideLayout 
      title="VoiceCode: Three Technologies, One Revolution"
      subtitle="The world's first mobile app that bridges voice commands with GitHub operations through AI-powered assistance"
    >
      <div className="space-y-12">
        {/* Core features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {coreFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.15 }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <Card className="bg-muted/50 border-border hover:bg-muted/70 transition-all duration-300 h-full">
                <CardContent className="p-6 text-center space-y-4">
                  <div className="w-16 h-16 mx-auto rounded-full bg-primary flex items-center justify-center shadow-lg">
                    <feature.icon className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">{feature.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* How it works */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-muted rounded-xl p-8 border border-primary"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            How VoiceCode Works
          </h3>
          
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
            {[
              { 
                step: "1", 
                title: "Voice Command", 
                description: "Speak naturally: 'Create a new branch called feature-auth'",
                icon: Mic,
                color: "bg-primary"
              },
              { 
                step: "2", 
                title: "AI Processing", 
                description: "Claude Code CLI interprets and validates the command",
                icon: Brain,
                color: "bg-secondary"
              },
              { 
                step: "3", 
                title: "Secure Execution", 
                description: "Command executed safely in Daytona sandbox environment",
                icon: Shield,
                color: "bg-accent"
              },
              { 
                step: "4", 
                title: "GitHub Integration", 
                description: "Changes synchronized with your GitHub repository",
                icon: Github,
                color: "bg-primary"
              }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 1 + index * 0.2 }}
                className="flex flex-col items-center text-center max-w-xs"
              >
                <div className={`w-16 h-16 ${step.color} rounded-full flex items-center justify-center mb-4 shadow-lg`}>
                  <step.icon className="w-8 h-8 text-foreground" />
                </div>
                <div className="text-sm font-semibold text-primary mb-2">STEP {step.step}</div>
                <h4 className="text-lg font-semibold text-foreground mb-2">{step.title}</h4>
                <p className="text-muted-foreground text-sm">{step.description}</p>
                {index < 3 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3, delay: 1.5 + index * 0.2 }}
                    className="hidden lg:block absolute transform translate-x-32"
                  >
                    <Zap className="w-6 h-6 text-primary" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Key differentiators */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.4 }}
          className="bg-muted/50 rounded-xl p-8 border border-border"
        >
          <h3 className="text-2xl font-semibold text-center mb-8 text-foreground">
            What Makes VoiceCode Unique
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {keyDifferentiators.map((differentiator, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 1.6 + index * 0.1 }}
                className="flex items-start gap-3"
              >
                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Code className="w-3 h-3 text-foreground" />
                </div>
                <span className="text-muted-foreground">{differentiator}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Value proposition */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2 }}
          className="text-center bg-muted rounded-xl p-8 border border-accent"
        >
          <h3 className="text-3xl font-bold text-foreground mb-4">
            The Result
          </h3>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-6">
            Developers can now perform complex GitHub operations with simple voice commands, 
            dramatically reducing the friction between mobile lifestyles and professional development workflows.
          </p>
          <div className="flex justify-center gap-4 flex-wrap">
            <Badge className="bg-primary/20 text-primary border-primary px-4 py-2">
              10x Faster Operations
            </Badge>
            <Badge className="bg-primary/20 text-primary border-primary px-4 py-2">
              Mobile-First Experience
            </Badge>
            <Badge className="bg-accent/20  border-accent px-4 py-2">
              AI-Powered Intelligence
            </Badge>
          </div>
        </motion.div>
      </div>
    </SlideLayout>
  )
}