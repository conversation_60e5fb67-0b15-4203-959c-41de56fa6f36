import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Code, Smartphone, Github } from 'lucide-react'
import { Button } from '../ui/button'
import SlideLayout from '../SlideLayout'

export default function HeroSlide() {
  return (
    <SlideLayout centered>
      <div className="text-center space-y-8 md:space-y-12 px-4">
        {/* Animated logo and title */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative"
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 mb-6">
            <motion.div
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="p-3 sm:p-4 bg-primary rounded-full shadow-2xl"
            >
              <Mic className="w-8 h-8 sm:w-12 sm:h-12 text-primary-foreground" />
            </motion.div>
            <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-9xl font-bold text-primary">
              VoiceCode
            </h1>
          </div>
          
          {/* Animated voice waves */}
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 flex gap-1">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="w-1 bg-primary rounded-full"
                animate={{
                  height: [4, 20, 4],
                  opacity: [0.4, 1, 0.4]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        </motion.div>

        {/* Tagline */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold mb-4 text-foreground">
            Code with Your Voice.
            <br />
            <span className="text-primary">Ship from Anywhere.</span>
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            The first mobile app that bridges voice commands with GitHub operations 
            through Claude Code CLI in secure sandboxed environments.
          </p>
        </motion.div>

        {/* Feature icons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="flex flex-wrap justify-center gap-4 sm:gap-6 md:gap-8 lg:gap-16"
        >
          {[
            { icon: Mic, label: 'Voice Control' },
            { icon: Smartphone, label: 'Mobile First' },
            { icon: Code, label: 'AI Powered' },
            { icon: Github, label: 'GitHub Native' }
          ].map((feature, _index) => (
            <motion.div
              key={feature.label}
              whileHover={{ scale: 1.1, y: -5 }}
              className="flex flex-col items-center gap-3"
            >
              <div className="p-3 sm:p-4 bg-primary rounded-xl shadow-lg">
                <feature.icon className="w-6 h-6 sm:w-8 sm:h-8 text-primary-foreground" />
              </div>
              <span className="text-xs sm:text-sm md:text-base text-muted-foreground font-medium">
                {feature.label}
              </span>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <Button 
            size="lg" 
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 sm:px-6 md:px-8 py-4 sm:py-6 text-sm sm:text-base md:text-lg font-semibold rounded-xl shadow-2xl transform transition-all duration-200 hover:scale-105 max-w-sm sm:max-w-none"
          >
            <span className="hidden sm:inline">Experience the Future of Mobile Development</span>
            <span className="sm:hidden">Start Building with Voice</span>
          </Button>
        </motion.div>

        {/* Subtitle */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.4 }}
          className="text-muted-foreground text-base sm:text-lg"
        >
          Join the revolution in voice-controlled development
        </motion.p>
      </div>
    </SlideLayout>
  )
}