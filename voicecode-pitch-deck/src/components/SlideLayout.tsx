import { type ReactNode } from 'react'
import { motion } from 'framer-motion'

interface SlideLayoutProps {
  title?: string
  subtitle?: string
  children: ReactNode
  className?: string
  centered?: boolean
}

export default function SlideLayout({ 
  title, 
  subtitle, 
  children, 
  className = '',
  centered = false 
}: SlideLayoutProps) {
  return (
    <div className={`min-h-[calc(100vh-140px)] px-8 py-12 ${centered ? 'flex flex-col justify-center' : ''} ${className}`}>
      <div className="max-w-7xl mx-auto">
        {title && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-4 text-primary">
              {title}
            </h1>
            {subtitle && (
              <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto">
                {subtitle}
              </p>
            )}
          </motion.div>
        )}
        
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {children}
        </motion.div>
      </div>
    </div>
  )
}