{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "serena": {"command": "/Users/<USER>/.local/bin/uv", "args": ["run", "--directory", "/Users/<USER>/Documents/personal/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/Users/<USER>/Documents/personal/sandboxed"]}}}