# VoiceCode Monorepo

This monorepo contains the entire VoiceCode project, including the frontend Progressive Web App (PWA), the backend API, and the public-facing landing page.

## High-Level Architecture

VoiceCode is a comprehensive platform that enables developers to code using voice commands. It integrates with GitHub for repository management and uses Daytona to provide secure, sandboxed development environments.

The project is structured as a monorepo and consists of the following main components:

1.  **`voicecode-fe-1`**: A React-based Progressive Web App (PWA) that serves as the main user interface for the application.
2.  **`voicecode/fastapi-app`**: A Python FastAPI application that provides the backend services, including GitHub integration and sandbox management.
3.  **`voicecode-landing-page`**: A separate React application that serves as the public-facing landing page to attract users and build a waitlist.

## Technology Stack

| Category           | Technology                               |
| ------------------ | ---------------------------------------- |
| Monorepo Management| pnpm, Turbo                              |
| Frontend           | React, Vite, TypeScript, Tailwind CSS    |
| Backend            | Python, FastAPI                          |
| Database           | PostgreSQL                               |
| Caching            | Redis                                    |
| Containerization   | Docker                                   |

## Prerequisites

Before you begin, ensure you have the following installed:

*   [Node.js](https://nodejs.org/) (v18 or later)
*   [pnpm](https://pnpm.io/)
*   [Docker](https://www.docker.com/) and Docker Compose
*   [Python](https://www.python.org/) (v3.9 or later)

## Getting Started

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd voicecode-monorepo
    ```

2.  **Install dependencies:**
    ```bash
    pnpm install
    ```

3.  **Set up environment variables:**

    Each package contains a `.env.example` file. Copy these to `.env` (or `.env.local` for frontend packages) and fill in the required values.

    *   `cp .env.example .env`
    *   `cd voicecode/fastapi-app && cp .env.example .env`
    *   `cd ../../voicecode-fe-1 && cp .env.example .env.local`
    *   `cd ../voicecode-landing-page && cp .env.example .env.local`

4.  **Start the development environment:**

    This project uses Docker Compose to run the backend services (API, PostgreSQL, Redis).

    ```bash
    # Start backend services in detached mode
    docker-compose up -d
    ```

    Once the backend services are running, you can start the frontend applications.

    ```bash
    # In the root directory, run the development server for all apps
    pnpm dev
    ```

    This will start the following:
    *   **Landing Page**: `http://localhost:5173`
    *   **PWA Frontend**: (Check terminal for port, usually `http://localhost:5174` or similar)
    *   **Backend API**: `http://localhost:9100`

## Available Scripts

The root `package.json` contains several scripts to manage the monorepo, orchestrated by Turbo.

*   `pnpm dev`: Start all applications in development mode.
*   `pnpm build`: Build all applications for production.
*   `pnpm lint`: Lint all code in the monorepo.
*   `pnpm test`: Run all tests.
*   `pnpm clean`: Clean up `dist` and `node_modules` directories.
*   `pnpm format`: Format all code.

Refer to the `README.md` file in each package for more specific details and instructions.