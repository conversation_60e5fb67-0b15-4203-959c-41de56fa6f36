version: '3.8'

services:
  voicecode-api:
    build:
      context: ./voicecode/fastapi-app
      dockerfile: Dockerfile.dev
    container_name: voicecode-api-dev
    ports:
      - "${API_HOST_PORT:-9100}:${UVICORN_PORT:-9100}"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    environment:
      - ENVIRONMENT=development
    volumes:
      - ./voicecode/fastapi-app:/app
      - /app/__pycache__
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${UVICORN_PORT:-9100}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - voicecode-network
    command: uvicorn main:app --host 0.0.0.0 --port ${UVICORN_PORT:-9100} --reload

  postgres:
    image: postgres:${POSTGRES_VERSION:-15-alpine}
    container_name: voicecode-postgres-dev
    ports:
      - "${POSTGRES_HOST_PORT:-5432}:5432"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    environment:
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data-dev:/var/lib/postgresql/data
      - ./voicecode/fastapi-app/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-voicecode} -d ${POSTGRES_DB:-voicecode}"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - voicecode-network

  redis:
    image: redis:${REDIS_VERSION:-6.2-alpine}
    container_name: voicecode-redis-dev
    ports:
      - "${REDIS_HOST_PORT:-6379}:6379"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    volumes:
      - redis-data-dev:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - voicecode-network

  voicecode-landing-page:
    build:
      context: ./voicecode-landing-page
      dockerfile: Dockerfile
    container_name: voicecode-landing-page-dev
    ports:
      - "${LANDING_PAGE_HOST_PORT:-9101}:${VITE_PORT:-9101}"
    volumes:
      - ./voicecode-landing-page:/app
    environment:
      - NODE_ENV=development
      - VITE_PORT=9101
    restart: unless-stopped
    networks:
      - voicecode-network

networks:
  voicecode-network:
    driver: bridge
    name: voicecode-network-dev

volumes:
  postgres-data-dev:
    driver: local
  redis-data-dev:
    driver: local