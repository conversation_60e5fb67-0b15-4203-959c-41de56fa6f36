# Redis configuration for VoiceCode development environment

# Save the DB on disk
save 900 1
save 300 10
save 60 10000

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Enable AOF persistence for better durability
appendonly yes
appendfsync everysec

# Network settings
bind 0.0.0.0
port 6379

# Security (development settings)
protected-mode no

# Logging
loglevel notice
logfile ""

# Performance tuning for development
tcp-keepalive 300
timeout 0