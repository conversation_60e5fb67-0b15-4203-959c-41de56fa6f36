# VoiceCode Task Streaming Enhancement Architecture

## Introduction

This document outlines the architectural approach for enhancing VoiceCode with real-time task execution and log streaming capabilities. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development of new features while ensuring seamless integration with the existing system.

**Relationship to Existing Architecture:**
This document supplements existing project architecture by defining how new streaming components will integrate with current systems. Where conflicts arise between new and existing patterns, this document provides guidance on maintaining consistency while implementing enhancements.

### Existing Project Analysis

#### Current Project State
- **Primary Purpose:** Voice-controlled GitHub repository interaction via Claude Code CLI in sandboxes
- **Current Tech Stack:** FastAPI (Python 3.11+), React + Vite, PostgreSQL, Redis, Daytona SDK
- **Architecture Style:** Service-oriented with clear separation of concerns
- **Deployment Method:** Containerized services with Docker

#### Available Documentation
- Brownfield architecture analysis (docs/brownfield-architecture.md)
- Task streaming implementation plan (docs/task-streaming-plan.md)
- Chat implementation documentation
- API endpoint documentation

#### Identified Constraints
- Must maintain Vercel AI SDK patterns for state management
- Cannot break existing chat functionality
- One task execution at a time per sandbox (resource constraint)
- JWT authentication must be maintained
- Existing API response format must be preserved

### Change Log

| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|---------|
| Initial | 2025-01-31 | 1.0 | Initial architecture for task streaming | Architect |

## Enhancement Scope and Integration Strategy

### Enhancement Overview
**Enhancement Type:** New Feature Addition with Major Modifications
**Scope:** Add real-time streaming infrastructure while maintaining existing chat system
**Integration Impact:** Significant - new async patterns, database changes, frontend state updates

### Integration Approach
**Code Integration Strategy:** New services layer for task management, minimal changes to existing endpoints
**Database Integration:** Additive approach - new Task table with foreign key to ChatMessage
**API Integration:** New streaming endpoint alongside existing REST endpoints
**UI Integration:** Enhanced chat components with backward compatibility

### Compatibility Requirements
- **Existing API Compatibility:** All current endpoints remain unchanged
- **Database Schema Compatibility:** No modifications to existing tables, only additions
- **UI/UX Consistency:** Task messages render within existing chat flow
- **Performance Impact:** Streaming must not degrade existing response times

## Tech Stack Alignment

### Existing Technology Stack

| Category | Current Technology | Version | Usage in Enhancement | Notes |
|----------|-------------------|---------|---------------------|-------|
| Backend Framework | FastAPI | 0.116.1 | Streaming endpoint via SSE | Built-in streaming support |
| Frontend Framework | React + Vite | Latest | UI components and hooks | No changes needed |
| State Management | Vercel AI SDK | 4.3.19 | Handle streaming messages | Already supports streaming |
| Database | PostgreSQL | Latest | Store task history | Add Task table |
| Cache | Redis | 6.2.0 | Track active tasks | Existing patterns |
| Sandbox Runtime | Daytona SDK | 0.22.0 | Log streaming source | Use async methods |
| Authentication | PyJWT | Latest | Secure streaming endpoint | Existing middleware |

### New Technology Additions

No new technologies required - leveraging existing stack capabilities.

## Data Models and Schema Changes

### New Data Models

#### Task
**Purpose:** Track command execution state, history, and logs
**Integration:** Foreign key relationship with ChatMessage for message tracking

**Key Attributes:**
- `id`: UUID - Primary key
- `sandbox_id`: UUID - Links to sandbox
- `user_id`: UUID - Links to user
- `command`: Text - Command being executed
- `status`: Enum - pending/running/completed/failed/cancelled
- `started_at`: DateTime - Execution start time
- `completed_at`: DateTime - Execution end time
- `exit_code`: Integer - Command exit code
- `execution_time`: Float - Total execution seconds
- `logs`: JSON - Array of log entries
- `task_metadata`: JSON - Additional metadata

**Relationships:**
- **With Existing:** Many-to-one with ChatMessage (optional)
- **With New:** None

### Schema Integration Strategy

**Database Changes Required:**
- **New Tables:** tasks
- **Modified Tables:** None (ChatMessage unchanged)
- **New Indexes:** sandbox_id, user_id, status, created_at
- **Migration Strategy:** Alembic migration with zero downtime

**Backward Compatibility:**
- Existing chat_messages table remains untouched
- Task tracking is optional - chat can work without tasks
- Foreign key is nullable to support non-task messages

## Component Architecture

### New Components

#### TaskService
**Responsibility:** Manage task lifecycle, state transitions, and database operations
**Integration Points:** Database layer, DaytonaService, Redis for state

**Key Interfaces:**
- `create_task(sandbox_id, user_id, command) -> Task`
- `get_current_task(sandbox_id, user_id) -> Optional[Task]`
- `cancel_task(task_id, user_id) -> bool`
- `execute_task_stream(task) -> AsyncGenerator[TaskEvent]`

**Dependencies:**
- **Existing Components:** Database session, Redis client
- **New Components:** Enhanced DaytonaService

**Technology Stack:** Python async/await, SQLAlchemy, asyncio

#### Enhanced DaytonaService
**Responsibility:** Abstract Daytona SDK streaming capabilities for task execution
**Integration Points:** Daytona SDK, TaskService

**Key Interfaces:**
- `execute_command_stream(sandbox_id, command) -> AsyncGenerator[Dict]`
- Session management for streaming
- Log aggregation and formatting

**Dependencies:**
- **Existing Components:** Daytona SDK client
- **New Components:** None

**Technology Stack:** Python async, Daytona SDK async methods

#### TaskMessage Component
**Responsibility:** Display real-time task execution status and logs in UI
**Integration Points:** Chat UI, useVoiceCodeChat hook

**Key Interfaces:**
- Props: `task`, `isLive`, `onCancel`
- Real-time log updates via React state
- Auto-scroll for new logs

**Dependencies:**
- **Existing Components:** UI component library, theme system
- **New Components:** None

**Technology Stack:** React, TypeScript, Tailwind CSS

### Component Interaction Diagram

```mermaid
graph TB
    subgraph Frontend
        UI[Chat UI]
        Hook[useVoiceCodeChat]
        TM[TaskMessage]
        AISDK[Vercel AI SDK]
    end
    
    subgraph Backend
        API[Streaming Endpoint]
        TS[TaskService]
        DS[DaytonaService]
        DB[(PostgreSQL)]
        Redis[(Redis)]
    end
    
    subgraph External
        Daytona[Daytona Sandbox]
    end
    
    UI --> Hook
    Hook --> AISDK
    AISDK --> API
    API --> TS
    TS --> DS
    TS --> DB
    TS --> Redis
    DS --> Daytona
    
    Daytona -.->|Log Stream| DS
    DS -.->|Events| TS
    TS -.->|SSE| API
    API -.->|Stream| AISDK
    AISDK -.->|Updates| Hook
    Hook --> TM
    TM --> UI
```

## API Design and Integration

### API Integration Strategy
**API Integration Strategy:** New streaming endpoint with SSE, maintains REST patterns
**Authentication:** Existing JWT middleware applies to streaming endpoint
**Versioning:** No versioning needed - additive change only

### New API Endpoints

#### Stream Task Execution
- **Method:** POST
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/stream`
- **Purpose:** Execute command with real-time log streaming
- **Integration:** Uses existing auth, new SSE response format

##### Request
```json
{
  "command": "npm test",
  "metadata": {
    "source": "chat",
    "context": {}
  }
}
```

##### Response
```text
data: {"id": "msg-1", "role": "assistant", "content": "Executing: npm test", "metadata": {"type": "task_start", "task_id": "task-123"}}

data: {"id": "msg-2", "role": "assistant", "content": "Running tests...", "metadata": {"type": "task_log", "task_id": "task-123"}}

data: {"id": "msg-3", "role": "assistant", "content": "Task completed (exit code: 0)", "metadata": {"type": "task_complete", "task_id": "task-123", "exit_code": 0}}

data: [DONE]
```

#### Get Current Task
- **Method:** GET
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/current`
- **Purpose:** Check if task is running in sandbox
- **Integration:** Standard REST response format

##### Request
No body required

##### Response
```json
{
  "data": {
    "id": "task-123",
    "status": "running",
    "command": "npm test",
    "started_at": "2025-01-31T10:00:00Z"
  },
  "message": "Current task retrieved"
}
```

#### Cancel Task
- **Method:** POST
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/{task_id}/cancel`
- **Purpose:** Cancel running task
- **Integration:** Standard REST response format

##### Request
No body required

##### Response
```json
{
  "data": {
    "success": true
  },
  "message": "Task cancelled successfully"
}
```

## Source Tree Integration

### Existing Project Structure
```plaintext
voicecode/
├── fastapi-app/
│   ├── main.py
│   ├── database.py
│   └── alembic/
└── scripts/
voicecode-fe-1/
├── src/
│   ├── hooks/
│   ├── lib/
│   ├── services/
│   └── components/
```

### New File Organization
```plaintext
voicecode/
├── fastapi-app/
│   ├── services/               # New service layer
│   │   ├── __init__.py
│   │   ├── task_service.py     # Task management logic
│   │   └── daytona_service.py  # Enhanced Daytona integration
│   ├── models/                 # New models directory
│   │   ├── __init__.py
│   │   └── task_models.py      # Pydantic models for tasks
│   └── alembic/versions/       # Existing migrations
│       └── 003_add_tasks_table.py  # New migration

voicecode-fe-1/
├── src/
│   ├── components/            # Existing components
│   │   └── TaskMessage.tsx    # New task display component
│   └── types/                 # Existing types
│       └── task.types.ts      # New task type definitions
```

### Integration Guidelines
- **File Naming:** Follow existing patterns - snake_case (Python), camelCase (TypeScript)
- **Folder Organization:** Services in dedicated directory, maintain flat structure
- **Import/Export Patterns:** Named exports for services, default exports for components

## Infrastructure and Deployment Integration

### Existing Infrastructure
**Current Deployment:** Docker containers with docker-compose
**Infrastructure Tools:** Docker, PostgreSQL, Redis
**Environments:** Development, Staging, Production

### Enhancement Deployment Strategy
**Deployment Approach:** Standard deployment with database migration first
**Infrastructure Changes:** None - uses existing infrastructure
**Pipeline Integration:** Add migration step before service deployment

### Rollback Strategy
**Rollback Method:** Revert to previous container version, migration is safe
**Risk Mitigation:** Feature flag optional, graceful degradation
**Monitoring:** Log streaming metrics, task completion rates

## Coding Standards and Conventions

### Existing Standards Compliance
**Code Style:** Black (Python), ESLint + Prettier (TypeScript)
**Linting Rules:** Strict type checking, no-any rule
**Testing Patterns:** pytest for backend, Jest for frontend
**Documentation Style:** Docstrings (Python), JSDoc (TypeScript)

### Critical Integration Rules
- **Existing API Compatibility:** No changes to existing endpoints
- **Database Integration:** Only additive changes, no schema modifications
- **Error Handling:** Use existing HTTPException patterns
- **Logging Consistency:** Use existing logger configuration

## Testing Strategy

### Integration with Existing Tests
**Existing Test Framework:** pytest (backend), Jest (frontend)
**Test Organization:** Tests alongside code files
**Coverage Requirements:** Maintain 80%+ coverage

### New Testing Requirements

#### Unit Tests for New Components
- **Framework:** pytest, Jest (following existing)
- **Location:** `services/test_*.py`, `*.test.ts`
- **Coverage Target:** 80%+ for new code
- **Integration with Existing:** Run with existing test suite

#### Integration Tests
- **Scope:** Task creation, streaming, cancellation
- **Existing System Verification:** Chat continues working during tasks
- **New Feature Testing:** End-to-end streaming flow

#### Regression Testing
- **Existing Feature Verification:** All chat tests must pass
- **Automated Regression Suite:** Add to CI/CD pipeline
- **Manual Testing Requirements:** Test on actual mobile devices

## Security Integration

### Existing Security Measures
**Authentication:** JWT tokens with HTTPBearer
**Authorization:** Sandbox ownership verification
**Data Protection:** HTTPS only, encrypted database
**Security Tools:** Security headers, rate limiting

### Enhancement Security Requirements
**New Security Measures:** Stream authentication, connection limits
**Integration Points:** Streaming endpoint uses same auth middleware
**Compliance Requirements:** No PII in logs, audit trail for tasks

### Security Testing
**Existing Security Tests:** Auth middleware tests continue
**New Security Test Requirements:** Test streaming auth, rate limits
**Penetration Testing:** Include streaming endpoint in next pentest

## Next Steps

### Story Manager Handoff
Reference this architecture document for task streaming enhancement implementation. Key integration requirements:
- Maintain existing chat functionality throughout
- Database changes are additive only
- Use existing authentication patterns
- Follow current API response formats
- Start with Story 1.1 (Database Schema) as foundation
- Each story must verify existing system integrity

### Developer Handoff
For implementation:
- Follow existing code patterns identified in architecture analysis
- Services go in new `services/` directory
- Use existing error handling and logging patterns
- Streaming format must be Vercel AI SDK compatible
- Test both streaming and existing chat after each change
- Database migration must be reversible