# Story 1.1: Database Schema and Models

**Epic**: Real-Time Task Streaming Enhancement
**Story ID**: RTTS-1.1
**Priority**: High (Foundation)
**Estimated Effort**: 3-5 story points
**Dependencies**: None (Foundation story)

## User Story

**As a** developer,
**I want to** create the Task model and database schema,
**so that** we can track task execution state and history.

## Business Value

This story establishes the foundational data layer for task streaming, enabling persistent tracking of command execution state, history, and metrics essential for the streaming feature.

## Acceptance Criteria

### AC1: Task Model Creation
- [ ] Task model created with all required fields:
  - `id`: UUID primary key
  - `sandbox_id`: UUID linking to sandbox
  - `user_id`: UUID linking to user
  - `command`: Text field for executed command
  - `status`: Enum (pending/running/completed/failed/cancelled)
  - `started_at`: DateTime execution start
  - `completed_at`: DateTime execution end
  - `exit_code`: Integer command exit code
  - `execution_time`: Float total execution seconds
  - `logs`: JSON array of log entries
  - `task_metadata`: JSON additional metadata
  - `created_at`: DateTime record creation
  - `updated_at`: DateTime record modification

### AC2: Database Migration
- [ ] Alembic migration created for Task table
- [ ] Migration includes appropriate indexes:
  - Index on `sandbox_id` for sandbox queries
  - Index on `user_id` for user queries
  - Index on `status` for filtering active tasks
  - Composite index on `(sandbox_id, status)` for active task checks
  - Index on `created_at` for chronological queries
- [ ] Migration tested in development environment
- [ ] Migration is reversible (down migration implemented)

### AC3: ChatMessage Relationship
- [ ] Optional foreign key relationship from Task to ChatMessage
- [ ] Relationship allows tracking which chat message initiated a task
- [ ] Foreign key is nullable to support non-chat-initiated tasks
- [ ] No modifications to existing ChatMessage table structure

### AC4: Model Validation
- [ ] Pydantic models created for API serialization
- [ ] Status enum properly defined with valid transitions
- [ ] Field validation rules implemented (required fields, data types)
- [ ] Model methods for common operations (duration calculation, etc.)

## Technical Implementation Tasks

### Database Layer
1. **Create SQLAlchemy Model** (2 hours)
   - Define Task model in `voicecode/fastapi-app/database.py`
   - Implement all required fields with proper types
   - Add table constraints and relationships

2. **Create Pydantic Models** (1 hour)
   - TaskCreate model for task creation
   - TaskUpdate model for task updates
   - TaskResponse model for API responses
   - TaskStatus enum definition

3. **Generate Migration** (1 hour)
   - Create Alembic migration: `003_add_tasks_table.py`
   - Include all indexes for performance
   - Test migration up and down

4. **Add Model Methods** (1 hour)
   - `calculate_execution_time()` method
   - `is_active()` property for running/pending tasks
   - `add_log_entry()` method for appending logs
   - Status transition validation

### Testing Tasks
1. **Unit Tests** (2 hours)
   - Test model creation and validation
   - Test field constraints and relationships
   - Test model methods and properties
   - Test Pydantic serialization/deserialization

2. **Migration Tests** (1 hour)
   - Test migration applies successfully
   - Test migration rollback works
   - Verify indexes are created correctly

## Integration Verification

### IV1: Existing ChatMessage Compatibility
- [ ] All existing ChatMessage queries execute without changes
- [ ] No performance impact on chat history retrieval
- [ ] Chat functionality works identically before/after migration

### IV2: Migration Safety
- [ ] Database migration completes without errors
- [ ] Migration can be reversed without data loss
- [ ] No downtime during migration (additive changes only)

### IV3: Performance Validation
- [ ] New indexes don't negatively impact existing operations
- [ ] Task queries perform within acceptable limits (<100ms)
- [ ] Database size increase is reasonable

## Definition of Done

- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests passing
- [ ] Migration tested in staging environment
- [ ] Documentation updated (API docs, database schema)
- [ ] No existing functionality broken

## Technical Notes

### Database Considerations
- Use UUID for all ID fields for better distribution
- JSON fields for logs and metadata provide flexibility
- Composite indexes optimize common query patterns
- Foreign key to ChatMessage is optional/nullable

### Performance Considerations
- Partition strategy may be needed for high-volume deployments
- Log cleanup policy should be implemented
- Index maintenance for large datasets

### Security Considerations
- Ensure user_id validation prevents unauthorized access
- Sanitize command field content for logging
- Consider PII implications in logs field

## Files Modified/Created

### New Files
- `voicecode/fastapi-app/models/task_models.py` - Pydantic models
- `voicecode/fastapi-app/models/__init__.py` - Models package init
- `voicecode/fastapi-app/alembic/versions/003_add_tasks_table.py` - Migration
- `voicecode/fastapi-app/tests/test_task_models.py` - Unit tests
- `voicecode/fastapi-app/tests/__init__.py` - Tests package init

### Modified Files
- `voicecode/fastapi-app/database.py` - Add Task SQLAlchemy model and TaskStatus enum

## Dev Agent Record

### Tasks Completed
- [x] Read and understand story 1.1 requirements  
- [x] Create SQLAlchemy Task model in database.py with all required fields
- [x] Create Pydantic models (TaskCreate, TaskUpdate, TaskResponse, TaskStatus enum)
- [x] Generate Alembic migration 003_add_tasks_table.py with indexes
- [x] Add model methods (calculate_execution_time, is_active, add_log_entry)
- [x] Create unit tests for Task models
- [x] Test migration up and down
- [x] Run full test suite to ensure no existing functionality broken

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514)

### Debug Log References
- Fixed enum serialization issue: SQLAlchemy was passing enum name instead of value
- Migration tested successfully: upgrade 002 -> 003 and downgrade 003 -> 002
- Task model integration test passed with all CRUD operations

### Completion Notes
- Task model implemented with all required fields from AC1
- Database migration includes all required indexes from AC2  
- Optional ChatMessage relationship implemented as specified in AC3
- Pydantic models created for API serialization with validation as per AC4
- All model methods implemented: calculate_execution_time(), is_active, add_log_entry()
- Comprehensive unit tests written covering all model functionality
- Migration tested both directions (up/down) successfully
- Integration test confirms database operations work correctly

### File List
- `voicecode/fastapi-app/database.py` - Modified to add Task SQLAlchemy model and TaskStatus enum
- `voicecode/fastapi-app/models/__init__.py` - New models package initialization
- `voicecode/fastapi-app/models/task_models.py` - New Pydantic models for Task API
- `voicecode/fastapi-app/alembic/versions/003_add_tasks_table.py` - New migration for tasks table
- `voicecode/fastapi-app/tests/__init__.py` - New tests package initialization  
- `voicecode/fastapi-app/tests/test_task_models.py` - New comprehensive unit tests

### Change Log
1. Added TaskStatus enum with string inheritance for proper database serialization
2. Created Task SQLAlchemy model with all required fields and relationships
3. Implemented model methods for execution time calculation and log management
4. Created comprehensive Pydantic models for API operations
5. Generated reversible Alembic migration with performance indexes
6. Created extensive unit test suite covering all model functionality
7. Verified migration works in both directions
8. Confirmed no existing functionality was broken

### Status
Ready for Review

## Next Story Dependencies

This story is a prerequisite for:
- Story 1.2: Task Management Service (requires Task model)
- Story 1.4: Streaming API Endpoint (requires Task persistence)

## Story Validation Checklist

- [ ] Task model covers all streaming requirements
- [ ] Database schema supports expected query patterns
- [ ] Migration path is safe for production deployment
- [ ] Integration with existing chat system is seamless
- [ ] Performance impact is acceptable
- [ ] Test coverage is comprehensive