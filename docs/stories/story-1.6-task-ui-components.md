# Story 1.6: Task UI Components

**Epic**: Real-Time Task Streaming Enhancement
**Story ID**: RTTS-1.6
**Priority**: High (User Interface)
**Estimated Effort**: 5-8 story points
**Dependencies**: Story 1.5 (Frontend Streaming Integration)

## User Story

**As a** user,
**I want to** see live task execution status in the chat interface,
**so that** I can monitor command progress and interact with running tasks.

## Business Value

This story creates the user-facing components that display real-time task execution, providing users with immediate feedback, progress visibility, and control over running commands within the familiar chat interface.

## Acceptance Criteria

### AC1: TaskMessage Component Implementation
- [ ] TaskMessage component displays command being executed
- [ ] Component shows current task status with visual indicators
- [ ] Real-time log output displayed in scrollable container
- [ ] Execution time counter updates during task execution
- [ ] Component integrates seamlessly within existing chat message flow

### AC2: Real-time Log Updates
- [ ] Log entries appear immediately as they arrive from backend
- [ ] Auto-scroll to latest log entry (with user override capability)
- [ ] Proper formatting for different log types (stdout, stderr, system)
- [ ] Long log lines handled gracefully (wrapping, truncation, or scrolling)
- [ ] Log timestamps displayed when available

### AC3: Status Indicators and Controls
- [ ] Visual status indicators for task states:
  - Pending: Loading spinner with "Preparing command..." message
  - Running: Progress indicator with "Executing..." message
  - Completed: Success checkmark with exit code and execution time
  - Failed: Error indicator with exit code and error details
  - Cancelled: Cancelled indicator with partial results
- [ ] Cancel button available for running tasks
- [ ] Retry button for failed tasks (if applicable)
- [ ] Copy command button for easy re-execution

### AC4: UI Integration and Responsiveness
- [ ] Component follows existing chat UI patterns and styling
- [ ] Responsive design works on mobile and desktop
- [ ] Accessibility features (screen reader support, keyboard navigation)
- [ ] Dark/light theme support matching existing design system
- [ ] Smooth animations for state transitions

## Technical Implementation Tasks

### Component Development
1. **Create TaskMessage Component** (4 hours)
   - Implement base component structure in `voicecode-fe-1/src/components/TaskMessage.tsx`
   - Set up proper TypeScript interfaces and props
   - Implement basic layout and styling
   - Add component to existing chat message rendering logic

2. **Implement Status Display** (2 hours)
   - Create status indicator components for each task state
   - Add appropriate icons and styling for visual feedback
   - Implement execution time counter with live updates
   - Add command display with proper formatting

3. **Add Log Display Container** (3 hours)
   - Create scrollable log container with auto-scroll functionality
   - Implement log entry formatting for different types
   - Add virtual scrolling for performance with long logs
   - Handle log content sanitization and formatting

4. **Implement Interactive Controls** (2 hours)
   - Add cancel button with confirmation dialog
   - Implement copy command functionality
   - Add retry mechanism for failed tasks
   - Create hover states and interactive feedback

### Styling and Design
1. **Apply Design System** (2 hours)
   - Use existing Tailwind CSS classes and design tokens
   - Ensure consistency with current chat message styling
   - Implement responsive breakpoints
   - Add dark/light theme support

2. **Add Animations and Transitions** (1 hour)
   - Smooth transitions between task states
   - Loading animations for pending/running states
   - Subtle animations for log entry additions
   - Hover and focus animations for interactive elements

### Testing and Integration
1. **Unit Tests** (3 hours)
   - Test component rendering with different task states
   - Test user interactions (cancel, copy, retry)
   - Test log display and scrolling behavior
   - Test responsive design and accessibility

2. **Integration Tests** (2 hours)
   - Test component within chat message flow
   - Test with real streaming data
   - Test error scenarios and edge cases
   - Visual regression testing

## Integration Verification

### IV1: Chat UI Pattern Consistency
- [ ] Component styling matches existing chat messages
- [ ] Message spacing and alignment consistent
- [ ] Typography and color scheme follow design system
- [ ] Interactive elements follow existing patterns

### IV2: Responsive Design Compatibility
- [ ] Component works correctly on mobile devices
- [ ] Layout adapts properly to different screen sizes
- [ ] Touch interactions work on mobile
- [ ] No horizontal scrolling issues

### IV3: Accessibility Compliance
- [ ] Screen reader compatibility for status and logs
- [ ] Keyboard navigation for interactive elements
- [ ] Proper ARIA labels and semantic HTML
- [ ] Color contrast meets accessibility standards

## Definition of Done

- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests passing
- [ ] Accessibility audit completed
- [ ] Visual design review approved
- [ ] Cross-browser testing completed
- [ ] Mobile testing completed
- [ ] Performance benchmarks meet requirements

## Technical Implementation Details

### Component Interface
```typescript
interface TaskMessageProps {
  task: TaskInfo;
  isLive: boolean;
  onCancel?: (taskId: string) => void;
  onRetry?: (command: string) => void;
  onCopyCommand?: (command: string) => void;
  className?: string;
}

interface TaskInfo {
  id: string;
  command: string;
  status: TaskStatus;
  logs: LogEntry[];
  startedAt: Date;
  completedAt?: Date;
  exitCode?: number;
  executionTime?: number;
  metadata?: Record<string, any>;
}
```

### Component Structure
```tsx
const TaskMessage: React.FC<TaskMessageProps> = ({
  task,
  isLive,
  onCancel,
  onRetry,
  onCopyCommand
}) => {
  return (
    <div className="task-message bg-slate-50 rounded-lg p-4 mb-4">
      <TaskHeader task={task} onCancel={onCancel} onCopyCommand={onCopyCommand} />
      <TaskStatus status={task.status} executionTime={task.executionTime} />
      <LogContainer logs={task.logs} isLive={isLive} />
      <TaskFooter task={task} onRetry={onRetry} />
    </div>
  );
};
```

### Status Display Design
```tsx
const TaskStatus: React.FC<{status: TaskStatus, executionTime?: number}> = ({
  status,
  executionTime
}) => {
  const statusConfig = {
    pending: { icon: SpinnerIcon, color: 'text-blue-500', message: 'Preparing...' },
    running: { icon: PlayIcon, color: 'text-green-500', message: 'Executing...' },
    completed: { icon: CheckIcon, color: 'text-green-600', message: 'Completed' },
    failed: { icon: XIcon, color: 'text-red-500', message: 'Failed' },
    cancelled: { icon: StopIcon, color: 'text-yellow-500', message: 'Cancelled' }
  };
  
  // Implementation details
};
```

### Log Display Implementation
- Virtual scrolling for performance with large log outputs
- Syntax highlighting for structured log content
- Collapsible sections for verbose output
- Search functionality within logs
- Export logs capability

### Performance Considerations
- Use React.memo for TaskMessage to prevent unnecessary re-renders
- Implement virtual scrolling for log entries
- Debounce rapid log updates during heavy streaming
- Lazy load images or rich content in logs
- Optimize animations using CSS transforms

### Accessibility Features
- ARIA live regions for status updates
- Keyboard shortcuts for common actions
- Screen reader announcements for state changes
- High contrast mode support
- Focus management for interactive elements

## Files Modified/Created

### New Files
- `voicecode-fe-1/src/components/TaskMessage.tsx` - Main task message component
- `voicecode-fe-1/src/components/task/TaskHeader.tsx` - Task header with command and controls
- `voicecode-fe-1/src/components/task/TaskStatus.tsx` - Status indicator component
- `voicecode-fe-1/src/components/task/LogContainer.tsx` - Log display container
- `voicecode-fe-1/src/components/task/TaskFooter.tsx` - Footer with actions
- `voicecode-fe-1/src/styles/task-message.css` - Task-specific styles (if needed)
- `voicecode-fe-1/src/components/__tests__/TaskMessage.test.tsx` - Unit tests

### Modified Files
- `voicecode-fe-1/src/components/Chat.tsx` - Integrate TaskMessage into chat flow
- `voicecode-fe-1/src/types/task.types.ts` - Add UI-specific task types
- `voicecode-fe-1/src/styles/globals.css` - Add any global task styles

## Design Specifications

### Visual Design
- Follow existing chat message card design
- Use subtle borders and shadows for task containers
- Color-coded status indicators (green=success, red=error, blue=running)
- Monospace font for log content
- Responsive layout that adapts to screen size

### Interactive Elements
- Hover effects for buttons and interactive areas
- Loading spinners for async operations
- Confirmation modals for destructive actions (cancel)
- Tooltips for status indicators and buttons

### Animation Design
- Smooth state transitions (0.2s ease-in-out)
- Gentle pulse animation for running tasks
- Slide-in animation for new log entries
- Fade transitions for status changes

## Story Dependencies

### Prerequisites
- Story 1.5: Frontend Streaming Integration (REQUIRED - provides task data and state)

### Enables
- Story 1.7: Testing and Documentation (provides components to test)

## Story Validation Checklist

- [ ] Task execution status is clearly visible to users
- [ ] Real-time log updates work smoothly without performance issues
- [ ] Interactive controls (cancel, retry, copy) function correctly
- [ ] Component integrates seamlessly with existing chat interface
- [ ] Responsive design works on all target devices
- [ ] Accessibility requirements are met
- [ ] Performance is acceptable even with long-running tasks
- [ ] Visual design matches existing application standards