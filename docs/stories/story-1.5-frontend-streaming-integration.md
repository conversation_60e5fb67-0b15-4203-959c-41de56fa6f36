# Story 1.5: Frontend Streaming Integration

**Epic**: Real-Time Task Streaming Enhancement
**Story ID**: RTTS-1.5
**Priority**: High (Frontend Integration)
**Estimated Effort**: 5-8 story points
**Dependencies**: Story 1.4 (Streaming API Endpoint)

## User Story

**As a** developer,
**I want to** enhance the useVoiceCodeChat hook,
**so that** it can handle streaming task execution while maintaining AI SDK patterns.

## Business Value

This story integrates the streaming backend with the existing frontend chat system, enabling users to see real-time command execution without disrupting the current user experience or breaking existing chat functionality.

## Acceptance Criteria

### AC1: Enhanced useVoiceCodeChat Hook
- [ ] Hook enhanced to support both regular chat and task streaming
- [ ] Maintains backward compatibility with existing chat patterns
- [ ] Integrates seamlessly with Vercel AI SDK's useChat hook
- [ ] Proper state management for streaming vs non-streaming messages

### AC2: Streaming Event Processing
- [ ] Hook processes different SSE event types correctly:
  - Task start events (display command execution start)
  - Task log events (append real-time log entries)
  - Task completion events (show final status and exit code)
  - Task error events (display error information)
- [ ] Maintains message ordering and consistency
- [ ] Handles partial messages and message reconstruction

### AC3: Task State Management
- [ ] Tracks active task state within existing message flow
- [ ] Provides task cancellation capability
- [ ] Manages task UI state (loading, streaming, completed)
- [ ] Integrates task state with existing chat state

### AC4: Error Handling and Connection Management
- [ ] Graceful handling of streaming connection failures
- [ ] Automatic reconnection attempts for dropped connections
- [ ] Fallback to regular chat when streaming unavailable
- [ ] User feedback for connection issues

## Technical Implementation Tasks

### Hook Enhancement
1. **Enhance useVoiceCodeChat Hook** (4 hours)
   - Modify existing hook in `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts`
   - Add streaming support while maintaining existing functionality
   - Integrate with Vercel AI SDK patterns
   - Implement state management for streaming vs regular messages

2. **Implement Streaming Message Processing** (3 hours)
   - Parse SSE messages from streaming endpoint
   - Handle different message types (start, log, completion, error)
   - Maintain message ordering and threading
   - Update UI state based on streaming events

3. **Add Task State Management** (2 hours)
   - Track active tasks within message context
   - Implement task cancellation functionality
   - Manage loading and streaming states
   - Integrate with existing message state

4. **Connection Management** (2 hours)
   - Handle SSE connection lifecycle
   - Implement reconnection logic for dropped connections
   - Add connection status indicators
   - Fallback mechanisms for streaming failures

### Integration and Testing
1. **Component Integration** (2 hours)
   - Ensure enhanced hook works with existing chat components
   - Test with various message types and scenarios
   - Verify no breaking changes to existing functionality
   - Update component props and interfaces as needed

2. **Unit Tests** (3 hours)
   - Test hook with streaming and non-streaming scenarios
   - Mock SSE connections for testing
   - Test error handling and edge cases
   - Test backward compatibility with existing patterns

3. **Integration Tests** (2 hours)
   - Test full streaming flow with real backend
   - Verify Vercel AI SDK integration works correctly
   - Test connection handling and error scenarios
   - Performance testing for streaming latency

## Integration Verification

### IV1: Existing Chat Functionality
- [ ] All existing chat features work without modification
- [ ] No breaking changes to component interfaces
- [ ] Chat history and message persistence unaffected
- [ ] User experience remains consistent for non-streaming messages

### IV2: Vercel AI SDK Compatibility
- [ ] useChat hook patterns maintained throughout
- [ ] Message state management follows AI SDK conventions
- [ ] No conflicts with existing AI SDK functionality
- [ ] Streaming integrates naturally with AI SDK patterns

### IV3: Component Integration
- [ ] No breaking changes to component props or interfaces
- [ ] Existing chat components work with enhanced hook
- [ ] TypeScript types remain compatible
- [ ] State updates don't cause unnecessary re-renders

## Definition of Done

- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests passing
- [ ] No breaking changes to existing functionality
- [ ] Performance benchmarks meet requirements
- [ ] TypeScript types properly updated
- [ ] Documentation updated with streaming examples

## Technical Implementation Details

### Enhanced Hook Interface
```typescript
interface UseVoiceCodeChatOptions {
  // Existing options
  api?: string;
  initialMessages?: Message[];
  // New streaming options
  enableStreaming?: boolean;
  onTaskStart?: (task: TaskInfo) => void;
  onTaskComplete?: (task: TaskInfo) => void;
  onTaskError?: (error: TaskError) => void;
}

interface UseVoiceCodeChatReturn {
  // Existing returns
  messages: Message[];
  input: string;
  handleInputChange: (e: ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  // New streaming returns
  activeTask: TaskInfo | null;
  cancelTask: () => void;
  isStreaming: boolean;
}
```

### Message Enhancement for Tasks
```typescript
interface TaskMessage extends Message {
  metadata?: {
    type: 'task_start' | 'task_log' | 'task_complete' | 'task_error';
    task_id: string;
    timestamp?: string;
    exit_code?: number;
    execution_time?: number;
  };
}
```

### Streaming Event Processing
```typescript
const processStreamingEvent = (event: MessageEvent) => {
  const data = JSON.parse(event.data);
  
  switch (data.metadata?.type) {
    case 'task_start':
      handleTaskStart(data);
      break;
    case 'task_log':
      handleTaskLog(data);
      break;
    case 'task_complete':
      handleTaskComplete(data);
      break;
    case 'task_error':
      handleTaskError(data);
      break;
    default:
      handleRegularMessage(data);
  }
};
```

### Error Handling Strategy
```typescript
interface TaskError {
  code: string;
  message: string;
  task_id?: string;
  recoverable: boolean;
}

const handleStreamingError = (error: TaskError) => {
  if (error.recoverable) {
    // Attempt reconnection
    setTimeout(() => retryConnection(), 1000);
  } else {
    // Fall back to regular chat
    setStreamingEnabled(false);
    addSystemMessage('Streaming unavailable, using regular chat');
  }
};
```

### State Management Strategy
- Use existing Vercel AI SDK state patterns
- Add streaming-specific state as extensions
- Maintain message immutability
- Implement optimistic updates for better UX

### Performance Considerations
- Debounce rapid log updates to prevent UI thrashing
- Implement virtual scrolling for long task outputs
- Optimize re-renders during streaming
- Cache task state to prevent unnecessary API calls

## Files Modified/Created

### Modified Files
- `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts` - Enhanced with streaming support
- `voicecode-fe-1/src/types/message.types.ts` - Add task message types
- `voicecode-fe-1/src/lib/fastapi-adapter.ts` - Add streaming message processing

### New Files  
- `voicecode-fe-1/src/hooks/useTaskStreaming.ts` - Dedicated streaming hook (if needed)
- `voicecode-fe-1/src/utils/streaming.ts` - Streaming utilities
- `voicecode-fe-1/src/types/task.types.ts` - Task-related TypeScript types
- `voicecode-fe-1/src/hooks/__tests__/useVoiceCodeChat.test.ts` - Enhanced tests

## Integration Points

### Vercel AI SDK Integration
- Maintain compatibility with useChat hook patterns
- Preserve existing message state management
- Ensure streaming messages follow AI SDK message format
- Keep existing error handling patterns

### FastAPI Adapter Integration
- Enhance adapter to handle streaming responses
- Maintain existing request/response patterns
- Add SSE connection management
- Preserve existing error handling

## Story Dependencies

### Prerequisites
- Story 1.4: Streaming API Endpoint (REQUIRED - provides streaming API)

### Enables  
- Story 1.6: Task UI Components (provides streaming data and state)

## Story Validation Checklist

- [ ] Hook maintains full backward compatibility
- [ ] Streaming integrates seamlessly with existing chat flow
- [ ] Vercel AI SDK patterns preserved throughout
- [ ] Error handling graceful and user-friendly
- [ ] Performance impact minimal on existing functionality
- [ ] Connection management robust and reliable
- [ ] TypeScript types accurate and complete
- [ ] State management follows existing patterns