# Story 1.4: Streaming API Endpoint

**Epic**: Real-Time Task Streaming Enhancement
**Story ID**: RTTS-1.4
**Priority**: High (API Integration)
**Estimated Effort**: 5-8 story points
**Dependencies**: Story 1.3 (Daytona Streaming Integration)

## User Story

**As a** developer,
**I want to** create the SSE streaming endpoint,
**so that** the frontend can receive real-time task execution updates.

## Business Value

This story creates the critical API bridge between the backend streaming capabilities and the frontend, enabling real-time communication using Server-Sent Events in a format compatible with Vercel AI SDK.

## Acceptance Criteria

### AC1: Streaming Endpoint Implementation
- [ ] POST `/api/sandbox/{sandbox_id}/tasks/stream` endpoint created
- [ ] Endpoint accepts task execution requests with command and metadata
- [ ] Returns Server-Sent Events (SSE) stream for real-time updates
- [ ] Proper HTTP headers set for SSE streaming
- [ ] Stream remains open until task completion or client disconnect

### AC2: Vercel AI SDK Compatibility
- [ ] SSE format matches Vercel AI SDK expectations
- [ ] Messages include required fields: `id`, `role`, `content`
- [ ] Task metadata included in message metadata field
- [ ] Stream termination follows AI SDK conventions (`[DONE]` message)
- [ ] Message IDs are unique and sequential

### AC3: Authentication and Authorization
- [ ] Existing JWT middleware applied to streaming endpoint
- [ ] User authorization verified for sandbox access
- [ ] Proper error responses for authentication failures
- [ ] Rate limiting applied to prevent abuse

### AC4: Task Event Streaming
- [ ] Task start events with command information
- [ ] Real-time log entries as they arrive from Daytona
- [ ] Task progress indicators (if available)
- [ ] Task completion events with exit code and final status
- [ ] Error events for task failures

## Technical Implementation Tasks

### API Endpoint Development
1. **Create Streaming Endpoint** (3 hours)
   - Implement FastAPI endpoint with SSE response
   - Set up proper headers for streaming
   - Integrate with TaskService for task management
   - Handle request validation and error responses

2. **Implement SSE Message Formatting** (2 hours)
   - Create message formatter for Vercel AI SDK compatibility
   - Implement unique ID generation for messages
   - Format different event types (start, log, completion, error)
   - Handle special characters and encoding in log content

3. **Integrate Task and Streaming Services** (3 hours)
   - Connect TaskService for task lifecycle management
   - Integrate DaytonaService for log streaming
   - Implement proper error handling and cleanup
   - Add logging and monitoring for stream operations

4. **Add Authentication and Security** (2 hours)
   - Apply existing JWT middleware
   - Implement sandbox access validation
   - Add rate limiting for streaming requests
   - Secure error handling to prevent information leakage

### Testing and Validation
1. **Unit Tests** (3 hours)
   - Test endpoint with various request scenarios
   - Mock TaskService and DaytonaService dependencies
   - Test SSE message formatting
   - Test authentication and authorization

2. **Integration Tests** (2 hours)
   - Test full streaming flow with real services
   - Validate SSE format with actual client consumption
   - Test error scenarios and cleanup
   - Performance testing for streaming latency

3. **API Documentation** (1 hour)
   - Update OpenAPI documentation
   - Add examples for request/response formats
   - Document SSE message structure
   - Include error response examples

## Integration Verification

### IV1: Existing API Compatibility
- [ ] All existing chat endpoints continue to work unchanged
- [ ] No interference with current API functionality
- [ ] Existing error handling patterns maintained

### IV2: Authentication Middleware Integration
- [ ] JWT middleware works correctly with streaming endpoint
- [ ] User context properly available throughout stream lifecycle
- [ ] Authentication errors handled appropriately

### IV3: CORS and Security
- [ ] CORS configuration supports SSE streaming
- [ ] Security headers properly set for streaming responses
- [ ] No security vulnerabilities introduced

## Definition of Done

- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests passing
- [ ] API documentation updated
- [ ] Security review completed
- [ ] Performance benchmarks meet requirements (<500ms latency)
- [ ] Error handling comprehensive and tested

## Technical Implementation Details

### SSE Message Format
```text
data: {"id": "msg-1", "role": "assistant", "content": "Executing: npm test", "metadata": {"type": "task_start", "task_id": "task-123"}}

data: {"id": "msg-2", "role": "assistant", "content": "Running tests...", "metadata": {"type": "task_log", "task_id": "task-123", "timestamp": "2025-01-31T10:00:01Z"}}

data: {"id": "msg-3", "role": "assistant", "content": "Task completed (exit code: 0)", "metadata": {"type": "task_complete", "task_id": "task-123", "exit_code": 0, "execution_time": 5.2}}

data: [DONE]
```

### Endpoint Implementation Structure
```python
@app.post("/api/sandbox/{sandbox_id}/tasks/stream")
async def stream_task_execution(
    sandbox_id: UUID,
    request: TaskExecutionRequest,
    current_user: User = Depends(get_current_user),
    task_service: TaskService = Depends(get_task_service),
    daytona_service: DaytonaService = Depends(get_daytona_service)
) -> StreamingResponse:
    """Execute command with real-time log streaming"""
    # Implementation details
```

### Request/Response Models
```python
class TaskExecutionRequest(BaseModel):
    command: str
    metadata: Optional[Dict] = None

class SSEMessage(BaseModel):
    id: str
    role: str = "assistant"
    content: str
    metadata: Optional[Dict] = None
```

### Error Handling Strategy
- 400 Bad Request: Invalid command or request format
- 401 Unauthorized: Authentication failure
- 403 Forbidden: Insufficient permissions for sandbox
- 409 Conflict: Task already running in sandbox
- 500 Internal Server Error: Streaming or execution failure

### Performance Considerations
- Implement connection timeout handling
- Use async generators for memory efficiency
- Buffer small messages to reduce network overhead
- Monitor connection count and implement limits

### Security Considerations
- Validate all input parameters
- Sanitize command content before execution
- Implement proper CORS policies
- Rate limit streaming requests per user
- Secure error messages to prevent information disclosure

## Files Modified/Created

### New Files
- `voicecode/fastapi-app/routers/streaming.py` - Streaming endpoints ✅
- `voicecode/fastapi-app/models/streaming_models.py` - Request/response models ✅
- `voicecode/fastapi-app/utils/sse_formatter.py` - SSE message formatting ✅
- `voicecode/fastapi-app/tests/test_streaming_endpoint.py` - Unit tests ✅
- `voicecode/fastapi-app/tests/integration/test_streaming_api.py` - Integration tests ✅
- `voicecode/fastapi-app/dependencies.py` - Shared dependencies module ✅
- `voicecode/fastapi-app/test_implementation.py` - Implementation validation script ✅

### Modified Files
- `voicecode/fastapi-app/main.py` - Register streaming router and update API docs ✅
- CORS configuration already supports SSE streaming (no changes needed) ✅

## API Design Specifications

### Request Format
```json
{
  "command": "npm test",
  "metadata": {
    "source": "chat",
    "context": {
      "message_id": "msg-456"
    }
  }
}
```

### Response Headers
```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive
Access-Control-Allow-Origin: *
```

### Message Types
- `task_start`: Task creation and initial command information
- `task_log`: Real-time log entries from command execution
- `task_progress`: Progress indicators (if available)
- `task_complete`: Task completion with final status
- `task_error`: Error events during execution

## Story Dependencies

### Prerequisites
- Story 1.2: Task Management Service (REQUIRED - for task lifecycle)
- Story 1.3: Daytona Streaming Integration (REQUIRED - for log streaming)

### Enables
- Story 1.5: Frontend Streaming Integration (provides API for frontend consumption)

## Story Validation Checklist

- [ ] SSE streaming works correctly with target frontend framework
- [ ] Message format is compatible with Vercel AI SDK
- [ ] Authentication and authorization work properly
- [ ] Error handling covers all edge cases
- [ ] Performance meets latency requirements
- [ ] Security controls are properly implemented
- [ ] API documentation is complete and accurate
- [ ] Integration doesn't break existing functionality