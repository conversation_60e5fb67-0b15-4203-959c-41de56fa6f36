# Coding Standards and Conventions

## Existing Standards Compliance
**Code Style:** <PERSON> (Python), <PERSON><PERSON>int + <PERSON><PERSON><PERSON> (TypeScript)
**Linting Rules:** Strict type checking, no-any rule
**Testing Patterns:** pytest for backend, Jest for frontend
**Documentation Style:** Docstrings (Python), JSDoc (TypeScript)

## Critical Integration Rules
- **Existing API Compatibility:** No changes to existing endpoints
- **Database Integration:** Only additive changes, no schema modifications
- **Error Handling:** Use existing HTTPException patterns
- **Logging Consistency:** Use existing logger configuration
