# Source Tree Integration

## Existing Project Structure
```plaintext
voicecode/
├── fastapi-app/
│   ├── main.py
│   ├── database.py
│   └── alembic/
└── scripts/
voicecode-fe-1/
├── src/
│   ├── hooks/
│   ├── lib/
│   ├── services/
│   └── components/
```

## New File Organization
```plaintext
voicecode/
├── fastapi-app/
│   ├── services/               # New service layer
│   │   ├── __init__.py
│   │   ├── task_service.py     # Task management logic
│   │   └── daytona_service.py  # Enhanced Daytona integration
│   ├── models/                 # New models directory
│   │   ├── __init__.py
│   │   └── task_models.py      # Pydantic models for tasks
│   └── alembic/versions/       # Existing migrations
│       └── 003_add_tasks_table.py  # New migration

voicecode-fe-1/
├── src/
│   ├── components/            # Existing components
│   │   └── TaskMessage.tsx    # New task display component
│   └── types/                 # Existing types
│       └── task.types.ts      # New task type definitions
```

## Integration Guidelines
- **File Naming:** Follow existing patterns - snake_case (Python), camelCase (TypeScript)
- **Folder Organization:** Services in dedicated directory, maintain flat structure
- **Import/Export Patterns:** Named exports for services, default exports for components
