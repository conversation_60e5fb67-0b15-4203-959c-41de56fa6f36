# API Design and Integration

## API Integration Strategy
**API Integration Strategy:** New streaming endpoint with SSE, maintains REST patterns
**Authentication:** Existing JWT middleware applies to streaming endpoint
**Versioning:** No versioning needed - additive change only

## New API Endpoints

### Stream Task Execution
- **Method:** POST
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/stream`
- **Purpose:** Execute command with real-time log streaming
- **Integration:** Uses existing auth, new SSE response format

#### Request
```json
{
  "command": "npm test",
  "metadata": {
    "source": "chat",
    "context": {}
  }
}
```

#### Response
```text
data: {"id": "msg-1", "role": "assistant", "content": "Executing: npm test", "metadata": {"type": "task_start", "task_id": "task-123"}}

data: {"id": "msg-2", "role": "assistant", "content": "Running tests...", "metadata": {"type": "task_log", "task_id": "task-123"}}

data: {"id": "msg-3", "role": "assistant", "content": "Task completed (exit code: 0)", "metadata": {"type": "task_complete", "task_id": "task-123", "exit_code": 0}}

data: [DONE]
```

### Get Current Task
- **Method:** GET
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/current`
- **Purpose:** Check if task is running in sandbox
- **Integration:** Standard REST response format

#### Request
No body required

#### Response
```json
{
  "data": {
    "id": "task-123",
    "status": "running",
    "command": "npm test",
    "started_at": "2025-01-31T10:00:00Z"
  },
  "message": "Current task retrieved"
}
```

### Cancel Task
- **Method:** POST
- **Endpoint:** `/api/sandbox/{sandbox_id}/tasks/{task_id}/cancel`
- **Purpose:** Cancel running task
- **Integration:** Standard REST response format

#### Request
No body required

#### Response
```json
{
  "data": {
    "success": true
  },
  "message": "Task cancelled successfully"
}
```
