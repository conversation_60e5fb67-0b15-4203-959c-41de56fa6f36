# Data Models and Schema Changes

## New Data Models

### Task
**Purpose:** Track command execution state, history, and logs
**Integration:** Foreign key relationship with ChatMessage for message tracking

**Key Attributes:**
- `id`: UUID - Primary key
- `sandbox_id`: UUID - Links to sandbox
- `user_id`: UUID - Links to user
- `command`: Text - Command being executed
- `status`: Enum - pending/running/completed/failed/cancelled
- `started_at`: DateTime - Execution start time
- `completed_at`: DateTime - Execution end time
- `exit_code`: Integer - Command exit code
- `execution_time`: Float - Total execution seconds
- `logs`: JSON - Array of log entries
- `task_metadata`: JSON - Additional metadata

**Relationships:**
- **With Existing:** Many-to-one with ChatMessage (optional)
- **With New:** None

## Schema Integration Strategy

**Database Changes Required:**
- **New Tables:** tasks
- **Modified Tables:** None (ChatMessage unchanged)
- **New Indexes:** sandbox_id, user_id, status, created_at
- **Migration Strategy:** Alembic migration with zero downtime

**Backward Compatibility:**
- Existing chat_messages table remains untouched
- Task tracking is optional - chat can work without tasks
- Foreign key is nullable to support non-task messages
