# Component Architecture

## New Components

### TaskService
**Responsibility:** Manage task lifecycle, state transitions, and database operations
**Integration Points:** Database layer, DaytonaService, Redis for state

**Key Interfaces:**
- `create_task(sandbox_id, user_id, command) -> Task`
- `get_current_task(sandbox_id, user_id) -> Optional[Task]`
- `cancel_task(task_id, user_id) -> bool`
- `execute_task_stream(task) -> AsyncGenerator[TaskEvent]`

**Dependencies:**
- **Existing Components:** Database session, Redis client
- **New Components:** Enhanced DaytonaService

**Technology Stack:** Python async/await, SQLAlchemy, asyncio

### Enhanced DaytonaService
**Responsibility:** Abstract Daytona SDK streaming capabilities for task execution
**Integration Points:** Daytona SDK, TaskService

**Key Interfaces:**
- `execute_command_stream(sandbox_id, command) -> AsyncGenerator[Dict]`
- Session management for streaming
- Log aggregation and formatting

**Dependencies:**
- **Existing Components:** Daytona SDK client
- **New Components:** None

**Technology Stack:** Python async, Daytona SDK async methods

### TaskMessage Component
**Responsibility:** Display real-time task execution status and logs in UI
**Integration Points:** Chat UI, useVoiceCodeChat hook

**Key Interfaces:**
- Props: `task`, `isLive`, `onCancel`
- Real-time log updates via React state
- Auto-scroll for new logs

**Dependencies:**
- **Existing Components:** UI component library, theme system
- **New Components:** None

**Technology Stack:** React, TypeScript, Tailwind CSS

## Component Interaction Diagram

```mermaid
graph TB
    subgraph Frontend
        UI[Chat UI]
        Hook[useVoiceCodeChat]
        TM[TaskMessage]
        AISDK[Vercel AI SDK]
    end
    
    subgraph Backend
        API[Streaming Endpoint]
        TS[TaskService]
        DS[DaytonaService]
        DB[(PostgreSQL)]
        Redis[(Redis)]
    end
    
    subgraph External
        Daytona[Daytona Sandbox]
    end
    
    UI --> Hook
    Hook --> AISDK
    AISDK --> API
    API --> TS
    TS --> DS
    TS --> DB
    TS --> Redis
    DS --> Daytona
    
    Daytona -.->|Log Stream| DS
    DS -.->|Events| TS
    TS -.->|SSE| API
    API -.->|Stream| AISDK
    AISDK -.->|Updates| Hook
    Hook --> TM
    TM --> UI
```
