# VoiceCode Brownfield Architecture Document

## Introduction

This document captures the CURRENT STATE of the VoiceCode codebase, including technical patterns, integration points, and areas that will be affected by the task streaming enhancement. It serves as a reference for AI agents working on implementing real-time command execution with log streaming.

### Document Scope

Focused on areas relevant to: **Task Streaming Enhancement** - Adding real-time command execution with log streaming from Daytona sandboxes to the frontend via Vercel AI SDK.

### Change Log

| Date       | Version | Description                                    | Author    |
| ---------- | ------- | ---------------------------------------------- | --------- |
| 2025-01-31 | 1.0     | Initial brownfield analysis for task streaming | Architect |

## Quick Reference - Key Files and Entry Points

### Critical Files for Understanding the System

- **Backend Entry**: `voicecode/fastapi-app/main.py` 
- **Database Models**: `voicecode/fastapi-app/database.py`
- **Frontend Hook**: `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts`
- **FastAPI Adapter**: `voicecode-fe-1/src/lib/fastapi-adapter.ts`
- **Configuration**: `.env` files in respective directories
- **Daytona Integration**: Command execution in `main.py` lines 800-850

### Enhancement Impact Areas

Files/modules that will be affected by task streaming:
- `voicecode/fastapi-app/main.py` - Add streaming endpoints
- `voicecode/fastapi-app/database.py` - Add Task model
- `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts` - Stream handling
- `voicecode-fe-1/src/lib/fastapi-adapter.ts` - Task message processing
- New: Task service, streaming utilities, TaskMessage component

## High Level Architecture

### Technical Summary

VoiceCode is a React web application built with Vite that enables voice-controlled GitHub repository interaction through Claude Code CLI execution in Daytona sandboxes. The current architecture uses a request-response pattern for command execution, which will be enhanced with real-time streaming.

### Actual Tech Stack

| Category    | Technology      | Version   | Notes                                    |
| ----------- | --------------- | --------- | ---------------------------------------- |
| Backend     | FastAPI         | 0.116.1   | REST API, needs SSE support for streaming |
| Runtime     | Python          | 3.11+     | Async support available                  |
| Frontend    | React + Vite    | Latest    | With Capacitor for mobile deployment    |
| UI Library  | Vercel AI SDK   | 4.3.19    | Already integrated with useChat hook    |
| Database    | PostgreSQL      | Latest    | With SQLAlchemy ORM                     |
| Cache       | Redis           | 6.2.0     | For session and sandbox state           |
| Sandbox     | Daytona SDK     | 0.22.0    | Supports log streaming (verified)       |
| Auth        | JWT             | PyJWT     | Custom implementation                   |

### Repository Structure Reality Check

- Type: Monorepo with separate backend/frontend
- Package Manager: pnpm (frontend), pip/uv (backend)
- Notable: Clean separation between services

## Source Tree and Module Organization

### Project Structure (Actual)

```text
sandboxed/
├── voicecode/
│   ├── fastapi-app/
│   │   ├── main.py              # Main API endpoints (800+ lines)
│   │   ├── database.py          # SQLAlchemy models
│   │   ├── alembic/             # Database migrations
│   │   └── docs/                # Implementation docs
│   └── scripts/
│       └── sandbox-manager.js   # Daytona management
├── voicecode-fe-1/
│   ├── src/
│   │   ├── hooks/
│   │   │   └── useVoiceCodeChat.ts  # Vercel AI SDK integration
│   │   ├── lib/
│   │   │   └── fastapi-adapter.ts   # Backend bridge
│   │   ├── services/            # API service layer
│   │   └── components/          # UI components
│   └── capacitor.config.json   # Mobile config
└── docs/
    └── task-streaming-plan.md   # Enhancement plan
```

### Key Modules and Their Current State

- **Command Execution**: `main.py:execute_command()` - Synchronous execution via `sandbox.process.exec()`
- **Chat System**: `main.py:send_chat_message()` - Stores messages, no streaming
- **Frontend Hook**: `useVoiceCodeChat.ts` - Uses Vercel AI SDK's useChat, ready for streaming
- **FastAPI Adapter**: Converts between backend and AI SDK formats
- **Daytona Integration**: Direct SDK usage, no streaming utilized yet

## Data Models and APIs

### Current Data Models

- **ChatMessage Model**: See `voicecode/fastapi-app/database.py`
  - Stores chat history with metadata
  - No task tracking capability
  - JSONB metadata field available for extension

### Current API Patterns

```python
# Current command execution (synchronous)
@app.post("/api/sandbox/{sandbox_id}/execute")
async def execute_command(...):
    response = sandbox.process.exec(command)
    return CommandResponse(...)

# Current chat (no streaming)
@app.post("/api/sandbox/{sandbox_id}/chat")
async def send_chat_message(...):
    # Stores message, returns static response
```

### Missing Capabilities for Task Streaming

1. No SSE/streaming endpoints
2. No task state management
3. No async command execution tracking
4. No log streaming from Daytona

## Technical Patterns and Constraints

### Current Implementation Patterns

1. **Authentication**: JWT bearer tokens on all endpoints
2. **Error Handling**: Standard HTTPException with status codes
3. **Database Access**: SQLAlchemy ORM with session management
4. **Response Format**: Consistent `{"data": ..., "message": ...}` structure
5. **Sandbox Ownership**: Verified via Redis cache before operations

### Integration Constraints

- **Daytona SDK**: Supports async execution and log streaming (confirmed in docs)
- **Frontend**: Already uses Vercel AI SDK streaming capabilities
- **Database**: PostgreSQL with proper async support
- **Redis**: Used for sandbox state, can track active tasks

### Technical Debt Relevant to Task Streaming

1. **No Background Task System**: Will need async task management
2. **Simple Command Processing**: `process_command()` wraps with Claude Code, needs task awareness
3. **No Real-time Updates**: Current polling-based status updates
4. **Limited Error Recovery**: Basic try-catch, needs streaming error handling

## Integration Points and External Dependencies

### External Services

| Service  | Purpose            | Integration Type | Key Files                    |
| -------- | ------------------ | ---------------- | ---------------------------- |
| Daytona  | Sandbox execution  | SDK              | `main.py`, daytona config    |
| GitHub   | Repository access  | OAuth + API      | GitHub app configuration     |
| Claude   | Code assistance    | CLI in sandbox   | Via command wrapping         |

### Internal Integration Points

- **Frontend ↔ Backend**: REST API on port 9100
- **Backend → Daytona**: Direct SDK calls
- **Database**: Connection pooling configured
- **Redis**: Sandbox state management

## Development and Deployment

### Local Development Setup

1. Backend: `cd voicecode/fastapi-app && uvicorn main:app --reload`
2. Frontend: `cd voicecode-fe-1 && pnpm dev`
3. Database: PostgreSQL required (Docker or local)
4. Redis: Required for sandbox state

### Current Testing Approach

- Backend: Basic test scripts (test_chat_endpoints.py)
- Frontend: Limited test coverage
- Integration: Manual testing primary method

## Impact Analysis for Task Streaming Enhancement

### Files That Will Need Modification

Based on the enhancement requirements:

1. **Backend Core**:
   - `voicecode/fastapi-app/main.py` - Add streaming endpoint
   - `voicecode/fastapi-app/database.py` - Add Task model

2. **Frontend Integration**:
   - `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts` - Handle streaming
   - `voicecode-fe-1/src/lib/fastapi-adapter.ts` - Process task events

### New Files/Modules Needed

1. **Backend**:
   - `voicecode/fastapi-app/services/task_service.py` - Task management
   - `voicecode/fastapi-app/services/daytona_service.py` - Enhanced Daytona integration
   - `voicecode/fastapi-app/models/task_models.py` - Pydantic models

2. **Frontend**:
   - `voicecode-fe-1/src/components/TaskMessage.tsx` - Task UI
   - `voicecode-fe-1/src/types/task.types.ts` - TypeScript types

### Integration Considerations

1. **Must maintain Vercel AI SDK patterns** - Use existing useChat hook
2. **SSE response format** - Compatible with AI SDK expectations
3. **Database migration** - Add Task table via Alembic
4. **Backward compatibility** - Existing chat must continue working
5. **Error handling** - Stream errors gracefully
6. **One task at a time** - Enforce via database checks

## Current Command Flow (Before Enhancement)

```mermaid
graph LR
    A[User Input] --> B[Frontend]
    B --> C[POST /execute]
    C --> D[Daytona SDK]
    D --> E[sandbox.process.exec]
    E --> F[Wait for completion]
    F --> G[Return response]
    G --> B
```

## Appendix - Useful Commands and Scripts

### Frequently Used Commands

```bash
# Backend
cd voicecode/fastapi-app
uvicorn main:app --reload --port 9100

# Frontend  
cd voicecode-fe-1
pnpm dev

# Database
alembic upgrade head
alembic revision --autogenerate -m "description"

# Testing
python test_chat_endpoints.py
```

### Key Environment Variables

- `DAYTONA_API_KEY` - Required for sandbox operations
- `DATABASE_URL` - PostgreSQL connection
- `REDIS_URL` - Redis connection
- `JWT_SECRET` - For authentication

---

This document provides the current state context needed to implement the task streaming enhancement while respecting existing patterns and constraints.