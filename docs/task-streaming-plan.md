# Detailed Implementation Plan: Task Management System with AI SDK Streaming

## Phase 1: Backend Foundation (Week 1)

### 1.1 Database Schema Updates

**File: `voicecode/fastapi-app/database.py`**

```python
# Add new Task model
class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sandbox_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    command = Column(Text, nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, index=True)
    
    # Execution details
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    exit_code = Column(Integer, nullable=True)
    execution_time = Column(Float, nullable=True)
    
    # Logs and metadata
    logs = Column(JSON, default=list)  # Store logs as JSON array
    task_metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    chat_messages = relationship("ChatMessage", back_populates="task")

# Update ChatMessage model
class ChatMessage(Base):
    # ... existing fields ...
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)
    
    # Relationships
    task = relationship("Task", back_populates="chat_messages")

# Add TaskStatus enum
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
```

**Migration Script: `voicecode/fastapi-app/alembic/versions/add_task_management.py`**

```python
"""Add task management tables

Revision ID: add_task_management
Revises: previous_revision
Create Date: 2024-01-XX XX:XX:XX.XXXXXX

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'add_task_management'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None

def upgrade():
    # Create task status enum
    task_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'cancelled',
        name='taskstatus'
    )
    task_status_enum.create(op.get_bind())
    
    # Create tasks table
    op.create_table('tasks',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('sandbox_id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('command', sa.Text(), nullable=False),
        sa.Column('status', task_status_enum, nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('exit_code', sa.Integer(), nullable=True),
        sa.Column('execution_time', sa.Float(), nullable=True),
        sa.Column('logs', sa.JSON(), nullable=True),
        sa.Column('task_metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add indexes
    op.create_index('ix_tasks_sandbox_id', 'tasks', ['sandbox_id'])
    op.create_index('ix_tasks_user_id', 'tasks', ['user_id'])
    op.create_index('ix_tasks_status', 'tasks', ['status'])
    
    # Add task_id column to chat_messages
    op.add_column('chat_messages', sa.Column('task_id', sa.UUID(), nullable=True))
    op.create_foreign_key('fk_chat_messages_task_id', 'chat_messages', 'tasks', ['task_id'], ['id'])

def downgrade():
    op.drop_constraint('fk_chat_messages_task_id', 'chat_messages', type_='foreignkey')
    op.drop_column('chat_messages', 'task_id')
    op.drop_index('ix_tasks_status', table_name='tasks')
    op.drop_index('ix_tasks_user_id', table_name='tasks')
    op.drop_index('ix_tasks_sandbox_id', table_name='tasks')
    op.drop_table('tasks')
    op.execute('DROP TYPE taskstatus')
```

### 1.2 Pydantic Models

**File: `voicecode/fastapi-app/models/task_models.py`**

```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskCreateRequest(BaseModel):
    command: str = Field(..., description="Command to execute")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TaskResponse(BaseModel):
    id: str
    sandbox_id: str
    user_id: str
    command: str
    status: TaskStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    exit_code: Optional[int] = None
    execution_time: Optional[float] = None
    logs: List[str] = []
    metadata: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime

class TaskStreamEvent(BaseModel):
    type: str = Field(..., description="Event type: task_start, task_log, task_status, task_complete, task_error")
    task_id: str
    timestamp: datetime
    data: Dict[str, Any] = {}

class TaskLogEvent(TaskStreamEvent):
    type: str = "task_log"
    log: str

class TaskStatusEvent(TaskStreamEvent):
    type: str = "task_status"
    status: TaskStatus

class TaskCompleteEvent(TaskStreamEvent):
    type: str = "task_complete"
    exit_code: int
    execution_time: float

class TaskErrorEvent(TaskStreamEvent):
    type: str = "task_error"
    error: str
```

### 1.3 Task Management Service

**File: `voicecode/fastapi-app/services/task_service.py`**

```python
import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import AsyncGenerator, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from database import Task, TaskStatus
from services.daytona_service import DaytonaService
from models.task_models import TaskStreamEvent, TaskLogEvent, TaskStatusEvent, TaskCompleteEvent, TaskErrorEvent

class TaskService:
    def __init__(self, daytona_service: DaytonaService):
        self.daytona = daytona_service
        self.active_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_task(self, db: Session, sandbox_id: str, user_id: str, command: str, metadata: Dict[str, Any] = None) -> Task:
        """Create a new task"""
        task = Task(
            sandbox_id=uuid.UUID(sandbox_id),
            user_id=uuid.UUID(user_id),
            command=command,
            status=TaskStatus.PENDING,
            task_metadata=metadata or {}
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        return task
    
    async def get_current_task(self, db: Session, sandbox_id: str, user_id: str) -> Optional[Task]:
        """Get currently running task for sandbox"""
        return db.query(Task).filter(
            and_(
                Task.sandbox_id == uuid.UUID(sandbox_id),
                Task.user_id == uuid.UUID(user_id),
                Task.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING])
            )
        ).first()
    
    async def cancel_task(self, db: Session, task_id: str, user_id: str) -> bool:
        """Cancel a running task"""
        task = db.query(Task).filter(
            and_(
                Task.id == uuid.UUID(task_id),
                Task.user_id == uuid.UUID(user_id),
                Task.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING])
            )
        ).first()
        
        if not task:
            return False
        
        # Cancel the asyncio task if it's running
        if str(task.id) in self.active_tasks:
            self.active_tasks[str(task.id)].cancel()
            del self.active_tasks[str(task.id)]
        
        # Update task status
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now(timezone.utc)
        db.commit()
        
        return True
    
    async def execute_task_stream(
        self, 
        db: Session, 
        task: Task, 
        sandbox_id: str
    ) -> AsyncGenerator[TaskStreamEvent, None]:
        """Execute task with streaming updates"""
        
        try:
            # Update task to running
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now(timezone.utc)
            db.commit()
            
            # Send start event
            yield TaskStatusEvent(
                task_id=str(task.id),
                timestamp=datetime.now(timezone.utc),
                status=TaskStatus.RUNNING
            )
            
            # Execute with Daytona
            execution_start = datetime.now(timezone.utc)
            
            async for event in self.daytona.execute_command_stream(sandbox_id, task.command):
                if event['type'] == 'log':
                    # Add log to task
                    if not task.logs:
                        task.logs = []
                    task.logs.append(event['data'])
                    
                    # Yield log event
                    yield TaskLogEvent(
                        task_id=str(task.id),
                        timestamp=datetime.now(timezone.utc),
                        log=event['data']
                    )
                    
                elif event['type'] == 'complete':
                    execution_end = datetime.now(timezone.utc)
                    execution_time = (execution_end - execution_start).total_seconds()
                    
                    # Update task
                    task.status = TaskStatus.COMPLETED if event['exit_code'] == 0 else TaskStatus.FAILED
                    task.completed_at = execution_end
                    task.exit_code = event['exit_code']
                    task.execution_time = execution_time
                    db.commit()
                    
                    # Yield completion event
                    yield TaskCompleteEvent(
                        task_id=str(task.id),
                        timestamp=execution_end,
                        exit_code=event['exit_code'],
                        execution_time=execution_time
                    )
                    break
            
        except asyncio.CancelledError:
            # Task was cancelled
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now(timezone.utc)
            db.commit()
            
            yield TaskErrorEvent(
                task_id=str(task.id),
                timestamp=datetime.now(timezone.utc),
                error="Task was cancelled"
            )
            
        except Exception as e:
            # Task failed
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now(timezone.utc)
            if not task.logs:
                task.logs = []
            task.logs.append(f"Error: {str(e)}")
            db.commit()
            
            yield TaskErrorEvent(
                task_id=str(task.id),
                timestamp=datetime.now(timezone.utc),
                error=str(e)
            )
        
        finally:
            # Clean up active task
            if str(task.id) in self.active_tasks:
                del self.active_tasks[str(task.id)]
```

### 1.4 Enhanced Daytona Service

**File: `voicecode/fastapi-app/services/daytona_service.py`**

```python
import asyncio
import uuid
from typing import AsyncGenerator, Dict, Any
from daytona import Daytona, SessionExecuteRequest

class DaytonaService:
    def __init__(self, daytona_client: Daytona):
        self.daytona = daytona_client
    
    async def execute_command_stream(self, sandbox_id: str, command: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute command with streaming logs"""
        
        try:
            sandbox = self.daytona.get(sandbox_id)
            
            # Create session for command execution
            session_id = f"task-{uuid.uuid4()}"
            sandbox.process.create_session(session_id)
            
            # Execute command asynchronously
            execution = sandbox.process.execute_session_command(
                session_id,
                SessionExecuteRequest(
                    command=command,
                    var_async=True  # Non-blocking execution
                )
            )
            
            # Stream logs in real-time
            while True:
                try:
                    status = sandbox.process.get_session_status(session_id)
                    
                    if status.is_running:
                        # Get new logs
                        logs = sandbox.process.get_session_logs(session_id, follow=True)
                        
                        for log_line in logs:
                            if log_line.strip():
                                yield {
                                    'type': 'log',
                                    'data': log_line.strip()
                                }
                        
                        await asyncio.sleep(0.5)  # Poll every 500ms
                    else:
                        # Session completed, get final logs
                        final_logs = sandbox.process.get_session_logs(session_id)
                        for log_line in final_logs:
                            if log_line.strip():
                                yield {
                                    'type': 'log',
                                    'data': log_line.strip()
                                }
                        
                        # Get final result
                        result = sandbox.process.get_session_result(session_id)
                        yield {
                            'type': 'complete',
                            'exit_code': result.exit_code,
                            'result': result.result
                        }
                        break
                        
                except Exception as e:
                    yield {
                        'type': 'error',
                        'data': f"Log streaming error: {str(e)}"
                    }
                    break
                    
        except Exception as e:
            yield {
                'type': 'error',
                'data': f"Execution error: {str(e)}"
            }
```

## Phase 2: Backend API Endpoints (Week 1-2)

### 2.1 Task Management Endpoints

**File: `voicecode/fastapi-app/main.py` - Add these endpoints**

```python
from fastapi.responses import StreamingResponse
from services.task_service import TaskService
from models.task_models import *

# Initialize task service
task_service = TaskService(DaytonaService(daytona))

@app.post("/api/sandbox/{sandbox_id}/tasks")
async def create_task(
    sandbox_id: str,
    request: TaskCreateRequest,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new task"""
    user_id = current_user["user"]["user"]["id"]
    
    # Check if there's already a running task
    current_task = await task_service.get_current_task(db, sandbox_id, user_id)
    if current_task:
        raise HTTPException(
            status_code=409, 
            detail=f"Task already running: {current_task.command}"
        )
    
    # Verify sandbox ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Create task
    task = await task_service.create_task(
        db, sandbox_id, user_id, request.command, request.metadata
    )
    
    return {
        "data": TaskResponse.from_orm(task),
        "message": "Task created successfully"
    }

@app.get("/api/sandbox/{sandbox_id}/tasks/current")
async def get_current_task(
    sandbox_id: str,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get currently running task"""
    user_id = current_user["user"]["user"]["id"]
    
    task = await task_service.get_current_task(db, sandbox_id, user_id)
    if not task:
        raise HTTPException(status_code=404, detail="No running task")
    
    return {
        "data": TaskResponse.from_orm(task),
        "message": "Current task retrieved"
    }

@app.post("/api/sandbox/{sandbox_id}/tasks/{task_id}/cancel")
async def cancel_task(
    sandbox_id: str,
    task_id: str,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a running task"""
    user_id = current_user["user"]["user"]["id"]
    
    success = await task_service.cancel_task(db, task_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="Task not found or not cancellable")
    
    return {
        "data": {"success": True},
        "message": "Task cancelled successfully"
    }

@app.post("/api/sandbox/{sandbox_id}/tasks/stream")
async def stream_task_execution(
    sandbox_id: str,
    request: TaskCreateRequest,
    current_user: Dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Stream task execution compatible with AI SDK"""
    
    user_id = current_user["user"]["user"]["id"]
    
    # Check for existing running task
    current_task = await task_service.get_current_task(db, sandbox_id, user_id)
    if current_task:
        raise HTTPException(
            status_code=409,
            detail=f"Task already running: {current_task.command}"
        )
    
    # Verify sandbox ownership
    sandbox_data = redis_client.get(f"sandbox:{sandbox_id}")
    if not sandbox_data:
        raise HTTPException(status_code=404, detail="Sandbox not found")
    
    sandbox_info = json.loads(sandbox_data)
    if sandbox_info.get("user_id") != user_id:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Create task
    task = await task_service.create_task(
        db, sandbox_id, user_id, request.command, request.metadata
    )
    
    async def generate_stream():
        """Generate AI SDK compatible stream"""
        try:
            # Send initial message
            yield f"data: {json.dumps({
                'id': str(uuid.uuid4()),
                'role': 'assistant',
                'content': f'Executing: {request.command}',
                'metadata': {
                    'type': 'task_start',
                    'task_id': str(task.id),
                    'command': request.command,
                    'status': 'running'
                }
            })}\n\n"
            
            # Stream task execution
            async for event in task_service.execute_task_stream(db, task, sandbox_id):
                if isinstance(event, TaskLogEvent):
                    # Stream log as content update
                    yield f"data: {json.dumps({
                        'id': str(uuid.uuid4()),
                        'role': 'assistant',
                        'content': event.log,
                        'metadata': {
                            'type': 'task_log',
                            'task_id': event.task_id,
                            'timestamp': event.timestamp.isoformat()
                        }
                    })}\n\n"
                
                elif isinstance(event, TaskCompleteEvent):
                    # Send completion message
                    status_text = "completed" if event.exit_code == 0 else "failed"
                    yield f"data: {json.dumps({
                        'id': str(uuid.uuid4()),
                        'role': 'assistant',
                        'content': f'Task {status_text} (exit code: {event.exit_code})',
                        'metadata': {
                            'type': 'task_complete',
                            'task_id': event.task_id,
                            'exit_code': event.exit_code,
                            'execution_time': event.execution_time,
                            'status': status_text
                        }
                    })}\n\n"
                    break
                
                elif isinstance(event, TaskErrorEvent):
                    # Send error message
                    yield f"data: {json.dumps({
                        'id': str(uuid.uuid4()),
                        'role': 'assistant',
                        'content': f'Task failed: {event.error}',
                        'metadata': {
                            'type': 'task_error',
                            'task_id': event.task_id,
                            'error': event.error
                        }
                    })}\n\n"
                    break
            
        except Exception as e:
            yield f"data: {json.dumps({
                'id': str(uuid.uuid4()),
                'role': 'assistant',
                'content': f'Execution error: {str(e)}',
                'metadata': {
                    'type': 'task_error',
                    'task_id': str(task.id),
                    'error': str(e)
                }
            })}\n\n"
        
        finally:
            yield f"data: [DONE]\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )
```

## Phase 3: Frontend Implementation (Week 2-3)

### 3.1 Enhanced Types

**File: `voicecode-fe-1/src/types/task.types.ts`**

```typescript
export interface Task {
  id: string
  sandboxId: string
  userId: string
  command: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt?: string
  completedAt?: string
  exitCode?: number
  executionTime?: number
  logs: string[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface TaskStreamEvent {
  type: 'task_start' | 'task_log' | 'task_status' | 'task_complete' | 'task_error'
  taskId: string
  timestamp: string
  data?: Record<string, any>
}

export interface TaskCreateRequest {
  command: string
  metadata?: Record<string, any>
}
```

### 3.2 Task Service

**File: `voicecode-fe-1/src/services/task.service.ts`**

```typescript
import { ApiService } from './api.service'
import type { Task, TaskCreateRequest } from '@/types/task.types'

export class TaskService {
  private static baseUrl = '/api/sandbox'

  static async createTask(sandboxId: string, request: TaskCreateRequest): Promise<{
    data: Task
    message: string
  }> {
    return ApiService.post(`${this.baseUrl}/${sandboxId}/tasks`, request)
  }

  static async getCurrentTask(sandboxId: string): Promise<{
    data: Task
    message: string
  }> {
    return ApiService.get(`${this.baseUrl}/${sandboxId}/tasks/current`)
  }

  static async cancelTask(sandboxId: string, taskId: string): Promise<{
    data: { success: boolean }
    message: string
  }> {
    return ApiService.post(`${this.baseUrl}/${sandboxId}/tasks/${taskId}/cancel`)
  }

  static isExecutableCommand(command: string): boolean {
    const trimmed = command.trim()
    
    // Simple heuristics for executable commands
    const commonCommands = [
      'ls', 'pwd', 'cd', 'mkdir', 'rm', 'mv', 'cp', 'cat', 'grep', 'find',
      'git', 'npm', 'pnpm', 'yarn', 'pip', 'python', 'node', 'docker',
      'curl', 'wget', 'ssh', 'scp', 'ps', 'top', 'kill'
    ]
    
    const firstWord = trimmed.split(' ')[0].toLowerCase()
    const isCommonCommand = commonCommands.includes(firstWord)
    const isLongCommand = trimmed.split(' ').length > 3 || trimmed.length > 20
    
    return isCommonCommand || isLongCommand
  }
}
```

### 3.3 Enhanced FastAPI Adapter

**File: `voicecode-fe-1/src/lib/fastapi-adapter.ts` - Enhanced version**

```typescript
import type { Message } from 'ai'
import { ChatService } from '@/services/chat.service'
import { TaskService } from '@/services/task.service'
import type { ChatMessage, ChatMessageType } from '@/types/chat.types'
import type { Task } from '@/types/task.types'

export class FastAPIAdapter {
  constructor(_authToken?: string) {}

  // ... existing methods ...

  /**
   * Check if message should be executed as a task
   */
  isExecutableCommand(content: string): boolean {
    return TaskService.isExecutableCommand(content)
  }

  /**
   * Create task message for AI SDK
   */
  createTaskMessage(task: Task, isLive: boolean = false): Message {
    let content = `$ ${task.command}\n`
    
    if (task.logs.length > 0) {
      content += task.logs.join('\n')
    }
    
    if (!isLive) {
      content += `\n\nStatus: ${task.status}`
      if (task.exitCode !== undefined) {
        content += ` (exit code: ${task.exitCode})`
      }
      if (task.executionTime) {
        content += ` (${task.executionTime.toFixed(2)}s)`
      }
    }

    return {
      id: `task-${task.id}`,
      role: 'assistant',
      content,
      createdAt: new Date(task.createdAt),
      metadata: {
        messageType: 'task',
        taskId: task.id,
        taskStatus: task.status,
        command: task.command,
        exitCode: task.exitCode,
        logs: task.logs,
        isLive
      }
    }
  }

  /**
   * Update task message content
   */
  updateTaskMessage(message: Message, task: Task): Message {
    return {
      ...message,
      content: this.createTaskMessage(task, task.status === 'running').content,
      metadata: {
        ...message.metadata,
        taskStatus: task.status,
        exitCode: task.exitCode,
        logs: task.logs,
        isLive: task.status === 'running'
      }
    }
  }

  /**
   * Process streaming message for task updates
   */
  processTaskStreamMessage(message: Message): {
    isTaskMessage: boolean
    taskUpdate?: Partial<Task>
  } {
    const metadata = message.metadata
    
    if (!metadata?.type?.startsWith('task_')) {
      return { isTaskMessage: false }
    }

    const taskUpdate: Partial<Task> = {
      id: metadata.task_id
    }

    switch (metadata.type) {
      case 'task_start':
        taskUpdate.status = 'running'
        taskUpdate.command = metadata.command
        break
        
      case 'task_log':
        // Log will be added to content by AI SDK
        break
        
      case 'task_complete':
        taskUpdate.status = metadata.status || 'completed'
        taskUpdate.exitCode = metadata.exit_code
        taskUpdate.executionTime = metadata.execution_time
        break
        
      case 'task_error':
        taskUpdate.status = 'failed'
        break
    }

    return { isTaskMessage: true, taskUpdate }
  }
}
```

### 3.4 Enhanced useVoiceCodeChat Hook

**File: `voicecode-fe-1/src/hooks/useVoiceCodeChat.ts` - Enhanced version**

```typescript
import { useChat } from 'ai/react'
import { useState, useEffect, useCallback, useRef } from 'react'
import type { Message } from 'ai'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'
import { TaskService } from '@/services/task.service'
import type { Task } from '@/types/task.types'

interface UseVoiceCodeChatOptions {
  initialMessages?: Message[]
  onCommandExecute?: (command: string, sandboxId: string) => void
  onError?: (error: Error) => void
  onTaskUpdate?: (task: Task) => void
}

interface UseVoiceCodeChatReturn {
  messages: Message[]
  input: string
  setInput: (value: string) => void
  handleSubmit: (e?: React.FormEvent) => void
  isLoading: boolean
  isLoadingHistory: boolean
  isSendingMessage: boolean
  error: string | null
  reload: () => void
  clearHistory: () => Promise<void>
  executeCommand: (command: string) => Promise<void>
  pagination: {
    page: number
    total: number
    hasNextPage: boolean
  }
  loadMoreHistory: () => Promise<void>
  
  // Task management
  currentTask: Task | null
  isTaskRunning: boolean
  canSubmit: boolean
  cancelCurrentTask: () => Promise<void>
}

export function useVoiceCodeChat(
  sandboxId: string,
  options: UseVoiceCodeChatOptions = {}
): UseVoiceCodeChatReturn {
  const [adapter] = useState(() => new FastAPIAdapter())
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentTask, setCurrentTask] = useState<Task | null>(null)
  const [isTaskRunning, setIsTaskRunning] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    hasNextPage: false
  })
  const [historyLoaded, setHistoryLoaded] = useState(false)
  const loadingRef = useRef(false)

  // Enhanced useChat with streaming support
  const {
    messages,
    input,
    setInput,
    handleSubmit: originalHandleSubmit,
    isLoading,
    setMessages
  } = useChat({
    api: `/api/sandbox/${sandboxId}/tasks/stream`,
    initialMessages: options.initialMessages || [],
    
    onResponse: async (response) => {
      console.log('🌊 Task streaming started')
    },
    
    onFinish: (message) => {
      console.log('✅ Task streaming completed')
      
      // Process final message for task completion
      const { isTaskMessage, taskUpdate } = adapter.processTaskStreamMessage(message)
      
      if (isTaskMessage && taskUpdate) {
        if (taskUpdate.status && ['completed', 'failed', 'cancelled'].includes(taskUpdate.status)) {
          setIsTaskRunning(false)
          setCurrentTask(null)
        }
      }
    },
    
    onError: (error) => {
      console.error('❌ Task streaming error:', error)
      setError(error.message)
      setIsTaskRunning(false)
      setCurrentTask(null)
      options.onError?.(error)
    }
  })

  // Process streaming messages for task updates
  useEffect(() => {
    const lastMessage = messages[messages.length - 1]
    if (!lastMessage?.metadata?.type?.startsWith('task_')) return

    const { isTaskMessage, taskUpdate } = adapter.processTaskStreamMessage(lastMessage)
    
    if (isTaskMessage && taskUpdate && currentTask) {
      const updatedTask = { ...currentTask, ...taskUpdate }
      setCurrentTask(updatedTask)
      options.onTaskUpdate?.(updatedTask)
      
      // Update running state
      if (taskUpdate.status) {
        setIsTaskRunning(!['completed', 'failed', 'cancelled'].includes(taskUpdate.status))
      }
    }
  }, [messages, currentTask, adapter, options])

  /**
   * Load chat history (existing implementation)
   */
  const loadHistory = useCallback(async () => {
    if (loadingRef.current || historyLoaded) return
    
    loadingRef.current = true
    setIsLoadingHistory(true)
    setError(null)

    try {
      const response = await adapter.loadHistory(sandboxId, 1)
      
      setMessages(response.messages)
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
      
      setHistoryLoaded(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load history'
      setError(errorMessage)
      console.error('Failed to load chat history:', err)
    } finally {
      setIsLoadingHistory(false)
      loadingRef.current = false
    }
  }, [sandboxId, adapter, setMessages, historyLoaded])

  /**
   * Enhanced submit handler with task management
   */
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault()
    
    if (!input.trim() || isLoading || isTaskRunning) return

    const message = input.trim()
    setError(null)

    // Check if this should be executed as a task
    if (adapter.isExecutableCommand(message)) {
      // Check for existing running task
      try {
        const existingTask = await TaskService.getCurrentTask(sandboxId)
        if (existingTask.data) {
          setError('Another task is already running. Please wait for it to complete.')
          return
        }
      } catch (err) {
        // No existing task, continue
      }

      // Create initial task state
      const newTask: Task = {
        id: `temp-${Date.now()}`,
        sandboxId,
        userId: 'current-user',
        command: message,
        status: 'pending',
        logs: [],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      setCurrentTask(newTask)
      setIsTaskRunning(true)
      
      // Use AI SDK's streaming
      await originalHandleSubmit(e)
      
    } else {
      // Regular message - use existing flow
      const userMessage = adapter.createOptimisticMessage(sandboxId, message)
      setMessages(prev => [...prev, userMessage])
      setInput('')

      try {
        await adapter.sendMessage(sandboxId, message, 'user')
        const historyResponse = await adapter.loadHistory(sandboxId, 1)
        setMessages(historyResponse.messages)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to send message'
        setError(errorMessage)
        setMessages(prev => prev.filter(m => m.id !== userMessage.id))
      }
    }
  }, [input, isLoading, isTaskRunning, sandboxId, adapter, originalHandleSubmit, setInput, setMessages])

  /**
   * Cancel current task
   */
  const cancelCurrentTask = useCallback(async () => {
    if (!currentTask) return
    
    try {
      await TaskService.cancelTask(sandboxId, currentTask.id)
      setIsTaskRunning(false)
      setCurrentTask(null)
    } catch (error) {
      console.error('Failed to cancel task:', error)
      setError('Failed to cancel task')
    }
  }, [currentTask, sandboxId])

  /**
   * Execute command (backward compatibility)
   */
  const executeCommand = useCallback(async (command: string) => {
    // For backward compatibility, redirect to task execution
    setInput(command)
    await handleSubmit()
  }, [setInput, handleSubmit])

  /**
   * Clear chat history
   */
  const clearHistory = useCallback(async () => {
    try {
      await adapter.clearHistory(sandboxId)
      setMessages([])
      setPagination({
        page: 1,
        total: 0,
        hasNextPage: false
      })
      setHistoryLoaded(false)
      setCurrentTask(null)
      setIsTaskRunning(false)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history'
      setError(errorMessage)
    }
  }, [sandboxId, adapter, setMessages])

  /**
   * Load more history
   */
  const loadMoreHistory = useCallback(async () => {
    if (!pagination.hasNextPage || isLoadingHistory) return

    setIsLoadingHistory(true)
    try {
      const response = await adapter.loadHistory(sandboxId, pagination.page + 1)
      
      setMessages(prevMessages => [...response.messages, ...prevMessages])
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more history'
      setError(errorMessage)
    } finally {
      setIsLoadingHistory(false)
    }
  }, [sandboxId, adapter, pagination, isLoadingHistory, setMessages])

  /**
   * Reload conversation
   */
  const reloadConversation = useCallback(() => {
    setHistoryLoaded(false)
    loadHistory()
  }, [loadHistory])

  // Load history on mount
  useEffect(() => {
    if (sandboxId) {
      setHistoryLoaded(false)
      loadHistory()
    }
  }, [sandboxId])

  return {
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading,
    isLoadingHistory,
    isSendingMessage: isLoading || isTaskRunning,
    error,
    reload: reloadConversation,
    clearHistory,
    executeCommand,
    pagination,
    loadMoreHistory,
    
    // Task management
    currentTask,
    isTaskRunning,
    canSubmit: !isLoading && !isTaskRunning,
    cancelCurrentTask
  }
}
```

### 3.5 Task Message Component

**File: `voicecode-fe-1/src/components/TaskMessage.tsx`**

```typescript
import React, { useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Terminal, CheckCircle, XCircle, Clock, Loader2, Copy, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Task } from '@/types/task.types'

interface TaskMessageProps {
  task: Task
  isLive?: boolean
  onCancel?: () => void
  className?: string
}

export function TaskMessage({ task, isLive = false, onCancel, className }: TaskMessageProps) {
  const logsEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (isLive && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [task.logs, isLive])

  const getStatusIcon = () => {
    switch (task.status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <X className="h-4 w-4 text-gray-500" />
      case 'pending':
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    switch (task.status) {
      case 'running':
        return 'default'
      case 'completed':
        return 'secondary'
      case 'failed':
        return 'destructive'
      case 'cancelled':
      case 'pending':
      default:
        return 'outline'
    }
  }

  const copyCommand = () => {
    navigator.clipboard.writeText(task.command)
  }

  const formatExecutionTime = (seconds: number) => {
    if (seconds < 1) return `${Math.round(seconds * 1000)}ms`
    if (seconds < 60) return `${seconds.toFixed(1)}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <Card className={cn("mb-4", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Terminal className="h-4 w-4 flex-shrink-0" />
            <code className="text-sm font-mono truncate">{task.command}</code>
          </div>
          
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge variant={getStatusVariant()} className="text-xs">
              {getStatusIcon()}
              <span className="ml-1 capitalize">{task.status}</span>
            </Badge>
            
            {task.executionTime && (
              <Badge variant="outline" className="text-xs">
                {formatExecutionTime(task.executionTime)}
              </Badge>
            )}
            
            {task.exitCode !== undefined && (
              <Badge 
                variant={task.exitCode === 0 ? "secondary" : "destructive"} 
                className="text-xs"
              >
                Exit {task.exitCode}
              </Badge>
            )}
            
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={copyCommand}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>
            
            {isLive && onCancel && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onCancel}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      {task.logs.length > 0 && (
        <CardContent className="pt-0">
          <div className="bg-black text-green-400 p-3 rounded font-mono text-sm max-h-64 overflow-y-auto">
            {task.logs.map((log, index) => (
              <div key={index} className="whitespace-pre-wrap break-words">
                {log}
              </div>
            ))}
            
            {isLive && task.status === 'running' && (
              <div className="flex items-center gap-1 mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-xs opacity-70">Live output...</span>
              </div>
            )}
            
            <div ref={logsEndRef} />
          </div>
        </CardContent>
      )}
    </Card>
  )
}
```

### 3.6 Enhanced ChatView

**File: `voicecode-fe-1/src/pages/ChatView.tsx` - Enhanced version**

```typescript
import { useParams, useNavigate } from 'react-router-dom'
import { useEffect, useRef, useState } from 'react'
import { Keyboard } from '@capacitor/keyboard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Loader2, Terminal, AlertCircle, RefreshCw, Trash2, MoreVertical, Copy, CheckCircle, XCircle, Play, Square, X } from 'lucide-react'
import { useAutoAnimate } from '@formkit/auto-animate/react'
import { useShallow } from 'zustand/react/shallow'
import { useSandboxStore } from '@/store/sandbox.store'
import { TypingIndicator } from '@/components/messages/TypingIndicator'
import { TaskMessage } from '@/components/TaskMessage'
import { SandboxService } from '@/services/sandbox.service'
import { cn } from '@/lib/utils'
import { useVoiceCodeChat } from '@/hooks/useVoiceCodeChat'
import { ChatInput } from '@/components/chat/ChatInput'

export function ChatView() {
  const { sandboxId } = useParams()
  const navigate = useNavigate()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [messagesParent] = useAutoAnimate()
  
  // Enhanced chat hook with task management
  const {
    messages,
    input: inputValue,
    setInput: setInputValue,
    handleSubmit,
    isLoadingHistory,
    isSendingMessage,
    error,
    reload: loadHistory,
    clearHistory,
    currentTask,
    isTaskRunning,
    canSubmit,
    cancelCurrentTask
  } = useVoiceCodeChat(sandboxId!, {
    onCommandExecute: (command, sandboxId) => {
      console.log('Command executed:', command, 'in sandbox:', sandboxId)
    },
    onError: (error) => {
      console.error('Chat error:', error)
    },
    onTaskUpdate: (task) => {
      console.log('Task updated:', task)
    }
  })
  
  const { 
    sandbox,
    deleteSandbox,
    startSandbox,
    stopSandbox,
    loadSandbox
  } = useSandboxStore(useShallow((state) => ({
    sandbox: state.getSandbox(sandboxId!),
    deleteSandbox: state.deleteSandbox,
    startSandbox: state.startSandbox,
    stopSandbox: state.stopSandbox,
    loadSandbox: state.loadSandbox
  })))

  // Local loading states
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  
  // Auto scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, currentTask])

  // Keyboard event listeners for iOS
  useEffect(() => {
    const handleKeyboardShow = (info: any) => {
      console.log('🎹 Keyboard will show:', info.keyboardHeight)
      document.body.classList.add('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', `${info.keyboardHeight}px`)
    }

    const handleKeyboardHide = () => {
      console.log('🎹 Keyboard will hide')
      document.body.classList.remove('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', '0px')
    }

    Keyboard.addListener('keyboardWillShow', handleKeyboardShow)
    Keyboard.addListener('keyboardWillHide', handleKeyboardHide)

    return () => {
      Keyboard.removeAllListeners()
    }
  }, [])

  const handleSend = async () => {
    if (!inputValue.trim() || !canSubmit) return
    handleSubmit()
  }

  const handleDeleteSandbox = async () => {
    if (!sandboxId) return
    if (!confirm('Are you sure you want to delete this sandbox?')) return
    
    try {
      await deleteSandbox(sandboxId)
      navigate('/conversations')
    } catch (error) {
      console.error('Failed to delete sandbox:', error)
    }
  }

  const handleStartSandbox = async () => {
    if (!sandboxId || isStarting) return
    
    try {
      setIsStarting(true)
      await startSandbox(sandboxId)
      await loadSandbox(sandboxId)
    } catch (error) {
      console.error('Failed to start sandbox:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopSandbox = async () => {
    if (!sandboxId || isStopping) return
    
    try {
      setIsStopping(true)
      await stopSandbox(sandboxId)
      await loadSandbox(sandboxId)
    } catch (error) {
      console.error('Failed to stop sandbox:', error)
    } finally {
      setIsStopping(false)
    }
  }

  if (!sandbox) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Sandbox not found</p>
          <Button 
            onClick={() => navigate('/conversations')} 
            className="mt-4"
            variant="outline"
          >
            Back to Sandboxes
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="mobile-layout">
      {/* Fixed Header */}
      <div className="mobile-header">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/conversations')}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Avatar className="flex-shrink-0 h-8 w-8">
              <AvatarImage src={`/placeholder.svg?height=32&width=32&text=${sandbox.sandbox_name.slice(0, 2).toUpperCase()}`} />
              <AvatarFallback className="text-sm">{sandbox.sandbox_name.slice(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <Badge variant={sandbox.status === "running" ? "default" : "secondary"} className="text-xs">
                <Terminal className="h-3 w-3 mr-1" />
                {SandboxService.getStatusText(sandbox.status)}
              </Badge>
              
              {/* Sandbox Controls */}
              {(sandbox.status === 'stopped' || sandbox.status === 'error') && !isStopping ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStartSandbox}
                  disabled={isStarting}
                  className="h-6 px-2 text-xs"
                >
                  {isStarting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Play className="h-3 w-3" />
                  )}
                </Button>
              ) : sandbox.status === 'running' && !isStarting ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStopSandbox}
                  disabled={isStopping}
                  className="h-6 px-2 text-xs"
                >
                  {isStopping ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Square className="h-3 w-3" />
                  )}
                </Button>
              ) : null}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex-shrink-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => loadHistory()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Chat
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => clearHistory()}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear History
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDeleteSandbox}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Sandbox
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        {/* Task Status Bar */}
        {currentTask && (
          <div className="px-4 py-2 bg-blue-50 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                <span className="text-sm text-blue-700 truncate">
                  {currentTask.status === 'running' ? 'Executing' : 'Processing'}: {currentTask.command}
                </span>
              </div>
              
              {isTaskRunning && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={cancelCurrentTask}
                  className="h-6 px-2 text-xs text-red-600 hover:text-red-800"
                >
                  <X className="h-3 w-3 mr-1" />
                  Cancel
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Messages Content */}
      <div className="mobile-content">
        <div className="p-4 space-y-4 pb-28" ref={messagesParent}>
        {isLoadingHistory ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : messages.length === 0 && !currentTask ? (
          <div className="text-center py-16">
            <Terminal className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              No messages yet
            </p>
            <p className="text-sm text-gray-500">
              Type a command to get started
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => {
              const metadata = (message as any).metadata || {}
              const messageType = message.role === 'user' ? 'user' : (metadata.messageType || 'system')
              const isOwnMessage = messageType === 'user'

              // Handle task messages
              if (metadata.messageType === 'task') {
                return (
                  <TaskMessage
                    key={message.id}
                    task={metadata as any}
                    isLive={metadata.isLive}
                  />
                )
              }

              // Handle system messages with command output
              if (messageType === 'system' && metadata.exitCode !== undefined) {
                return (
                  <Card key={message.id} className="max-w-full sm:max-w-[90%]">
                    <CardHeader className="pb-2">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <Terminal className="h-4 w-4 flex-shrink-0" />
                          <code className="text-sm font-mono truncate">$ {metadata.originalCommand}</code>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge variant={metadata.exitCode === 0 ? "default" : "destructive"} className="text-xs">
                            {metadata.exitCode === 0 ? (
                              <CheckCircle className="h-3 w-3 mr-1" />
                            ) : (
                              <XCircle className="h-3 w-3 mr-1" />
                            )}
                            Exit {metadata.exitCode}
                          </Badge>
                          <Button variant="ghost" size="sm" onClick={() => navigator.clipboard.writeText(metadata.originalCommand)}>
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    {message.content && (
                      <CardContent className="pt-0">
                        <pre className="text-sm bg-muted/50 p-3 rounded whitespace-pre-wrap break-words">
                          <code>{message.content}</code>
                        </pre>
                      </CardContent>
                    )}
                  </Card>
                )
              }

              // Handle error messages
              if (messageType === 'error') {
                return (
                  <Alert key={message.id} variant="destructive">
                    <AlertDescription>{message.content}</AlertDescription>
                  </Alert>
                )
              }

              // Handle regular system messages
              if (messageType === 'system') {
                return (
                  <Alert key={message.id}>
                    <AlertDescription>{message.content}</AlertDescription>
                  </Alert>
                )
              }

              // Handle regular chat messages
              return (
                <div key={message.id} className={isOwnMessage ? "flex justify-end" : "flex justify-start"}>
                  <div className={cn(
                    "rounded-lg px-3 py-2 max-w-[85%] sm:max-w-[80%] break-words",
                    isOwnMessage 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted"
                  )}>
                    <div className="whitespace-pre-wrap">{message.content}</div>
                  </div>
                </div>
              )
            })}
            
            {/* Live task display */}
            {currentTask && (
              <TaskMessage 
                task={currentTask} 
                isLive={isTaskRunning}
                onCancel={cancelCurrentTask}
              />
            )}
          </>
        )}
        
        {isSendingMessage && !currentTask && (
          <TypingIndicator />
        )}
        
        <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed Footer - Input Area */}
      <div className="mobile-footer">
        <div className="px-4 pb-6">
          {/* Error display */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Chat Input */}
          <ChatInput
            value={inputValue}
            onChange={setInputValue}
            onSend={handleSend}
            disabled={!canSubmit}
            placeholder={
              isTaskRunning 
                ? "Task running... Please wait" 
                : "Type a command..."
            }
            isSending={isSendingMessage}
          />
        </div>
      </div>
    </div>
  )
}
```

## Phase 4: Testing & Deployment (Week 3-4)

### 4.1 Backend Tests

**File: `voicecode/fastapi-app/tests/test_task_management.py`**

```python
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from services.task_service import TaskService
from database import Task, TaskStatus

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture
def mock_db():
    return Mock(spec=Session)

@pytest.fixture
def mock_daytona_service():
    return Mock()

@pytest.fixture
def task_service(mock_daytona_service):
    return TaskService(mock_daytona_service)

class TestTaskService:
    @pytest.mark.asyncio
    async def test_create_task(self, task_service, mock_db):
        """Test task creation"""
        # Mock database operations
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        task = await task_service.create_task(
            mock_db, 
            "sandbox-123", 
            "user-456", 
            "ls -la"
        )
        
        assert task.command == "ls -la"
        assert task.status == TaskStatus.PENDING
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_task_execution_stream(self, task_service, mock_db):
        """Test task execution with streaming"""
        # Create mock task
        mock_task = Mock()
        mock_task.id = "task-123"
        mock_task.command = "echo hello"
        mock_task.status = TaskStatus.PENDING
        mock_task.logs = []
        
        # Mock Daytona service
        async def mock_daytona_stream():
            yield {'type': 'log', 'data': 'hello'}
            yield {'type': 'complete', 'exit_code': 0}
        
        task_service.daytona.execute_command_stream = AsyncMock(
            return_value=mock_daytona_stream()
        )
        
        # Execute task stream
        events = []
        async for event in task_service.execute_task_stream(mock_db, mock_task, "sandbox-123"):
            events.append(event)
        
        # Verify events
        assert len(events) >= 2  # At least status and completion events
        assert any(event.type == 'task_status' for event in events)
        assert any(event.type == 'task_complete' for event in events)

class TestTaskEndpoints:
    def test_create_task_endpoint(self, client):
        """Test task creation endpoint"""
        with patch('main.get_current_user') as mock_auth:
            mock_auth.return_value = {"user": {"user": {"id": "user-123"}}}
            
            with patch('main.redis_client') as mock_redis:
                mock_redis.get.return_value = '{"user_id": "user-123"}'
                
                with patch('main.task_service') as mock_service:
                    mock_task = Mock()
                    mock_task.id = "task-123"
                    mock_task.command = "ls -la"
                    mock_service.create_task = AsyncMock(return_value=mock_task)
                    mock_service.get_current_task = AsyncMock(return_value=None)
                    
                    response = client.post(
                        "/api/sandbox/sandbox-123/tasks",
                        json={"command": "ls -la"},
                        headers={"Authorization": "Bearer test-token"}
                    )
                    
                    assert response.status_code == 200
                    assert "task-123" in response.json()["data"]["id"]

    def test_stream_task_execution(self, client):
        """Test streaming task execution endpoint"""
        with patch('main.get_current_user') as mock_auth:
            mock_auth.return_value = {"user": {"user": {"id": "user-123"}}}
            
            with patch('main.redis_client') as mock_redis:
                mock_redis.get.return_value = '{"user_id": "user-123"}'
                
                with patch('main.task_service') as mock_service:
                    mock_task = Mock()
                    mock_task.id = "task-123"
                    mock_service.create_task = AsyncMock(return_value=mock_task)
                    mock_service.get_current_task = AsyncMock(return_value=None)
                    
                    # Mock streaming events
                    async def mock_stream():
                        from models.task_models import TaskLogEvent, TaskCompleteEvent
                        yield TaskLogEvent(
                            task_id="task-123",
                            timestamp=datetime.now(),
                            log="test output"
                        )
                        yield TaskCompleteEvent(
                            task_id="task-123",
                            timestamp=datetime.now(),
                            exit_code=0,
                            execution_time=1.5
                        )
                    
                    mock_service.execute_task_stream = AsyncMock(
                        return_value=mock_stream()
                    )
                    
                    response = client.post(
                        "/api/sandbox/sandbox-123/tasks/stream",
                        json={"command": "echo hello"},
                        headers={"Authorization": "Bearer test-token"}
                    )
                    
                    assert response.status_code == 200
                    assert "text/plain" in response.headers["content-type"]

    def test_concurrent_task_prevention(self, client):
        """Test that concurrent tasks are prevented"""
        with patch('main.get_current_user') as mock_auth:
            mock_auth.return_value = {"user": {"user": {"id": "user-123"}}}
            
            with patch('main.redis_client') as mock_redis:
                mock_redis.get.return_value = '{"user_id": "user-123"}'
                
                with patch('main.task_service') as mock_service:
                    # Mock existing running task
                    existing_task = Mock()
                    existing_task.command = "long running command"
                    mock_service.get_current_task = AsyncMock(return_value=existing_task)
                    
                    response = client.post(
                        "/api/sandbox/sandbox-123/tasks",
                        json={"command": "ls -la"},
                        headers={"Authorization": "Bearer test-token"}
                    )
                    
                    assert response.status_code == 409
                    assert "already running" in response.json()["detail"]
```

### 4.2 Frontend Tests

**File: `voicecode-fe-1/src/hooks/__tests__/useVoiceCodeChat.test.ts`**

```typescript
import { renderHook, act, waitFor } from '@testing-library/react'
import { useVoiceCodeChat } from '../useVoiceCodeChat'
import { TaskService } from '@/services/task.service'

// Mock dependencies
jest.mock('@/services/task.service')
jest.mock('ai/react', () => ({
  useChat: jest.fn(() => ({
    messages: [],
    input: '',
    setInput: jest.fn(),
    handleSubmit: jest.fn(),
    isLoading: false,
    setMessages: jest.fn()
  }))
}))

const mockTaskService = TaskService as jest.Mocked<typeof TaskService>

describe('useVoiceCodeChat', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should prevent task submission when another task is running', async () => {
    // Mock existing running task
    mockTaskService.getCurrentTask.mockResolvedValue({
      data: {
        id: 'existing-task',
        command: 'long running command',
        status: 'running'
      }
    } as any)

    const { result } = renderHook(() => useVoiceCodeChat('sandbox-123'))

    await act(async () => {
      result.current.setInput('ls -la')
      await result.current.handleSubmit()
    })

    await waitFor(() => {
      expect(result.current.error).toContain('already running')
    })
  })

  test('should handle task streaming correctly', async () => {
    mockTaskService.getCurrentTask.mockRejectedValue(new Error('No task'))
    
    const mockUseChat = require('ai/react').useChat
    const mockHandleSubmit = jest.fn()
    
    mockUseChat.mockReturnValue({
      messages: [
        {
          id: 'msg-1',
          role: 'assistant',
          content: 'Executing: ls -la',
          metadata: {
            type: 'task_start',
            task_id: 'task-123',
            command: 'ls -la'
          }
        }
      ],
      input: 'ls -la',
      setInput: jest.fn(),
      handleSubmit: mockHandleSubmit,
      isLoading: false,
      setMessages: jest.fn()
    })

    const { result } = renderHook(() => useVoiceCodeChat('sandbox-123'))

    expect(result.current.currentTask).toBeTruthy()
    expect(result.current.isTaskRunning).toBe(true)
  })

  test('should handle task completion', async () => {
    const mockUseChat = require('ai/react').useChat
    
    mockUseChat.mockReturnValue({
      messages: [
        {
          id: 'msg-1',
          role: 'assistant',
          content: 'Task completed (exit code: 0)',
          metadata: {
            type: 'task_complete',
            task_id: 'task-123',
            exit_code: 0,
            status: 'completed'
          }
        }
      ],
      input: '',
      setInput: jest.fn(),
      handleSubmit: jest.fn(),
      isLoading: false,
      setMessages: jest.fn()
    })

    const { result } = renderHook(() => useVoiceCodeChat('sandbox-123'))

    expect(result.current.isTaskRunning).toBe(false)
    expect(result.current.currentTask).toBeNull()
  })

  test('should handle task cancellation', async () => {
    mockTaskService.cancelTask.mockResolvedValue({
      data: { success: true },
      message: 'Task cancelled'
    })

    const { result } = renderHook(() => useVoiceCodeChat('sandbox-123'))

    // Set up a running task
    act(() => {
      result.current.setInput('long running command')
    })

    await act(async () => {
      await result.current.cancelCurrentTask()
    })

    expect(mockTaskService.cancelTask).toHaveBeenCalled()
    expect(result.current.isTaskRunning).toBe(false)
  })
})
```

**File: `voicecode-fe-1/src/components/__tests__/TaskMessage.test.tsx`**

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { TaskMessage } from '../TaskMessage'
import type { Task } from '@/types/task.types'

const mockTask: Task = {
  id: 'task-123',
  sandboxId: 'sandbox-123',
  userId: 'user-123',
  command: 'ls -la',
  status: 'running',
  logs: ['total 8', 'drwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .'],
  metadata: {},
  createdAt: '2024-01-01T12:00:00Z',
  updatedAt: '2024-01-01T12:00:00Z'
}

describe('TaskMessage', () => {
  test('renders task command and status', () => {
    render(<TaskMessage task={mockTask} />)
    
    expect(screen.getByText('ls -la')).toBeInTheDocument()
    expect(screen.getByText('Running')).toBeInTheDocument()
  })

  test('displays logs correctly', () => {
    render(<TaskMessage task={mockTask} />)
    
    expect(screen.getByText('total 8')).toBeInTheDocument()
    expect(screen.getByText(/drwxr-xr-x/)).toBeInTheDocument()
  })

  test('shows live indicator for running tasks', () => {
    render(<TaskMessage task={mockTask} isLive={true} />)
    
    expect(screen.getByText('Live output...')).toBeInTheDocument()
  })

  test('shows completion status for completed tasks', () => {
    const completedTask = {
      ...mockTask,
      status: 'completed' as const,
      exitCode: 0,
      executionTime: 1.5
    }
    
    render(<TaskMessage task={completedTask} />)
    
    expect(screen.getByText('Completed')).toBeInTheDocument()
    expect(screen.getByText('Exit 0')).toBeInTheDocument()
    expect(screen.getByText('1.5s')).toBeInTheDocument()
  })

  test('calls onCancel when cancel button is clicked', () => {
    const onCancel = jest.fn()
    
    render(<TaskMessage task={mockTask} isLive={true} onCancel={onCancel} />)
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    fireEvent.click(cancelButton)
    
    expect(onCancel).toHaveBeenCalled()
  })

  test('copies command to clipboard when copy button is clicked', async () => {
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn()
      }
    })
    
    render(<TaskMessage task={mockTask} />)
    
    const copyButton = screen.getByRole('button', { name: /copy/i })
    fireEvent.click(copyButton)
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('ls -la')
  })
})
```

### 4.3 Integration Tests

**File: `voicecode-fe-1/src/__tests__/integration/task-flow.test.tsx`**

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { ChatView } from '@/pages/ChatView'
import { TaskService } from '@/services/task.service'

// Mock services
jest.mock('@/services/task.service')
jest.mock('@/hooks/useVoiceCodeChat')

const mockTaskService = TaskService as jest.Mocked<typeof TaskService>

const renderChatView = () => {
  return render(
    <BrowserRouter>
      <ChatView />
    </BrowserRouter>
  )
}

describe('Task Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock useVoiceCodeChat hook
    const mockUseVoiceCodeChat = require('@/hooks/useVoiceCodeChat').useVoiceCodeChat
    mockUseVoiceCodeChat.mockReturnValue({
      messages: [],
      input: '',
      setInput: jest.fn(),
      handleSubmit: jest.fn(),
      isLoadingHistory: false,
      isSendingMessage: false,
      error: null,
      reload: jest.fn(),
      clearHistory: jest.fn(),
      currentTask: null,
      isTaskRunning: false,
      canSubmit: true,
      cancelCurrentTask: jest.fn()
    })
  })

  test('complete task execution flow', async () => {
    const mockSetInput = jest.fn()
    const mockHandleSubmit = jest.fn()
    const mockCancelTask = jest.fn()
    
    // Mock initial state - no running task
    mockTaskService.getCurrentTask.mockRejectedValue(new Error('No task'))
    
    const mockUseVoiceCodeChat = require('@/hooks/useVoiceCodeChat').useVoiceCodeChat
    mockUseVoiceCodeChat.mockReturnValue({
      messages: [],
      input: 'ls -la',
      setInput: mockSetInput,
      handleSubmit: mockHandleSubmit,
      isLoadingHistory: false,
      isSendingMessage: false,
      error: null,
      reload: jest.fn(),
      clearHistory: jest.fn(),
      currentTask: null,
      isTaskRunning: false,
      canSubmit: true,
      cancelCurrentTask: mockCancelTask
    })

    renderChatView()

    // Find and interact with chat input
    const input = screen.getByPlaceholderText(/type a command/i)
    const sendButton = screen.getByRole('button', { name: /send/i })

    // Type command
    fireEvent.change(input, { target: { value: 'ls -la' } })
    expect(mockSetInput).toHaveBeenCalledWith('ls -la')

    // Submit command
    fireEvent.click(sendButton)
    expect(mockHandleSubmit).toHaveBeenCalled()
  })

  test('prevents submission when task is running', async () => {
    const mockUseVoiceCodeChat = require('@/hooks/useVoiceCodeChat').useVoiceCodeChat
    mockUseVoiceCodeChat.mockReturnValue({
      messages: [],
      input: 'ls -la',
      setInput: jest.fn(),
      handleSubmit: jest.fn(),
      isLoadingHistory: false,
      isSendingMessage: true,
      error: null,
      reload: jest.fn(),
      clearHistory: jest.fn(),
      currentTask: {
        id: 'task-123',
        command: 'long running command',
        status: 'running',
        logs: []
      },
      isTaskRunning: true,
      canSubmit: false,
      cancelCurrentTask: jest.fn()
    })

    renderChatView()

    // Input should be disabled
    const input = screen.getByPlaceholderText(/task running/i)
    expect(input).toBeDisabled()

    // Should show task status
    expect(screen.getByText(/executing/i)).toBeInTheDocument()
    expect(screen.getByText('long running command')).toBeInTheDocument()
  })

  test('allows task cancellation', async () => {
    const mockCancelTask = jest.fn()
    
    const mockUseVoiceCodeChat = require('@/hooks/useVoiceCodeChat').useVoiceCodeChat
    mockUseVoiceCodeChat.mockReturnValue({
      messages: [],
      input: '',
      setInput: jest.fn(),
      handleSubmit: jest.fn(),
      isLoadingHistory: false,
      isSendingMessage: true,
      error: null,
      reload: jest.fn(),
      clearHistory: jest.fn(),
      currentTask: {
        id: 'task-123',
        command: 'long running command',
        status: 'running',
        logs: ['Starting execution...']
      },
      isTaskRunning: true,
      canSubmit: false,
      cancelCurrentTask: mockCancelTask
    })

    renderChatView()

    // Find and click cancel button
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    fireEvent.click(cancelButton)

    expect(mockCancelTask).toHaveBeenCalled()
  })
})
```

## Phase 5: Deployment & Monitoring (Week 4)

### 5.1 Environment Configuration

**File: `voicecode/fastapi-app/.env.example` - Add task management variables**

```bash
# Existing variables...

# Task Management
TASK_EXECUTION_TIMEOUT=300  # 5 minutes
TASK_LOG_RETENTION_DAYS=7
TASK_MAX_CONCURRENT_PER_USER=1
TASK_STREAM_BUFFER_SIZE=1024

# Performance
TASK_POLLING_INTERVAL=0.5  # seconds
TASK_CLEANUP_INTERVAL=3600  # 1 hour
```

### 5.2 Monitoring & Logging

**File: `voicecode/fastapi-app/monitoring/task_metrics.py`**

```python
import time
from typing import Dict, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque

class TaskMetrics:
    def __init__(self):
        self.task_counts = defaultdict(int)
        self.execution_times = deque(maxlen=1000)
        self.error_counts = defaultdict(int)
        self.active_tasks = {}
        
    def record_task_start(self, task_id: str, command: str):
        """Record task start"""
        self.task_counts['started'] += 1
        self.active_tasks[task_id] = {
            'command': command,
            'start_time': time.time(),
            'status': 'running'
        }
        
    def record_task_complete(self, task_id: str, exit_code: int, execution_time: float):
        """Record task completion"""
        self.task_counts['completed'] += 1
        self.execution_times.append(execution_time)
        
        if exit_code == 0:
            self.task_counts['successful'] += 1
        else:
            self.task_counts['failed'] += 1
            
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
    
    def record_task_error(self, task_id: str, error_type: str):
        """Record task error"""
        self.error_counts[error_type] += 1
        self.task_counts['errored'] += 1
        
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        avg_execution_time = (
            sum(self.execution_times) / len(self.execution_times)
            if self.execution_times else 0
        )
        
        return {
            'task_counts': dict(self.task_counts),
            'active_tasks': len(self.active_tasks),
            'average_execution_time': avg_execution_time,
            'error_counts': dict(self.error_counts),
            'timestamp': datetime.now().isoformat()
        }

# Global metrics instance
task_metrics = TaskMetrics()
```

### 5.3 Production Deployment

**File: `docker-compose.prod.yml` - Add task management services**

```yaml
version: '3.8'

services:
  # Existing services...
  
  fastapi-app:
    # Existing configuration...
    environment:
      # Existing environment variables...
      - TASK_EXECUTION_TIMEOUT=300
      - TASK_LOG_RETENTION_DAYS=7
      - TASK_MAX_CONCURRENT_PER_USER=1
      - TASK_STREAM_BUFFER_SIZE=1024
      - TASK_POLLING_INTERVAL=0.5
      - TASK_CLEANUP_INTERVAL=3600
    
    # Add health check for task management
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Task cleanup service
  task-cleanup:
    build:
      context: ./voicecode/fastapi-app
      dockerfile: Dockerfile
    command: python -m scripts.task_cleanup
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - TASK_LOG_RETENTION_DAYS=7
    depends_on:
      - postgres
    restart: unless-stopped
```

### 5.4 Cleanup Scripts

**File: `voicecode/fastapi-app/scripts/task_cleanup.py`**

```python
#!/usr/bin/env python3
"""
Task cleanup script - removes old completed tasks and logs
"""

import os
import time
from datetime import datetime, timedelta
from sqlalchemy import create_engine, and_
from sqlalchemy.orm import sessionmaker

from database import Task, TaskStatus

def cleanup_old_tasks():
    """Clean up old completed tasks"""
    
    # Get retention period from environment
    retention_days = int(os.getenv('TASK_LOG_RETENTION_DAYS', '7'))
    cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
    
    # Create database connection
    database_url = os.getenv('DATABASE_URL')
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(bind=engine)
    
    with SessionLocal() as db:
        # Delete old completed/failed/cancelled tasks
        deleted_count = db.query(Task).filter(
            and_(
                Task.status.in_([TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]),
                Task.completed_at < cutoff_date
            )
        ).delete()
        
        db.commit()
        
        print(f"Cleaned up {deleted_count} old tasks (older than {retention_days} days)")

def main():
    """Main cleanup loop"""
    cleanup_interval = int(os.getenv('TASK_CLEANUP_INTERVAL', '3600'))  # 1 hour
    
    print(f"Starting task cleanup service (interval: {cleanup_interval}s)")
    
    while True:
        try:
            cleanup_old_tasks()
        except Exception as e:
            print(f"Error during cleanup: {e}")
        
        time.sleep(cleanup_interval)

if __name__ == "__main__":
    main()
```

## Summary

This detailed implementation plan provides:

1. **Complete backend infrastructure** with task management, streaming, and database schema
2. **Enhanced frontend integration** leveraging AI SDK streaming capabilities
3. **Comprehensive testing strategy** covering unit, integration, and end-to-end tests
4. **Production deployment configuration** with monitoring and cleanup services
5. **Progressive migration path** maintaining backward compatibility

The implementation ensures:
- ✅ One task at a time enforcement
- ✅ Real-time log streaming from Daytona
- ✅ Seamless AI SDK integration
- ✅ Robust error handling and recovery
- ✅ Production-ready monitoring and cleanup
- ✅ Comprehensive test coverage

This architecture provides the foundation for a robust, scalable task management system that enhances the user experience while maintaining system reliability.