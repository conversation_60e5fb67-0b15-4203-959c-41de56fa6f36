# Docker Setup Documentation

This document describes the comprehensive Docker setup for VoiceCode, including separate configurations for local development and production environments.

## Environment Structure

### Development Environment
- **File**: `docker-compose.dev.yml`
- **Features**:
  - Hot reloading for both frontend and backend
  - Volume mounts for live code updates
  - Development tools and debugging capabilities
  - Exposed ports for direct service access

### Production Environment
- **File**: `docker-compose.prod.yml`
- **Features**:
  - Optimized multi-stage builds
  - No volume mounts (security)
  - Resource limits
  - Health checks
  - Production-grade configurations

## Quick Start

### Development

```bash
# Start development environment
./scripts/docker-dev.sh up

# View logs
./scripts/docker-dev.sh logs

# Stop environment
./scripts/docker-dev.sh down
```

### Production

```bash
# Deploy production environment
./scripts/docker-prod.sh deploy

# Check health status
./scripts/docker-prod.sh health

# Create backup
./scripts/docker-prod.sh backup
```

## Services

The Docker Compose setup includes four services:

### React PWA Frontend (`voicecode-pwa`)
- **Development Port**: 3000 (with Vite dev server)
- **Production Port**: 80 (with nginx)
- **Hot Reloading**: Enabled in development
- **Production**: Optimized build with nginx

### FastAPI Application (`voicecode-api`)
- **Port**: 9100 (host) → 9100 (container)
- **Hot Reloading**: Enabled for development
- **Health Check**: `http://localhost:9100/health`
- **Production**: Multi-worker setup

### PostgreSQL Database (`postgres`)
- **Development Port**: 5432
- **Production Port**: 5432
- **Database**: `voicecode`
- **User**: Configured via environment
- **Data Persistence**: Volume mounted

### Redis Cache (`redis`)
- **Development Port**: 6379
- **Production Port**: 6379
- **Configuration**: Custom redis.conf
- **Production**: Password protected

## Environment Configuration

### Files
- **`.env`**: Base environment variables (copy from `.env.example`)
- **`docker-compose.dev.yml`**: Development orchestration
- **`docker-compose.prod.yml`**: Production orchestration
- **`.env.docker`**: Development application variables
- **`.env.production`**: Production application variables

### Service URLs

#### Development
- Frontend: http://localhost:3000
- Backend API: http://localhost:9100
- PostgreSQL: localhost:5432
- Redis: localhost:6379

#### Production
- Frontend: http://localhost:80
- Backend API: http://localhost:9100

### Key Differences

#### Volume Mounts
- **Development**: Code directories are mounted for hot reloading
- **Production**: No mounts, code is baked into images

#### Build Process
- **Development**: Single-stage builds with dev dependencies
- **Production**: Multi-stage builds for smaller images

#### Performance
- **Development**: Optimized for developer experience
- **Production**: Optimized for performance and security

## Development Workflow

### Starting Services
```bash
# Start all services
docker compose up -d

# Start with logs
docker compose up

# Start specific service
docker compose up postgres redis
```

### Monitoring Services
```bash
# Check service status
docker compose ps

# View logs
docker compose logs -f

# View specific service logs
docker compose logs -f voicecode-api
```

### Managing Services
```bash
# Stop services
docker compose down

# Restart services
docker compose restart

# Rebuild and restart
docker compose up --build -d
```

### Database Operations
```bash
# Connect to PostgreSQL
docker compose exec postgres psql -U voicecode -d voicecode

# Run database migrations
docker compose exec voicecode-api alembic upgrade head

# Check database connection
docker compose exec postgres pg_isready -U voicecode -d voicecode
```

### Redis Operations
```bash
# Connect to Redis CLI
docker compose exec redis redis-cli

# Check Redis connection
docker compose exec redis redis-cli ping

# Monitor Redis commands
docker compose exec redis redis-cli monitor
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 9100, 9379, and 9432 are not in use
2. **Permission issues**: Run `chmod 755 data/postgres data/redis`
3. **Environment variables**: Verify `.env.docker` file exists and is properly configured

### Health Checks
```bash
# Check all service health
docker compose ps

# Test API health endpoint
curl http://localhost:9100/health

# Test database connection
docker compose exec postgres pg_isready -U voicecode -d voicecode

# Test Redis connection
docker compose exec redis redis-cli ping
```

### Logs and Debugging
```bash
# View all logs
docker compose logs

# Follow logs in real-time
docker compose logs -f

# View specific service logs
docker compose logs voicecode-api
docker compose logs postgres
docker compose logs redis

# Debug container issues
docker compose exec voicecode-api bash
```

## Data Persistence

- **PostgreSQL data**: Stored in `./data/postgres`
- **Redis data**: Stored in `./data/redis`
- **Application code**: Live-mounted from `./voicecode/fastapi-app`

Data persists across container restarts and rebuilds.

## Configuration Customization

### Changing Ports
Edit `.env` file:
```env
POSTGRES_HOST_PORT=5432
REDIS_HOST_PORT=6379
API_HOST_PORT=8000
```

### Environment-Specific Settings
Edit `voicecode/fastapi-app/.env.docker`:
```env
ENVIRONMENT=production
DEBUG=false
UVICORN_RELOAD=false
```

### Database Configuration
```env
POSTGRES_DB=myapp
POSTGRES_USER=myuser
POSTGRES_PASSWORD=mypassword
```

## Docker Files Structure

```
/
├── docker-compose.dev.yml      # Development orchestration
├── docker-compose.prod.yml     # Production orchestration
├── .env.example               # Environment template
├── scripts/
│   ├── docker-dev.sh          # Development helper
│   └── docker-prod.sh         # Production helper
├── voicecode-pwa/
│   ├── Dockerfile             # Production build
│   ├── Dockerfile.dev         # Development build
│   └── nginx.conf            # Production web server
└── voicecode/fastapi-app/
    ├── Dockerfile             # Multi-stage build
    ├── Dockerfile.dev         # Development build
    └── requirements-dev.txt   # Dev dependencies
```

## Scripts

### Development Helper (`docker-dev.sh`)
- `up`: Start development environment
- `down`: Stop development environment
- `restart`: Restart services
- `logs [service]`: View logs
- `build`: Build development images
- `clean`: Remove volumes and containers
- `ps`: List running services
- `exec [service] [command]`: Execute commands

### Production Helper (`docker-prod.sh`)
- `up`: Start production environment
- `down`: Stop production environment
- `deploy`: Full deployment cycle
- `health`: Check service health
- `backup`: Create database backup

## Integration with Existing Workflow

The Docker setup is designed to work alongside existing development workflows:

- **Local development**: Use `.env` for local Python development
- **Docker development**: Use `.env.docker` for containerized development
- **Testing**: Both environments support the same API endpoints
- **Hot reloading**: Code changes are reflected immediately in Docker

## Best Practices

1. **Development**: Always use volume mounts for faster iteration
2. **Production**: Never use volume mounts to avoid conflicts
3. **Secrets**: Use environment variables, never commit secrets
4. **Health Checks**: Always configured for production
5. **Resource Limits**: Set in production to prevent resource exhaustion

## Migration from Old Setup

If you were using the old single `docker-compose.yml`:

1. Stop old containers: `docker-compose down`
2. Copy environment files: `cp .env.example .env`
3. Start new development: `./scripts/docker-dev.sh up`
4. Run migrations if needed

## Next Steps

After setting up Docker:

1. Copy environment configuration: `cp .env.example .env`
2. Start development: `./scripts/docker-dev.sh up`
3. Run database migrations: `./scripts/docker-dev.sh exec voicecode-api python run-migrations.py`
4. Access frontend: http://localhost:3000
5. Test API health: `curl http://localhost:9100/health`