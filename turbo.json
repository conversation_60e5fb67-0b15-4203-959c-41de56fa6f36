{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"dependsOn": ["^lint:fix"]}, "type-check": {"dependsOn": ["^type-check"]}, "test": {"dependsOn": ["^test"]}, "test:run": {"dependsOn": ["^test:run"]}, "test:coverage": {"dependsOn": ["^test:coverage"]}, "format": {"dependsOn": ["^format"]}, "format:check": {"dependsOn": ["^format:check"]}}}