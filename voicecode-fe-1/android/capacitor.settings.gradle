// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../../node_modules/.pnpm/@capacitor+android@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/android/capacitor')

include ':capacitor-community-http'
project(':capacitor-community-http').projectDir = new File('../../node_modules/.pnpm/@capacitor-community+http@1.4.1/node_modules/@capacitor-community/http/android')

include ':capacitor-community-speech-recognition'
project(':capacitor-community-speech-recognition').projectDir = new File('../../node_modules/.pnpm/@capacitor-community+speech-recognition@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor-community/speech-recognition/android')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/app/android')

include ':capacitor-browser'
project(':capacitor-browser').projectDir = new File('../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/browser/android')

include ':capacitor-keyboard'
project(':capacitor-keyboard').projectDir = new File('../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/keyboard/android')

include ':capacitor-network'
project(':capacitor-network').projectDir = new File('../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/network/android')

include ':capacitor-preferences'
project(':capacitor-preferences').projectDir = new File('../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/preferences/android')
