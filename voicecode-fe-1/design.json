{"theme": {"metadata": {"name": "VoiceCode Theme Configuration", "version": "1.0.0", "description": "Comprehensive theme customization for VoiceCode mobile app"}, "branding": {"appName": "VoiceCode", "tagline": "Voice-powered development", "logo": {"primary": "/path/to/logo.svg", "icon": "/path/to/icon.svg", "favicon": "/path/to/favicon.ico"}}, "colors": {"light": {"primary": {"50": "#f0f9ff", "100": "#e0f2fe", "500": "#007AFF", "600": "#0056b3", "900": "#003d82", "foreground": "#ffffff"}, "secondary": {"50": "#f8fafc", "100": "#f1f5f9", "500": "#64748b", "600": "#475569", "foreground": "#0f172a"}, "accent": {"50": "#fef7ff", "100": "#fce7ff", "500": "#a855f7", "600": "#9333ea", "foreground": "#ffffff"}, "success": {"50": "#f0fdf4", "500": "#22c55e", "600": "#16a34a", "foreground": "#ffffff"}, "warning": {"50": "#fffbeb", "500": "#f59e0b", "600": "#d97706", "foreground": "#ffffff"}, "error": {"50": "#fef2f2", "500": "#ef4444", "600": "#dc2626", "foreground": "#ffffff"}, "background": {"primary": "#ffffff", "secondary": "#f8fafc", "tertiary": "#f1f5f9"}, "surface": {"primary": "#ffffff", "secondary": "#f8fafc", "elevated": "#ffffff"}, "text": {"primary": "#0f172a", "secondary": "#64748b", "tertiary": "#94a3b8", "inverse": "#ffffff"}, "border": {"primary": "#e2e8f0", "secondary": "#cbd5e1", "focus": "#007AFF"}}, "dark": {"primary": {"50": "#1e3a8a", "100": "#1e40af", "500": "#007AFF", "600": "#3b82f6", "900": "#60a5fa", "foreground": "#ffffff"}, "secondary": {"50": "#0f172a", "100": "#1e293b", "500": "#64748b", "600": "#94a3b8", "foreground": "#f8fafc"}, "accent": {"50": "#581c87", "100": "#7c3aed", "500": "#a855f7", "600": "#c084fc", "foreground": "#ffffff"}, "success": {"50": "#14532d", "500": "#22c55e", "600": "#4ade80", "foreground": "#ffffff"}, "warning": {"50": "#92400e", "500": "#f59e0b", "600": "#fbbf24", "foreground": "#000000"}, "error": {"50": "#991b1b", "500": "#ef4444", "600": "#f87171", "foreground": "#ffffff"}, "background": {"primary": "#0f172a", "secondary": "#1e293b", "tertiary": "#334155"}, "surface": {"primary": "#1e293b", "secondary": "#334155", "elevated": "#475569"}, "text": {"primary": "#f8fafc", "secondary": "#cbd5e1", "tertiary": "#94a3b8", "inverse": "#0f172a"}, "border": {"primary": "#334155", "secondary": "#475569", "focus": "#007AFF"}}, "voiceCode": {"messageUser": "#007AFF", "messageSystem": "#E5E5EA", "messageSystemDark": "#2C2C2E", "messageError": "#FF3B30", "codeBackground": "#1E1E1E", "voiceActive": "#00C851", "voiceInactive": "#6c757d", "sandboxActive": "#17a2b8", "githubIntegration": "#24292e"}}, "typography": {"fontFamilies": {"primary": "Inter, system-ui, -apple-system, sans-serif", "secondary": "SF Pro Display, system-ui, sans-serif", "mono": "SF Mono, Consolas, Monaco, monospace", "display": "Inter Display, system-ui, sans-serif"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem"}, "fontWeights": {"light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800}, "lineHeights": {"tight": 1.25, "normal": 1.5, "relaxed": 1.75}, "letterSpacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em"}}, "spacing": {"scale": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "mobile": {"touchTarget": "48px", "primaryAction": "72px", "messagePadding": "12px", "safeAreaTop": "env(safe-area-inset-top)", "safeAreaBottom": "env(safe-area-inset-bottom)"}}, "borderRadius": {"none": "0", "sm": "0.25rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgb(0 0 0 / 0.05)", "md": "0 4px 6px -1px rgb(0 0 0 / 0.1)", "lg": "0 10px 15px -3px rgb(0 0 0 / 0.1)", "xl": "0 20px 25px -5px rgb(0 0 0 / 0.1)", "glow": "0 0 20px rgb(0 122 255 / 0.3)"}, "animations": {"durations": {"fast": "150ms", "normal": "300ms", "slow": "500ms"}, "easings": {"easeOut": "cubic-bezier(0.0, 0.0, 0.2, 1)", "easeIn": "cubic-bezier(0.4, 0.0, 1, 1)", "easeInOut": "cubic-bezier(0.4, 0.0, 0.2, 1)", "bounce": "cubic-bezier(0.68, -0.55, 0.265, 1.55)"}, "keyframes": {"slideUp": {"from": {"opacity": 0, "transform": "translateY(20px)"}, "to": {"opacity": 1, "transform": "translateY(0)"}}, "fadeIn": {"from": {"opacity": 0}, "to": {"opacity": 1}}, "scaleIn": {"from": {"opacity": 0, "transform": "scale(0.95)"}, "to": {"opacity": 1, "transform": "scale(1)"}}, "pulse": {"0%, 100%": {"opacity": 1}, "50%": {"opacity": 0.5}}}}, "components": {"button": {"variants": {"primary": {"background": "primary.500", "color": "primary.foreground", "hoverBackground": "primary.600", "shadow": "md"}, "secondary": {"background": "secondary.100", "color": "secondary.600", "hoverBackground": "secondary.200"}, "voice": {"background": "voiceCode.voiceActive", "color": "white", "hoverBackground": "voiceCode.voiceActive", "activeScale": "0.95", "shadow": "glow"}}, "sizes": {"sm": {"height": "2rem", "padding": "0.5rem 0.75rem", "fontSize": "sm"}, "md": {"height": "2.5rem", "padding": "0.75rem 1rem", "fontSize": "base"}, "lg": {"height": "3rem", "padding": "1rem 1.5rem", "fontSize": "lg"}, "touch": {"minHeight": "48px", "minWidth": "48px"}}}, "card": {"variants": {"default": {"background": "surface.primary", "border": "border.primary", "shadow": "md", "borderRadius": "xl"}, "elevated": {"background": "surface.elevated", "shadow": "lg", "borderRadius": "xl"}, "message": {"background": "surface.secondary", "borderRadius": "lg", "padding": "messagePadding"}}}, "input": {"variants": {"default": {"background": "surface.primary", "border": "border.primary", "focusBorder": "border.focus", "borderRadius": "lg", "fontSize": "base"}, "voice": {"background": "surface.secondary", "border": "voiceCode.voiceActive", "shadow": "glow"}}}}, "layout": {"maxWidth": {"mobile": "100%", "tablet": "768px", "desktop": "1024px"}, "breakpoints": {"xs": "375px", "sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px"}, "grid": {"columns": 12, "gap": "1rem"}}, "effects": {"blur": {"sm": "4px", "md": "8px", "lg": "16px"}, "gradients": {"primary": "linear-gradient(135deg, primary.500, primary.600)", "accent": "linear-gradient(135deg, accent.500, accent.600)", "voice": "linear-gradient(135deg, voiceCode.voiceActive, voiceCode.sandboxActive)"}, "glassmorphism": {"background": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)"}}, "accessibility": {"focusRing": {"width": "3px", "color": "border.focus", "style": "solid", "offset": "2px"}, "contrast": {"minimum": 4.5, "enhanced": 7}, "reducedMotion": {"respectPreference": true, "fallbackDuration": "0ms"}}, "customProperties": {"voiceCode": {"messageMaxWidth": "70%", "codeBlockWidth": "90%", "voiceButtonSize": "72px", "animationQuick": "200ms", "animationNormal": "300ms"}}}}