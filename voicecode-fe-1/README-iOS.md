# iOS Development Guide

This guide covers building and running the VoiceCode app in the iOS simulator.

## Prerequisites

- **Xcode**: Install from the Mac App Store or Apple Developer portal
- **Xcode Command Line Tools**: `xcode-select --install`
- **Node.js**: Version 18+ recommended
- **pnpm**: `npm install -g pnpm`
- **Capacitor CLI**: Installed via project dependencies

## Initial Setup

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Install iOS platform** (if not already added):
   ```bash
   npx cap add ios
   ```

## Building and Running

### Quick Start Commands (Recommended)

**For iOS Simulator with Live Reload:**
```bash
pnpm run ios:simulator    # Configure for simulator
pnpm run dev             # Start dev server
pnpm run dev:ios         # Run with live reload
```

**For Physical Device:**
```bash
pnpm run ios:device      # Configure for device
pnpm run cap:sync        # Build and sync
npx cap open ios         # Open in Xcode, then run
```

**Check Current Configuration:**
```bash
pnpm run ios:config      # Show current mode
```

### Manual Configuration

#### Method 1: iOS Simulator with Live Reload

1. **Configure for simulator**:
   ```bash
   pnpm run ios:simulator
   ```

2. **Start development server**:
   ```bash
   pnpm run dev
   ```

3. **Run with live reload**:
   ```bash
   pnpm run dev:ios
   ```

#### Method 2: Physical Device (iOS 18.6+)

1. **Configure for device**:
   ```bash
   pnpm run ios:device
   ```

2. **Connect your iPhone/iPad** via USB

3. **Build and sync**:
   ```bash
   pnpm run cap:sync
   ```

4. **Open in Xcode**:
   ```bash
   npx cap open ios
   ```

5. **Configure code signing**:
   - Select your device in the device dropdown
   - In Xcode, select the "App" target
   - Go to "Signing & Capabilities" tab
   - Select your Apple Developer Team
   - Ensure "Automatically manage signing" is checked

6. **Trust the developer certificate on your device**:
   - Settings → General → VPN & Device Management
   - Find your developer certificate and tap "Trust"

7. **Run on device**:
   - Click the "Play" button or press `Cmd+R`
   - The app will install and launch on your device

## Available Scripts

### Configuration Scripts
- `pnpm run ios:simulator` - Configure for iOS Simulator with live reload
- `pnpm run ios:device` - Configure for physical device deployment
- `pnpm run ios:config` - Show current configuration mode

### Build & Run Scripts
- `pnpm run build` - Build the web app for production
- `pnpm run cap:sync` - Build and sync to native platforms
- `pnpm run dev:ios` - Build and run iOS with live reload
- `npx cap open ios` - Open project in Xcode
- `npx cap run ios` - Build and run on iOS simulator
- `npx cap run ios --target="iPhone 15"` - Run on specific simulator
- `npx cap run ios --target="Your Device Name"` - Run on connected physical device

### One-Command Workflows
- `pnpm run ios:sim` - Configure for simulator + start dev server + run with live reload
- `pnpm run ios:dev` - Configure for device + build & sync + open Xcode

## Configuration

### Environment Variables

The app uses these environment variables (`.env.local`):

```env
VITE_API_BASE_URL=http://localhost:9100
VITE_ENABLE_STREAMING=true
VITE_STREAMING_TIMEOUT=30000
VITE_STREAMING_RETRY_COUNT=3
```

**Important**: Use `localhost` instead of IP addresses for iOS simulator compatibility.

### Automatic Configuration

The project includes automatic configuration switching:

**Current Mode**: Run `pnpm run ios:config` to see current configuration

**Simulator Mode** (`pnpm run ios:simulator`):
```typescript
server: {
  url: 'http://localhost:9080',  // Dev server enabled
  cleartext: true,
  iosScheme: 'http'
}
```

**Device Mode** (`pnpm run ios:device`):
```typescript
server: {
  // url: 'http://localhost:9080',  // Dev server disabled
  cleartext: true,
  iosScheme: 'http'
}
```

**What gets updated automatically**:
- `capacitor.config.ts` - Dev server URL enabled/disabled
- `.env.local` - API base URL configuration
  - **Simulator**: `http://localhost:9100`
  - **Device**: `http://[YOUR_IP]:9100` (auto-detected)
- Provides next steps for each mode
- Shows detected IP address for device mode

## Debugging

### Safari Web Inspector

1. **Enable debugging** (already enabled in config):
   ```typescript
   webContentsDebuggingEnabled: true
   ```

2. **Access Safari debugger**:
   - Run app in iOS simulator
   - Open Safari → Develop → [Simulator Name] → localhost
   - Use browser dev tools to debug

### Console Logs

View logs in Xcode console or Safari Web Inspector. The app includes detailed logging for:
- API requests and responses
- Authentication flow
- Network errors and retries

### Common Issues

1. **Network Error (ERR_NETWORK)**:
   - Ensure backend is running on `localhost:9100`
   - Check `.env.local` uses `localhost` not IP addresses
   - Verify firewall isn't blocking connections

2. **Build Failures**:
   ```bash
   # Clean and rebuild
   pnpm run build
   npx cap sync ios
   ```

3. **Simulator Not Starting**:
   - Restart Xcode
   - Reset iOS Simulator: Device → Erase All Content and Settings

4. **Device Deployment Issues**:
   - **"iOS 18.6 not supported"**: The deployment target has been updated to iOS 18.0+
   - **Code signing errors**: Ensure you have a valid Apple Developer account
   - **"Untrusted Developer"**: Trust the certificate in Settings → General → VPN & Device Management
   - **Device not recognized**: Ensure device is unlocked and "Trust This Computer" is selected

5. **Network Issues on Physical Device**:
   - Device must be on the same WiFi network as your development machine
   - Backend must be accessible from your local network
   - Consider using your machine's IP address instead of localhost for device testing

## Backend Requirements

The iOS app connects to a backend API. Ensure it's running:

```bash
# Backend should be accessible at:
curl http://localhost:9100/api/auth/github
```

## File Structure

```
ios/
├── App/
│   ├── App/                    # iOS app source
│   │   ├── Assets.xcassets/   # App icons and images
│   │   ├── Base.lproj/        # Storyboards
│   │   ├── AppDelegate.swift  # iOS app delegate
│   │   └── Info.plist         # iOS app configuration
│   ├── App.xcodeproj/         # Xcode project file
│   └── Podfile               # CocoaPods dependencies
└── .gitignore
```

## Production Build

For App Store or TestFlight distribution:

1. **Update configuration**:
   ```typescript
   // capacitor.config.ts
   ios: {
     buildOptions: {
       signingStyle: 'automatic',
       exportMethod: 'app-store'  // or 'ad-hoc' for TestFlight
     }
   }
   ```

2. **Build for production**:
   ```bash
   pnpm run build:prod
   npx cap sync ios
   npx cap open ios
   ```

3. **Archive in Xcode**:
   - Product → Archive
   - Follow Xcode's distribution workflow

## Troubleshooting

### Reset Everything

If you encounter persistent issues:

```bash
# Clean build
rm -rf dist/
rm -rf ios/App/App/public/
pnpm run build
npx cap sync ios

# Reset simulator
# iOS Simulator → Device → Erase All Content and Settings
```

### Update Capacitor

```bash
# Update to latest Capacitor
npx cap update ios
```

### Check Capacitor Doctor

```bash
npx cap doctor ios
```

## Additional Resources

- [Capacitor iOS Documentation](https://capacitorjs.com/docs/ios)
- [Xcode Documentation](https://developer.apple.com/xcode/)
- [iOS Simulator Guide](https://developer.apple.com/documentation/xcode/running-your-app-in-the-simulator)