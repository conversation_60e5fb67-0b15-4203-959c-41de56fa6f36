{"name": "voicecode-fe-1", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "preview": "vite preview --host", "type-check": "tsc --noEmit", "lint": "eslint .", "cap:sync": "pnpm run build && npx cap sync", "cap:ios": "pnpm run cap:sync && npx cap open ios", "cap:android": "pnpm run cap:sync && npx cap open android", "cap:run:ios": "pnpm run cap:sync && npx cap run ios", "cap:run:android": "pnpm run cap:sync && npx cap run android", "cap:clean": "npx cap clean", "dev:ios": "pnpm run build && npx cap run ios --live-reload", "dev:android": "pnpm run build && npx cap run android --live-reload", "build:prod": "pnpm run type-check && pnpm run lint && pnpm run build", "cap:build": "pnpm run build:prod && npx cap sync", "ios:simulator": "node scripts/ios-config.cjs simulator", "ios:device": "node scripts/ios-config.cjs device", "ios:config": "node scripts/ios-config.cjs show", "ios:sim": "pnpm run ios:simulator && pnpm run dev && pnpm run dev:ios", "ios:dev": "pnpm run ios:device && pnpm run cap:sync && npx cap open ios"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@capacitor-community/http": "^1.4.1", "@capacitor-community/speech-recognition": "^7.0.1", "@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/ios": "^7.4.2", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@faker-js/faker": "^9.9.0", "@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2", "@tanstack/react-virtual": "^3.13.12", "ai": "^4.3.19", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next-themes": "^0.4.4", "prism-react-renderer": "^2.4.1", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "latest", "recharts": "2.15.0", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}