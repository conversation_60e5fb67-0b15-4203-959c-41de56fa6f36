# AGENTS.md

## Build/Test Commands
- **Build**: `pnpm run build` (TypeScript compile + Vite build)
- **Dev**: `pnpm run dev` (Vite dev server on port 9080)
- **Preview**: `pnpm run preview` (Preview production build)
- **Lint**: `pnpm run lint` (ESLint check)
- **Type Check**: `pnpm run type-check` (TypeScript type checking without emit)
- **Production Build**: `pnpm run build:prod` (type-check + lint + build)
- **Capacitor Sync**: `pnpm run cap:sync` (build + sync to native platforms)
- **iOS Dev**: `pnpm run dev:ios` (build + run iOS with live reload)
- **Android Dev**: `pnpm run dev:android` (build + run Android with live reload)

## Code Style Guidelines
- **TypeScript**: Strict mode, use `@/` path aliases for src imports
- **React**: Functional components with hooks, use `React.ComponentProps<"element">` for props
- **Imports**: Group by external → internal → relative, use `@/` for src paths
- **Naming**: camelCase for variables/functions, PascalCase for components/types, kebab-case for files
- **State**: Zustand stores with devtools/persist, interface separation (State/Actions)
- **UI**: Radix UI + Tailwind, use `cn()` utility for className merging
- **Error Handling**: Try/catch with proper error types, console.log with emojis for debugging
- **Types**: Define interfaces separately, use `type` for unions, explicit return types for functions

## Project Structure
- React 19 + TypeScript + Vite + Tailwind + Zustand + React Router + Capacitor
- Mobile-first PWA with Capacitor for iOS/Android deployment
- Component-based architecture with shared UI components in `@/components/ui/`