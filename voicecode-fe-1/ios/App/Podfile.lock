PODS:
  - Capacitor (7.4.2):
    - Capac<PERSON><PERSON><PERSON>ova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBrowser (7.0.1):
    - Capacitor
  - CapacitorCommunityHttp (1.4.1):
    - Capacitor
  - CapacitorCommunitySpeechRecognition (7.0.1):
    - Capacitor
  - CapacitorCordova (7.4.2)
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorNetwork (7.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/browser`)"
  - "CapacitorCommunityHttp (from `../../../node_modules/.pnpm/@capacitor-community+http@1.4.1/node_modules/@capacitor-community/http`)"
  - "CapacitorCommunitySpeechRecognition (from `../../../node_modules/.pnpm/@capacitor-community+speech-recognition@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor-community/speech-recognition`)"
  - "CapacitorCordova (from `../../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/ios`)"
  - "CapacitorKeyboard (from `../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/keyboard`)"
  - "CapacitorNetwork (from `../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/network`)"
  - "CapacitorPreferences (from `../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/preferences`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../../node_modules/.pnpm/@capacitor+browser@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/browser"
  CapacitorCommunityHttp:
    :path: "../../../node_modules/.pnpm/@capacitor-community+http@1.4.1/node_modules/@capacitor-community/http"
  CapacitorCommunitySpeechRecognition:
    :path: "../../../node_modules/.pnpm/@capacitor-community+speech-recognition@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor-community/speech-recognition"
  CapacitorCordova:
    :path: "../../../node_modules/.pnpm/@capacitor+ios@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/ios"
  CapacitorKeyboard:
    :path: "../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/keyboard"
  CapacitorNetwork:
    :path: "../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/network"
  CapacitorPreferences:
    :path: "../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/preferences"

SPEC CHECKSUMS:
  Capacitor: 9d9e481b79ffaeacaf7a85d6a11adec32bd33b59
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorBrowser: 6299776d496e968505464884d565992faa20444a
  CapacitorCommunityHttp: 7eba71da94e070e37a26a60483350285a33e3903
  CapacitorCommunitySpeechRecognition: 3e03566c44c2bb3b52391a33d4518b1adbdeb38f
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorKeyboard: 09fd91dcde4f8a37313e7f11bde553ad1ed52036
  CapacitorNetwork: 15cb4385f0913a8ceb5e9a4d7af1ec554bdb8de8
  CapacitorPreferences: 6c98117d4d7508034a4af9db64d6b26fc75d7b94

PODFILE CHECKSUM: 05715f4bba48b264996033a2586907e98c07f924

COCOAPODS: 1.16.2
