import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.voicecode.app',
  appName: 'VoiceCode',
  webDir: 'dist',
  server: {
    androidScheme: 'http',
    // Development server disabled for device builds
    // url: 'http://localhost:9080',
    cleartext: true,
    // HTTP for device with machine IP
    iosScheme: 'http'
  },
  plugins: {
    // Configure plugins as they're added
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#3B82F6', // Match theme color
      showSpinner: false,
      androidSpinnerStyle: 'large',
      iosSpinnerStyle: 'small',
      spinnerColor: '#ffffff'
    },
    Preferences: {
      // No additional configuration needed
    },
    Network: {
      // No additional configuration needed
    },
    Keyboard: {
      resize: 'none', // Prevent automatic viewport resizing
      style: 'dark', // Match app theme
      resizeOnFullScreen: true
    },
    Browser: {
      enabled: true
    },
    SpeechRecognition: {
      // Configure speech recognition plugin
      language: 'en-US',
      maxResults: 5,
      partialResults: true
    }
  },
  ios: {
    // iOS-specific configuration
    scheme: 'App',
    contentInset: 'automatic',
    scrollEnabled: true,
    backgroundColor: '#faf9f5',
    // iOS-specific build settings
    buildOptions: {
      signingStyle: 'automatic', // Use automatic signing
      exportMethod: 'development' // or 'app-store' for production
    },
    // Handle safe areas and notches
    webContentsDebuggingEnabled: true, // Enable for development
    limitsNavigationsToAppBoundDomains: false,
    // Allow external domains for API calls
    allowsLinkPreview: false
  },
  android: {
    // Android-specific configuration
    allowMixedContent: false,
    captureInput: true,
    webContentsDebuggingEnabled: true, // Enable for development
    backgroundColor: '#faf9f5',
    // Handle navigation and URL schemes
    appendUserAgent: 'VoiceCode-Android',
    // Build configuration
    buildOptions: {
      keystorePath: undefined, // Will be set for production
      keystoreAlias: undefined,
      keystorePassword: undefined,
      releaseType: 'APK', // or 'AAB' for Play Store
      signingType: 'apksigner' // Default Android signing
    },
    // Permissions and features
    useLegacyBridge: false
  }
};

export default config;
