import { type ReactNode, useEffect } from 'react';
import { PlatformUtils } from '@/utils/platform';
import { androidOptimizations } from '@/utils/android-optimizations';

interface MobileLayoutProps {
  children: ReactNode;
}

export function MobileLayout({ children }: MobileLayoutProps) {
  useEffect(() => {
    // Add platform classes to body
    if (PlatformUtils.isNative()) {
      document.body.classList.add('capacitor-native');
      document.body.classList.add(`capacitor-${PlatformUtils.getPlatform()}`);
      
      // Initialize platform-specific optimizations
      if (PlatformUtils.isAndroid()) {
        androidOptimizations.initialize();
        androidOptimizations.setupTouchHandling();
        // Add Android-specific classes
        const androidClasses = androidOptimizations.getAndroidClasses();
        androidClasses.forEach(className => {
          document.body.classList.add(className);
        });
      }
    }
  }, []);

  return (
    <div className="mobile-layout">
      {children}
    </div>
  );
}