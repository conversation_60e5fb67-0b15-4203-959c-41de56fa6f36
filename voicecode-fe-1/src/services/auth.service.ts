import { apiClient } from './api'
import { Capacitor } from '@capacitor/core'
import { MobileOAuthService } from './mobile-oauth.service'
import type { LoginResponse, AuthUser, GitHubAuthResponse, TokenValidationResponse } from '@/types/auth.types'

export class AuthService {
  static async initiateGitHubAuth(): Promise<GitHubAuthResponse | void> {
    try {
      if (Capacitor.isNativePlatform()) {
        // For mobile, we don't return a URL - the authentication happens directly
        // The mobile service will handle the full flow and return tokens
        return; // Mobile flow is handled differently
      } else {
        // Existing web implementation
        const response = await apiClient.get('/api/auth/github') as { data: GitHubAuthResponse };
        return response.data;
      }
    } catch {
      throw new Error('Failed to start authentication');
    }
  }

  static async authenticateMobile(): Promise<LoginResponse> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('Mobile authentication only available on native platforms');
    }
    
    return MobileOAuthService.authenticate();
  }

  static async handleGitHubCallback(authKey: string): Promise<LoginResponse> {
    try {
      const response = await apiClient.get(`/api/auth/token/${authKey}`) as { data: LoginResponse };
      return response.data;
    } catch (error) {
      console.error('Failed to handle GitHub callback:', error)
      throw new Error('Failed to complete authentication')
    }
  }

  static async getCurrentUser(): Promise<AuthUser> {
    try {
      const response = await apiClient.get('/api/auth/me') as { data: TokenValidationResponse };
      return response.data.user;
    } catch (error) {
      console.error('Failed to get current user:', error)
      throw error
    }
  }

  static logout(): void {
    // Clear local storage
    localStorage.removeItem('voicecode-auth-store')
    // Redirect to login
    window.location.href = '/login'
  }
}