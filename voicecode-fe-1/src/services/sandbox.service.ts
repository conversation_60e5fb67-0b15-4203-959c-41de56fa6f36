import { apiClient } from './api'
import type { Sandbox, SandboxStatus, CreateSandboxRequest, SandboxListResponse, CreateSandboxResponse } from '@/types/sandbox.types'

export class SandboxService {
  static async listSandboxes(): Promise<Sandbox[]> {
    try {
      const response = await apiClient.get<SandboxListResponse>('/api/sandbox/list')
      // Transform backend response to frontend Sandbox interface
      return response.data.sandboxes.map(backendSandbox => ({
        id: backendSandbox.sandbox_id,
        sandbox_id: backendSandbox.sandbox_id,
        sandbox_name: backendSandbox.sandbox_name,
        name: backendSandbox.sandbox_name,
        repo_url: backendSandbox.repo_url,
        branch: backendSandbox.branch,
        status: SandboxService.mapBackendStatus(backendSandbox.status),
        created_at: backendSandbox.created_at,
        timestamp: backendSandbox.created_at,
        // Set defaults for optional fields
        avatar: undefined,
        lastMessage: `Repository: ${backendSandbox.repo_url}`,
        unreadCount: 0
      }))
    } catch (error) {
      console.error('Failed to fetch sandboxes:', error)
      throw new Error('Failed to load sandboxes')
    }
  }

  static async getSandbox(sandboxId: string): Promise<Sandbox> {
    try {
      const response = await apiClient.get<any>(`/api/sandbox/${sandboxId}`)
      const backendSandbox = response.data
      return {
        id: backendSandbox.sandbox_id,
        sandbox_id: backendSandbox.sandbox_id,
        sandbox_name: backendSandbox.sandbox_name,
        name: backendSandbox.sandbox_name,
        repo_url: backendSandbox.repo_url,
        branch: backendSandbox.branch,
        status: this.mapBackendStatus(backendSandbox.status),
        created_at: backendSandbox.created_at,
        timestamp: backendSandbox.created_at,
        avatar: undefined,
        lastMessage: `Repository: ${backendSandbox.repo_url}`,
        unreadCount: 0
      }
    } catch (error) {
      console.error(`Failed to fetch sandbox ${sandboxId}:`, error)
      throw new Error('Failed to load sandbox')
    }
  }

  static getStatusText(status: SandboxStatus): string {
    switch (status) {
      case 'running':
        return 'Running'
      case 'stopped':
        return 'Stopped'
      case 'starting':
        return 'Starting'
      case 'stopping':
        return 'Stopping'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  static async createSandbox(request: CreateSandboxRequest): Promise<Sandbox> {
    try {
      const response = await apiClient.post<CreateSandboxResponse>('/api/sandbox/create', request)
      // Transform backend response to frontend Sandbox interface
      const sandbox: Sandbox = {
        id: response.data.sandbox_id,
        sandbox_id: response.data.sandbox_id,
        sandbox_name: response.data.sandbox_name,
        name: response.data.sandbox_name,
        repo_url: response.data.repo_url,
        branch: response.data.branch,
        status: SandboxService.mapBackendStatus(response.data.status),
        created_at: response.data.created_at,
        timestamp: response.data.created_at,
        // Set defaults for optional fields
        avatar: undefined,
        lastMessage: `Repository: ${response.data.repo_url}`,
        unreadCount: 0
      }
      return sandbox
    } catch (error) {
      console.error('Failed to create sandbox:', error)
      throw new Error('Failed to create sandbox')
    }
  }

  static async startSandbox(sandboxId: string): Promise<void> {
    try {
      await apiClient.post(`/api/sandbox/${sandboxId}/start`)
    } catch (error) {
      console.error(`Failed to start sandbox ${sandboxId}:`, error)
      throw new Error('Failed to start sandbox')
    }
  }

  static async stopSandbox(sandboxId: string): Promise<void> {
    try {
      await apiClient.post(`/api/sandbox/${sandboxId}/stop`)
    } catch (error) {
      console.error(`Failed to stop sandbox ${sandboxId}:`, error)
      throw new Error('Failed to stop sandbox')
    }
  }

  static async deleteSandbox(sandboxId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/sandbox/${sandboxId}`)
    } catch (error) {
      console.error(`Failed to delete sandbox ${sandboxId}:`, error)
      throw new Error('Failed to delete sandbox')
    }
  }

  static getStatusColor(status: SandboxStatus): string {
    switch (status) {
      case 'running':
        return 'bg-green-500'
      case 'starting':
        return 'bg-blue-500'
      case 'stopping':
        return 'bg-amber-500'
      case 'stopped':
        return 'bg-gray-500'
      case 'error':
        return 'bg-red-500'
      case 'pending':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  static getStatusDisplay(status: SandboxStatus): string {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  static mapBackendStatus(backendStatus: string): SandboxStatus {
    // Map backend status values to frontend status values
    switch (backendStatus) {
      case 'started':
        return 'running'
      case 'stopped':
        return 'stopped'
      case 'starting':
        return 'starting'
      case 'stopping':
        return 'stopping'
      case 'pending':
        return 'pending'
      case 'error':
        return 'error'
      case 'ready':
        return 'stopped' // Map 'ready' to 'stopped' since it's not running yet
      default:
        console.warn(`Unknown backend status: ${backendStatus}`)
        return 'pending'
    }
  }
}