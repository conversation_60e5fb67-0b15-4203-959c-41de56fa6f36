import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';
import type { AuthUser } from '@/types/auth.types';

export class SecureTokenManager {
  private static readonly TOKEN_KEY = 'oauth_access_token';
  private static readonly USER_KEY = 'oauth_user_data';

  static async storeTokens(accessToken: string, user: AuthUser): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      console.log('🔐 SecureTokenManager: Storing tokens securely on native platform');
      
      await Promise.all([
        Preferences.set({
          key: this.TOKEN_KEY,
          value: accessToken
        }),
        Preferences.set({
          key: this.USER_KEY,
          value: JSON.stringify(user)
        })
      ]);
      
      console.log('✅ SecureTokenManager: Tokens stored successfully');
    } else {
      console.log('🔐 SecureTokenManager: Using localStorage for web platform');
      // Fallback to localStorage for web
      localStorage.setItem(this.TOKEN_KEY, accessToken);
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  static async getStoredTokens(): Promise<{token: string | null, user: AuthUser | null}> {
    if (Capacitor.isNativePlatform()) {
      console.log('🔐 SecureTokenManager: Retrieving tokens from secure storage');
      
      const [tokenResult, userResult] = await Promise.all([
        Preferences.get({ key: this.TOKEN_KEY }),
        Preferences.get({ key: this.USER_KEY })
      ]);
      
      const token = tokenResult.value;
      const user = userResult.value ? JSON.parse(userResult.value) : null;
      
      console.log('🔐 SecureTokenManager: Retrieved tokens:', { 
        hasToken: !!token, 
        hasUser: !!user 
      });
      
      return { token, user };
    } else {
      console.log('🔐 SecureTokenManager: Retrieving tokens from localStorage');
      // Fallback to localStorage for web
      const token = localStorage.getItem(this.TOKEN_KEY);
      const userStr = localStorage.getItem(this.USER_KEY);
      const user = userStr ? JSON.parse(userStr) : null;
      
      return { token, user };
    }
  }

  static async clearStoredTokens(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      console.log('🔐 SecureTokenManager: Clearing secure storage');
      
      await Promise.all([
        Preferences.remove({ key: this.TOKEN_KEY }),
        Preferences.remove({ key: this.USER_KEY })
      ]);
      
      console.log('✅ SecureTokenManager: Secure storage cleared');
    } else {
      console.log('🔐 SecureTokenManager: Clearing localStorage');
      // Fallback to localStorage for web
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  static async hasStoredTokens(): Promise<boolean> {
    const { token, user } = await this.getStoredTokens();
    return !!(token && user);
  }
}