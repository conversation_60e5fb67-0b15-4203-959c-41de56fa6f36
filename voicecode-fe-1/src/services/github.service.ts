import { apiClient } from './api'
import type { Repository, Branch } from '@/types/repository.types'

export class GitHubService {
  static readonly ENDPOINTS = {
    REPOSITORIES: '/api/repositories',
    BRANCHES: (owner: string, repo: string) => `/api/repositories/${owner}/${repo}/branches`
  }

  static async listRepositories(): Promise<Repository[]> {
    try {
      const response = await apiClient.get<any>(this.ENDPOINTS.REPOSITORIES)
      return response.data.items || []
    } catch (error) {
      console.error('Failed to list repositories:', error)
      throw new Error('Failed to load repositories')
    }
  }

  static async listBranches(owner: string, repo: string): Promise<Branch[]> {
    try {
      const response = await apiClient.get<any>(
        this.ENDPOINTS.BRANCHES(owner, repo)
      )
      return response.data.branches || []
    } catch (error) {
      console.error(`Failed to list branches for ${owner}/${repo}:`, error)
      throw new Error('Failed to load branches')
    }
  }

  static parseRepoUrl(url: string): { owner: string; repo: string } | null {
    const patterns = [
      /github\.com\/([^\/]+)\/([^\/\.]+)/,
      /github\.com:([^\/]+)\/([^\/\.]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return { owner: match[1], repo: match[2] }
      }
    }
    
    return null
  }
}