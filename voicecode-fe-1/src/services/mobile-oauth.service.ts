import { <PERSON>rowser } from '@capacitor/browser';
import { App } from '@capacitor/app';
import type { URLOpenListenerEvent } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { SecureTokenManager } from './secure-token-manager';
import type { LoginResponse } from '@/types/auth.types';

export class MobileOAuthService {
  private static authPromise: Promise<LoginResponse> | null = null;
  private static authResolve: ((value: LoginResponse) => void) | null = null;
  private static authReject: ((reason: unknown) => void) | null = null;
  private static currentState: string | null = null;
  private static currentListener: Promise<{ remove: () => void }> | null = null;

  static async authenticate(): Promise<LoginResponse> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('Mobile OAuth service should only be used on native platforms');
    }
    
    try {
      // Generate state parameter for security
      const state = this.generateRandomString(32);
      this.currentState = state;

      // Set up deep link listener
      this.setupDeepLinkListener();

      // Build authorization URL
      const authUrl = this.buildAuthorizationUrl(state);

      // Create promise for the OAuth flow
      this.authPromise = new Promise<LoginResponse>((resolve, reject) => {
        this.authResolve = resolve;
        this.authReject = reject;
      });

      // Open system browser
      await Browser.open({ 
        url: authUrl,
        windowName: '_system'
      });

      // Wait for the OAuth flow to complete
      return await this.authPromise;

    } catch (error) {
      this.cleanup();
      throw new Error(`Mobile authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private static setupDeepLinkListener(): void {
    this.currentListener = App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
      if (event.url.includes('voicecode://oauth/callback')) {
        this.handleOAuthCallback(event.url);
      }
    });
  }

  private static async handleOAuthCallback(url: string): Promise<void> {
    try {
      // Parse URL parameters
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      const state = urlObj.searchParams.get('state');
      const error = urlObj.searchParams.get('error');

      // Close browser
      await Browser.close();

      if (error) {
        throw new Error(`OAuth error: ${error}`);
      }

      if (!code) {
        throw new Error('No authorization code received');
      }

      if (state !== this.currentState) {
        throw new Error('State mismatch - possible CSRF attack');
      }

      // Exchange authorization code for tokens
      const tokens = await this.exchangeCodeForTokens(code, 'voicecode://oauth/callback');

      // Store tokens securely
      await SecureTokenManager.storeTokens(tokens.access_token, tokens.user);
      
      if (this.authResolve) {
        this.authResolve(tokens);
      }

    } catch (error) {
      if (this.authReject) {
        this.authReject(error);
      }
    } finally {
      this.cleanup();
    }
  }

  private static buildAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: import.meta.env.VITE_GITHUB_CLIENT_ID,
      redirect_uri: 'voicecode://oauth/callback',
      scope: 'repo user:email read:org write:repo_hook',
      response_type: 'code',
      state: state
    });

    return `https://github.com/login/oauth/authorize?${params.toString()}`;
  }



  private static generateRandomString(length: number): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    const values = new Uint8Array(length);
    crypto.getRandomValues(values);
    return Array.from(values, (v) => charset[v % charset.length]).join('');
  }



  private static async exchangeCodeForTokens(
    authorizationCode: string,
    redirectUri: string
  ): Promise<LoginResponse> {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/auth/mobile/exchange`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        authorization_code: authorizationCode,
        redirect_uri: redirectUri
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Token exchange failed');
    }

    const data = await response.json();
    return data.data || data;
  }

  private static cleanup(): void {
    this.authPromise = null;
    this.authResolve = null;
    this.authReject = null;
    this.currentState = null;
    
    // Remove deep link listener
    if (this.currentListener) {
      this.currentListener.then((handle) => handle.remove()).catch(console.error);
      this.currentListener = null;
    }
  }
}