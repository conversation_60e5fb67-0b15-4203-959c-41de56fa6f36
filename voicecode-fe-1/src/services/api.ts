import axios, { type AxiosInstance, type AxiosError, type InternalAxiosRequestConfig } from 'axios'

interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  metadata?: {
    startTime: number
  }
}

interface NetworkError extends Error {
  isNetworkError: boolean
  isTimeout: boolean
  isServerError: boolean
  statusCode?: number
  retryAfter?: number
}

interface RetryConfig {
  maxRetries: number
  retryDelay: number
  retryCondition?: (error: AxiosError) => boolean
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryCondition: (error: AxiosError) => {
    // Retry on network errors, timeouts, and 5xx server errors
    return !error.response || 
           error.code === 'ECONNABORTED' || 
           error.code === 'NETWORK_ERROR' ||
           (error.response.status >= 500 && error.response.status < 600)
  }
}

class ApiClient {
  private client: AxiosInstance
  private baseURL: string
  private isOnline: boolean = navigator.onLine
  private offlineQueue: Array<{ resolve: Function; reject: Function; request: () => Promise<any> }> = []

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:9100'
    
    console.log('🔧 API Client initialized with baseURL:', this.baseURL)
    console.log('🔧 Environment variables:', {
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      NODE_ENV: import.meta.env.NODE_ENV,
      DEV: import.meta.env.DEV
    })
    console.log('🔧 Platform info:', {
      userAgent: navigator.userAgent,
      isOnline: navigator.onLine,
      language: navigator.language
    })
    
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // Increased timeout for command execution
      withCredentials: true,
    })

    this.setupInterceptors()
    this.setupNetworkListeners()
  }

  private setupNetworkListeners() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
      console.log('🌐 Network connection restored')
      this.isOnline = true
      this.processOfflineQueue()
    })

    window.addEventListener('offline', () => {
      console.log('📴 Network connection lost')
      this.isOnline = false
    })
  }

  private processOfflineQueue() {
    // Process queued requests when connection is restored
    while (this.offlineQueue.length > 0) {
      const { resolve, reject, request } = this.offlineQueue.shift()!
      request()
        .then((response) => resolve(response))
        .catch((error) => reject(error))
    }
  }

  private createNetworkError(error: AxiosError): NetworkError {
    const networkError = new Error(this.getErrorMessage(error)) as NetworkError
    networkError.isNetworkError = !error.response
    networkError.isTimeout = error.code === 'ECONNABORTED'
    networkError.isServerError = error.response?.status ? error.response.status >= 500 : false
    networkError.statusCode = error.response?.status
    
    // Extract retry-after header if present
    if (error.response?.headers['retry-after']) {
      networkError.retryAfter = parseInt(error.response.headers['retry-after'], 10)
    }
    
    return networkError
  }

  private getErrorMessage(error: AxiosError): string {
    if (!error.response) {
      if (error.code === 'ECONNABORTED') {
        return 'Request timeout - the server took too long to respond'
      }
      if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
        return 'Network connection failed - please check your internet connection'
      }
      return 'Unable to connect to the server'
    }

    const status = error.response.status
    const data = error.response.data as any

    // Use server-provided error message if available
    if (data?.error?.message) {
      return data.error.message
    }
    if (data?.message) {
      return data.message
    }

    // Default messages based on status code
    switch (status) {
      case 400:
        return 'Invalid request - please check your input'
      case 401:
        return 'Authentication required - please log in again'
      case 403:
        return 'Access denied - you don\'t have permission for this action'
      case 404:
        return 'Resource not found'
      case 408:
        return 'Request timeout - please try again'
      case 409:
        return 'Conflict - the resource has been modified'
      case 422:
        return 'Validation error - please check your input'
      case 429:
        return 'Too many requests - please wait before trying again'
      case 500:
        return 'Internal server error - please try again later'
      case 502:
        return 'Bad gateway - the server is temporarily unavailable'
      case 503:
        return 'Service unavailable - please try again later'
      case 504:
        return 'Gateway timeout - the server took too long to respond'
      default:
        return `Server error (${status}) - please try again later`
    }
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    retryConfig: Partial<RetryConfig> = {}
  ): Promise<T> {
    const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig }
    let lastError: AxiosError

    console.log('🔄 Starting retry operation with config:', config)

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt + 1}/${config.maxRetries + 1}`)
        const result = await operation()
        if (attempt > 0) {
          console.log(`🔄✅ Retry succeeded on attempt ${attempt + 1}`)
        }
        return result
      } catch (error) {
        lastError = error as AxiosError
        
        console.log(`🔄❌ Attempt ${attempt + 1} failed:`, {
          message: lastError.message,
          code: lastError.code,
          hasResponse: !!lastError.response,
          status: lastError.response?.status
        })
        
        // Don't retry if it's the last attempt
        if (attempt === config.maxRetries) {
          console.log('🔄❌ Max retries reached, giving up')
          break
        }

        // Check if we should retry this error
        const shouldRetry = config.retryCondition ? config.retryCondition(lastError) : true
        console.log('🔄 Should retry?', shouldRetry)
        
        if (!shouldRetry) {
          console.log('🔄❌ Retry condition failed, giving up')
          break
        }

        // Calculate delay with exponential backoff
        const delay = config.retryDelay * Math.pow(2, attempt)
        const jitter = Math.random() * 0.1 * delay // Add 10% jitter
        const totalDelay = delay + jitter

        console.warn(`🔄⏳ Request failed (attempt ${attempt + 1}/${config.maxRetries + 1}), retrying in ${Math.round(totalDelay)}ms:`, lastError.message)
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, totalDelay))
      }
    }

    // All retries failed, throw the last error
    const networkError = this.createNetworkError(lastError!)
    console.error('🔄💥 All retries failed, throwing network error:', networkError)
    throw networkError
  }

  private async handleOfflineRequest<T>(request: () => Promise<T>): Promise<T> {
    if (this.isOnline) {
      return request()
    }

    // Queue request for when connection is restored
    return new Promise((resolve, reject) => {
      this.offlineQueue.push({
        resolve,
        reject,
        request
      })
      
      // Reject immediately if queue is too large (prevent memory issues)
      if (this.offlineQueue.length > 50) {
        reject(new Error('Too many requests queued - please try again when online'))
      }
    })
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Add request timestamp for timeout tracking
        (config as CustomAxiosRequestConfig).metadata = { startTime: Date.now() }
        
        console.log('📤 Making request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          fullURL: `${config.baseURL}${config.url}`,
          headers: JSON.stringify(config.headers, null, 2),
          timeout: config.timeout,
          withCredentials: config.withCredentials,
          data: config.data
        })
        
        console.log('📤 Raw request config:', JSON.stringify({
          method: config.method,
          url: config.url,
          baseURL: config.baseURL,
          headers: config.headers,
          withCredentials: config.withCredentials,
          timeout: config.timeout
        }, null, 2))
        
        // Get token from localStorage (will be updated by auth store)
        const authData = localStorage.getItem('voicecode-auth-store')
        if (authData) {
          try {
            const parsed = JSON.parse(authData)
            const token = parsed.state?.token
            if (token) {
              config.headers.Authorization = `Bearer ${token}`
              console.log('🔐 Adding auth token to request:', config.url)
            } else {
              console.warn('⚠️ No token found in auth store')
            }
          } catch (error) {
            console.warn('Failed to parse auth data from localStorage:', error)
          }
        } else {
          console.warn('⚠️ No auth data found in localStorage')
        }
        return config
      },
      (error) => {
        console.error('📤❌ Request interceptor error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Log successful requests in development
        const duration = Date.now() - ((response.config as CustomAxiosRequestConfig).metadata?.startTime || 0)
        console.log(`📥✅ ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
          status: response.status,
          statusText: response.statusText,
          headers: JSON.stringify(response.headers, null, 2),
          data: JSON.stringify(response.data, null, 2)
        })
        return response
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any
        const duration = Date.now() - ((originalRequest as CustomAxiosRequestConfig)?.metadata?.startTime || 0)
        
        console.error(`📥❌ ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url} (${duration}ms):`, {
          message: error.message,
          code: error.code,
          response: error.response ? {
            status: error.response.status,
            statusText: error.response.statusText,
            headers: JSON.stringify(error.response.headers, null, 2),
            data: JSON.stringify(error.response.data, null, 2)
          } : null,
          request: error.request ? {
            readyState: error.request.readyState,
            status: error.request.status,
            responseURL: error.request.responseURL,
            responseText: error.request.responseText
          } : null,
          config: {
            url: originalRequest?.url,
            method: originalRequest?.method,
            baseURL: originalRequest?.baseURL,
            timeout: originalRequest?.timeout,
            headers: JSON.stringify(originalRequest?.headers, null, 2),
            withCredentials: originalRequest?.withCredentials
          }
        })
        
        // Log the full error for debugging
        console.error('📥❌ Full error object:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2))

        // Handle 401 errors (unauthorized) - redirect to login
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true
          
          // Clear auth and redirect to login
          localStorage.removeItem('voicecode-auth-store')
          console.warn('Authentication failed, redirecting to login')
          window.location.href = '/login'
          return Promise.reject(error)
        }

        // Handle 429 (rate limiting) with backoff
        if (error.response?.status === 429 && !originalRequest._rateLimitRetry) {
          originalRequest._rateLimitRetry = true
          
          const retryAfter = error.response.headers['retry-after']
          const delay = retryAfter ? parseInt(retryAfter, 10) * 1000 : 5000
          
          console.warn(`Rate limited, retrying after ${delay}ms`)
          await new Promise(resolve => setTimeout(resolve, delay))
          
          return this.client(originalRequest)
        }

        return Promise.reject(error)
      }
    )
  }

  // Public API methods
  async get<T>(url: string, config?: any): Promise<T> {
    return this.handleOfflineRequest(async () => {
      return this.withRetry(async () => {
        // For auth endpoints, don't send credentials if no token exists
        const finalConfig = { ...config }
        if (url.includes('/api/auth/github') && !this.hasAuthToken()) {
          finalConfig.withCredentials = false
        }
        const response = await this.client.get(url, finalConfig)
        return response.data
      })
    })
  }

  private hasAuthToken(): boolean {
    const authData = localStorage.getItem('voicecode-auth-store')
    if (authData) {
      try {
        const parsed = JSON.parse(authData)
        return !!parsed.state?.token
      } catch {
        return false
      }
    }
    return false
  }

  async post<T>(url: string, data?: any, config?: any): Promise<T> {
    return this.handleOfflineRequest(async () => {
      return this.withRetry(async () => {
        const response = await this.client.post(url, data, config)
        return response.data
      }, { maxRetries: 1 }) // Fewer retries for POST requests
    })
  }

  async put<T>(url: string, data?: any, config?: any): Promise<T> {
    return this.handleOfflineRequest(async () => {
      return this.withRetry(async () => {
        const response = await this.client.put(url, data, config)
        return response.data
      }, { maxRetries: 1 }) // Fewer retries for PUT requests
    })
  }

  async delete<T>(url: string, config?: any): Promise<T> {
    return this.handleOfflineRequest(async () => {
      return this.withRetry(async () => {
        const response = await this.client.delete(url, config)
        return response.data
      }, { maxRetries: 1 }) // Fewer retries for DELETE requests
    })
  }

  // Utility methods
  getBaseURL(): string {
    return this.baseURL
  }

  isConnected(): boolean {
    return this.isOnline
  }

  getQueueLength(): number {
    return this.offlineQueue.length
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
export default apiClient