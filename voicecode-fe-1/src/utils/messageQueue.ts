import { UnifiedStorage } from './storage';

interface QueuedMessage {
  id: string;
  sandboxId: string;
  content: string;
  timestamp: number;
  token: string;
}

export class MessageQueue {
  private static QUEUE_KEY = 'message_queue';

  static async addMessage(message: Omit<QueuedMessage, 'id' | 'timestamp'>): Promise<void> {
    const queuedMessage: QueuedMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: Date.now()
    };

    const queue = await this.getQueue();
    queue.push(queuedMessage);
    await UnifiedStorage.setObject(this.QUEUE_KEY, queue);
  }

  static async getQueue(): Promise<QueuedMessage[]> {
    return await UnifiedStorage.getObject<QueuedMessage[]>(this.QUEUE_KEY) || [];
  }

  static async removeMessage(id: string): Promise<void> {
    const queue = await this.getQueue();
    const filteredQueue = queue.filter(msg => msg.id !== id);
    await UnifiedStorage.setObject(this.QUEUE_KEY, filteredQueue);
  }

  static async processQueue(): Promise<void> {
    const queue = await this.getQueue();
    
    for (const message of queue) {
      try {
        const response = await fetch(`/api/sandbox/${message.sandboxId}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${message.token}`
          },
          body: JSON.stringify({
            message: message.content,
            message_type: 'user'
          })
        });

        if (response.ok) {
          await this.removeMessage(message.id);
        }
      } catch (error) {
        console.error('Failed to send queued message:', error);
      }
    }
  }
}