/**
 * Speech Recognition Installation Verification
 * Utility to verify the speech recognition setup is working correctly
 */

import { SpeechRecognition } from '@capacitor-community/speech-recognition';
import { Capacitor } from '@capacitor/core';

export interface VerificationResult {
  platform: string;
  pluginInstalled: boolean;
  speechRecognitionAvailable: boolean;
  permissionStatus: string;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

export async function verifySpeechRecognitionSetup(): Promise<VerificationResult> {
  const result: VerificationResult = {
    platform: Capacitor.getPlatform(),
    pluginInstalled: false,
    speechRecognitionAvailable: false,
    permissionStatus: 'unknown',
    errors: [],
    warnings: [],
    recommendations: []
  };

  try {
    // Check if plugin is installed and accessible
    if (typeof SpeechRecognition === 'undefined') {
      result.errors.push('SpeechRecognition plugin not found. Install with: pnpm add @capacitor-community/speech-recognition');
      return result;
    }
    result.pluginInstalled = true;

    // Check availability
    try {
      const availabilityCheck = await SpeechRecognition.available();
      result.speechRecognitionAvailable = availabilityCheck.available;

      if (!availabilityCheck.available) {
        result.errors.push(`Speech recognition not available on ${result.platform}`);
        
        // Platform-specific recommendations
        if (result.platform === 'ios') {
          result.recommendations.push('Ensure iOS 13.0+ and test on a real device (not simulator)');
          result.recommendations.push('Check Info.plist has NSSpeechRecognitionUsageDescription');
        } else if (result.platform === 'android') {
          result.recommendations.push('Ensure Android API 21+ and RECORD_AUDIO permission in manifest');
          result.recommendations.push('Test on a real device with Google services');
        } else if (result.platform === 'web') {
          result.recommendations.push('Test in Chrome/Edge/Safari with HTTPS');
          result.recommendations.push('Check if Web Speech API is supported');
        }
      }
    } catch (availError) {
      result.errors.push(`Availability check failed: ${availError}`);
    }

    // Check permissions if available
    if (result.speechRecognitionAvailable) {
      try {
        const permissions = await SpeechRecognition.checkPermissions();
        result.permissionStatus = permissions.speechRecognition;

        if (permissions.speechRecognition === 'denied') {
          result.warnings.push('Speech recognition permission denied');
          result.recommendations.push('Guide user to enable permissions in device settings');
        } else if (permissions.speechRecognition === 'prompt') {
          result.recommendations.push('Permission will be requested when speech recognition starts');
        }
      } catch (permError) {
        result.warnings.push(`Permission check failed: ${permError}`);
      }
    }

    // Platform-specific checks
    await performPlatformSpecificChecks(result);

  } catch (error) {
    result.errors.push(`Verification failed: ${error}`);
  }

  return result;
}

async function performPlatformSpecificChecks(result: VerificationResult): Promise<void> {
  const platform = result.platform;

  if (platform === 'web') {
    // Web-specific checks
    if (!window.isSecureContext) {
      result.errors.push('Web Speech API requires HTTPS (except localhost)');
    }

    const hasWebSpeechAPI = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    if (!hasWebSpeechAPI) {
      result.errors.push('Web Speech API not supported in this browser');
      result.recommendations.push('Use Chrome, Edge, or Safari for best support');
    }
  }

  if (platform === 'ios') {
    // iOS-specific checks
    result.recommendations.push('Test on physical device - simulator may not support speech recognition');
  }

  if (platform === 'android') {
    // Android-specific checks
    result.recommendations.push('Ensure Google app is updated for better recognition accuracy');
  }
}

export function formatVerificationReport(result: VerificationResult): string {
  let report = `🔍 Speech Recognition Verification Report\n`;
  report += `Platform: ${result.platform}\n`;
  report += `Plugin Installed: ${result.pluginInstalled ? '✅' : '❌'}\n`;
  report += `Speech Recognition Available: ${result.speechRecognitionAvailable ? '✅' : '❌'}\n`;
  report += `Permission Status: ${result.permissionStatus}\n\n`;

  if (result.errors.length > 0) {
    report += `❌ Errors:\n`;
    result.errors.forEach(error => report += `  • ${error}\n`);
    report += '\n';
  }

  if (result.warnings.length > 0) {
    report += `⚠️  Warnings:\n`;
    result.warnings.forEach(warning => report += `  • ${warning}\n`);
    report += '\n';
  }

  if (result.recommendations.length > 0) {
    report += `💡 Recommendations:\n`;
    result.recommendations.forEach(rec => report += `  • ${rec}\n`);
    report += '\n';
  }

  if (result.errors.length === 0 && result.speechRecognitionAvailable) {
    report += `✅ Setup verified successfully! Speech recognition is ready to use.\n`;
  } else {
    report += `❌ Setup incomplete. Please address the issues above.\n`;
  }

  return report;
}

// Debug function to log verification results
export async function debugSpeechRecognitionSetup(): Promise<void> {
  console.group('🎤 Speech Recognition Setup Verification');
  
  try {
    const result = await verifySpeechRecognitionSetup();
    const report = formatVerificationReport(result);
    
    console.log(report);
    
    // Also log raw result for debugging
    console.log('Raw verification result:', result);
    
  } catch (error) {
    console.error('Verification failed:', error);
  }
  
  console.groupEnd();
}