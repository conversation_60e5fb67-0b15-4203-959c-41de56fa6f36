// Performance optimization utilities

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0,
    memoryUsage: 0,
  };

  // Measure page load time
  measureLoadTime(): void {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      }
    }
  }

  // Measure memory usage
  measureMemoryUsage(): void {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // MB
    }
  }

  // Measure First Contentful Paint (FCP)
  measureFCP(): Promise<number> {
    return new Promise((resolve) => {
      if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
          if (fcp) {
            resolve(fcp.startTime);
            observer.disconnect();
          }
        });
        observer.observe({ entryTypes: ['paint'] });
      } else {
        resolve(0);
      }
    });
  }

  // Measure Largest Contentful Paint (LCP)
  measureLCP(): Promise<number> {
    return new Promise((resolve) => {
      if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lcp = entries[entries.length - 1];
          if (lcp) {
            resolve(lcp.startTime);
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });

        // Fallback timeout
        setTimeout(() => {
          observer.disconnect();
          resolve(0);
        }, 5000);
      } else {
        resolve(0);
      }
    });
  }

  // Get current metrics
  getMetrics(): PerformanceMetrics {
    this.measureLoadTime();
    this.measureMemoryUsage();
    return { ...this.metrics };
  }

  // Log metrics to console
  logMetrics(): void {
    const metrics = this.getMetrics();
    console.group('Performance Metrics');
    console.log('Load Time:', `${metrics.loadTime.toFixed(2)}ms`);
    console.log('Memory Usage:', `${metrics.memoryUsage.toFixed(2)}MB`);
    console.groupEnd();
  }
}

export const perfMonitor = new PerformanceMonitor();

// Debounce utility for performance
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Throttle utility for performance
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function executedFunction(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Lazy loading utility
export function lazyLoad<T>(
  importFunc: () => Promise<T>
): Promise<T> {
  return importFunc();
}

// Bundle analysis utility
export function analyzeBundleSize(): void {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const jsResources = resources.filter(resource => 
      resource.name.includes('.js') || resource.name.includes('.mjs')
    );
    
    const totalSize = jsResources.reduce((sum, resource) => {
      return sum + (resource.transferSize || 0);
    }, 0);
    
    console.group('Bundle Analysis');
    console.log('Total JS Size:', `${(totalSize / 1024).toFixed(2)}KB`);
    console.log('JS Files:', jsResources.length);
    console.log('Largest JS File:', jsResources.reduce((largest, resource) => {
      return (resource.transferSize || 0) > (largest.transferSize || 0) ? resource : largest;
    }, jsResources[0]));
    console.groupEnd();
  }
}

// React performance hooks
import { useEffect, useRef, useState } from 'react';

export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const mountTime = useRef<number>(Date.now());

  useEffect(() => {
    const updateMetrics = () => {
      const currentMetrics = perfMonitor.getMetrics();
      setMetrics(currentMetrics);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Log component mount time
    const componentMountTime = Date.now() - mountTime.current;
    console.log('Component mount time:', `${componentMountTime}ms`);
  }, []);

  return metrics;
}

// Image optimization utilities
export function optimizeImage(
  imageElement: HTMLImageElement,
  quality: number = 0.8
): Promise<string> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      resolve(imageElement.src);
      return;
    }

    canvas.width = imageElement.naturalWidth;
    canvas.height = imageElement.naturalHeight;
    
    ctx.drawImage(imageElement, 0, 0);
    
    const optimizedDataUrl = canvas.toDataURL('image/jpeg', quality);
    resolve(optimizedDataUrl);
  });
}

// Intersection Observer for lazy loading
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): React.RefObject<HTMLDivElement | null> {
  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(callback, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options,
    });

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => {
      if (targetRef.current) {
        observer.unobserve(targetRef.current);
      }
    };
  }, [callback, options]);

  return targetRef;
}

// React.memo with custom comparison - will be in a separate React utility file

// Virtual scrolling utilities
export function calculateVirtualScrollItems(
  containerHeight: number,
  itemHeight: number,
  totalItems: number,
  scrollTop: number,
  overscan: number = 3
): { startIndex: number; endIndex: number; visibleItems: number } {
  const visibleItems = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2);

  return { startIndex, endIndex, visibleItems };
}

export default perfMonitor;