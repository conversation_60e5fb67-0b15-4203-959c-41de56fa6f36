/**
 * Android-specific optimizations for VoiceCode
 * Handles Material Design patterns, navigation, and performance
 */

import { Capacitor } from '@capacitor/core';

export class AndroidOptimizations {
  private static instance: AndroidOptimizations;
  private isAndroid: boolean;

  private constructor() {
    this.isAndroid = Capacitor.getPlatform() === 'android';
  }

  public static getInstance(): AndroidOptimizations {
    if (!AndroidOptimizations.instance) {
      AndroidOptimizations.instance = new AndroidOptimizations();
    }
    return AndroidOptimizations.instance;
  }

  /**
   * Initialize Android-specific optimizations
   */
  public initialize(): void {
    if (!this.isAndroid) return;

    this.setupMaterialDesign();
    this.setupNavigationBar();
    this.setupKeyboardHandling();
    this.setupBackButtonHandling();
    this.setupPerformanceOptimizations();
  }

  /**
   * Apply Material Design principles
   */
  private setupMaterialDesign(): void {
    // Add Material Design classes to body
    document.body.classList.add('android-material');
    
    // Set up ripple effects for buttons
    this.setupRippleEffects();
    
    // Configure elevation shadows
    this.setupElevationShadows();
  }

  /**
   * Setup Android navigation patterns
   */
  private setupNavigationBar(): void {
    // Handle Android navigation bar
    const metaTheme = document.querySelector('meta[name="theme-color"]');
    if (metaTheme) {
      metaTheme.setAttribute('content', '#3B82F6');
    }

    // Add navigation bar padding
    document.documentElement.style.setProperty('--android-nav-height', '48px');
  }

  /**
   * Handle Android keyboard behavior
   */
  private setupKeyboardHandling(): void {
    // Prevent viewport resize on keyboard show
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, viewport-fit=cover, interactive-widget=resizes-content'
      );
    }

    // Handle keyboard show/hide events
    window.addEventListener('resize', this.handleKeyboardResize.bind(this));
  }

  /**
   * Handle Android back button
   */
  private setupBackButtonHandling(): void {
    document.addEventListener('backbutton', (e) => {
      e.preventDefault();
      // Custom back button logic
      this.handleBackButton();
    });
  }

  /**
   * Android performance optimizations
   */
  private setupPerformanceOptimizations(): void {
    // Enable hardware acceleration
    document.body.style.transform = 'translateZ(0)';
    
    // Optimize scrolling
    (document.body.style as any).webkitOverflowScrolling = 'touch';
    
    // Reduce paint operations
    this.optimizePaintOperations();
  }

  /**
   * Setup Material Design ripple effects
   */
  private setupRippleEffects(): void {
    const style = document.createElement('style');
    style.textContent = `
      .android-ripple {
        position: relative;
        overflow: hidden;
      }
      
      .android-ripple::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
      }
      
      .android-ripple:active::before {
        width: 300px;
        height: 300px;
      }
    `;
    document.head.appendChild(style);

    // Add ripple class to buttons
    document.querySelectorAll('button, [role="button"]').forEach(button => {
      button.classList.add('android-ripple');
    });
  }

  /**
   * Setup Material Design elevation shadows
   */
  private setupElevationShadows(): void {
    const style = document.createElement('style');
    style.textContent = `
      .android-elevation-1 {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
      }
      
      .android-elevation-2 {
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
      }
      
      .android-elevation-3 {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
      }
      
      .android-elevation-4 {
        box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
      }
      
      .android-elevation-5 {
        box-shadow: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Handle keyboard resize events
   */
  private handleKeyboardResize(): void {
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.clientHeight;
    
    if (windowHeight < documentHeight * 0.75) {
      // Keyboard is likely open
      document.body.classList.add('keyboard-open');
    } else {
      // Keyboard is likely closed
      document.body.classList.remove('keyboard-open');
    }
  }

  /**
   * Handle Android back button press
   */
  private handleBackButton(): void {
    // Check if there are any open modals or overlays
    const openModal = document.querySelector('[data-state="open"]');
    if (openModal) {
      // Close the modal
      const closeButton = openModal.querySelector('[data-dismiss]');
      if (closeButton) {
        (closeButton as HTMLElement).click();
        return;
      }
    }

    // Check if we can go back in history
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Exit app or show exit confirmation
      this.showExitConfirmation();
    }
  }

  /**
   * Show exit confirmation dialog
   */
  private showExitConfirmation(): void {
    const confirmed = confirm('Are you sure you want to exit VoiceCode?');
    if (confirmed) {
      // Exit the app (this would typically use a Capacitor plugin)
      if ((navigator as any).app) {
        (navigator as any).app.exitApp();
      }
    }
  }

  /**
   * Optimize paint operations for better performance
   */
  private optimizePaintOperations(): void {
    // Use CSS containment for better performance
    const style = document.createElement('style');
    style.textContent = `
      .android-contain {
        contain: layout style paint;
      }
      
      .android-will-change {
        will-change: transform;
      }
      
      /* Optimize scrollable areas */
      .android-scroll-optimize {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
        transform: translateZ(0);
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Setup Android-specific touch handling
   */
  public setupTouchHandling(): void {
    if (!this.isAndroid) return;

    // Prevent 300ms click delay
    document.addEventListener('touchstart', () => {}, { passive: true });
    
    // Optimize touch events
    const touchOptions = { passive: true };
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), touchOptions);
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), touchOptions);
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), touchOptions);
  }

  /**
   * Handle touch start events
   */
  private handleTouchStart(event: TouchEvent): void {
    // Add visual feedback for touch
    const target = event.target as HTMLElement;
    if (target && target.classList.contains('android-ripple')) {
      target.classList.add('android-touching');
    }
  }

  /**
   * Handle touch move events
   */
  private handleTouchMove(event: TouchEvent): void {
    // Handle scroll momentum
    const target = event.target as HTMLElement;
    if (target && target.classList.contains('android-scroll-optimize')) {
      // Let the browser handle optimized scrolling
    }
  }

  /**
   * Handle touch end events
   */
  private handleTouchEnd(event: TouchEvent): void {
    // Remove visual feedback
    const target = event.target as HTMLElement;
    if (target && target.classList.contains('android-touching')) {
      setTimeout(() => {
        target.classList.remove('android-touching');
      }, 150);
    }
  }

  /**
   * Get Android-specific CSS classes
   */
  public getAndroidClasses(): string[] {
    if (!this.isAndroid) return [];
    
    return [
      'platform-android',
      'android-material',
      'android-contain',
      'android-scroll-optimize'
    ];
  }

  /**
   * Check if device is Android
   */
  public isAndroidDevice(): boolean {
    return this.isAndroid;
  }
}

// Export singleton instance
export const androidOptimizations = AndroidOptimizations.getInstance();