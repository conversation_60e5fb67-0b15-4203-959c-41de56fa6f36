import { Capacitor } from '@capacitor/core';

export const PlatformUtils = {
  isNative: () => Capacitor.isNativePlatform(),
  isWeb: () => !Capacitor.isNativePlatform(),
  getPlatform: () => Capacitor.getPlatform(), // 'ios', 'android', or 'web'
  isIOS: () => Capacitor.getPlatform() === 'ios',
  isAndroid: () => Capacitor.getPlatform() === 'android',
};

// Environment-specific configurations
export const getApiBaseUrl = () => {
  if (PlatformUtils.isNative()) {
    // Native apps use production API or configured endpoint
    return import.meta.env.VITE_API_BASE_URL || 'https://api.voicecode.app';
  }
  // Web version can use relative URLs or development server
  return import.meta.env.VITE_API_BASE_URL || '/api';
};

export const getWebSocketUrl = () => {
  if (PlatformUtils.isNative()) {
    return import.meta.env.VITE_WS_URL || 'wss://api.voicecode.app/ws';
  }
  return import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws';
};