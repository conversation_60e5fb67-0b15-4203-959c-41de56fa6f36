import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { Capacitor } from '@capacitor/core'
import './index.css'
import AppComponent from './App.tsx'

// Handle mobile app lifecycle events
if (Capacitor.isNativePlatform()) {
  console.log('🔧 Native platform detected - custom OAuth with deep links enabled');
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AppComponent />
  </StrictMode>,
)
