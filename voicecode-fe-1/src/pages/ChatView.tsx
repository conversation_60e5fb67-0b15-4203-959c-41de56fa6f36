import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { useEffect, useRef, useState } from 'react'
import { Keyboard } from '@capacitor/keyboard'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Loader2, Terminal, AlertCircle, RefreshCw, Trash2, MoreVertical, Copy, CheckCircle, XCircle, Play, Square } from 'lucide-react'
import { useAutoAnimate } from '@formkit/auto-animate/react'
import { useShallow } from 'zustand/react/shallow'
import { useSandboxStore } from '@/store/sandbox.store'
import { TypingIndicator } from '@/components/messages/TypingIndicator'
import { SandboxService } from '@/services/sandbox.service'
import { cn } from '@/lib/utils'
import { useVoiceCodeChat } from '@/hooks/useVoiceCodeChat'
import { ChatInput } from '@/components/chat/ChatInput'

export function ChatView() {
  const { sandboxId } = useParams()
  const navigate = useNavigate()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [messagesParent] = useAutoAnimate()
  
  // Use AI SDK chat hook instead of Zustand store
  const {
    messages,
    input: inputValue,
    setInput: setInputValue,
    handleSubmit,
    isLoadingHistory,
    isSendingMessage,
    error,
    reload: loadHistory,
    clearHistory
  } = useVoiceCodeChat(sandboxId!, {
    onCommandExecute: (command, sandboxId) => {
      console.log('Command executed:', command, 'in sandbox:', sandboxId)
    },
    onError: (error) => {
      console.error('Chat error:', error)
    }
  })
  
  const { 
    sandbox,
    deleteSandbox,
    startSandbox,
    stopSandbox,
    loadSandbox
  } = useSandboxStore(useShallow((state) => ({
    sandbox: state.getSandbox(sandboxId!),
    deleteSandbox: state.deleteSandbox,
    startSandbox: state.startSandbox,
    stopSandbox: state.stopSandbox,
    loadSandbox: state.loadSandbox
  })))

  // Local loading states
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  
  // Debug: Log sandbox status changes
  useEffect(() => {
    if (sandbox) {
      console.log(`🎯 ChatView sees sandbox status:`, sandbox.status)
    }
  }, [sandbox?.status])

  // Auto scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Keyboard event listeners for iOS
  useEffect(() => {
    const handleKeyboardShow = (info: any) => {
      console.log('🎹 Keyboard will show:', info.keyboardHeight)
      document.body.classList.add('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', `${info.keyboardHeight}px`)
    }

    const handleKeyboardHide = () => {
      console.log('🎹 Keyboard will hide')
      document.body.classList.remove('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', '0px')
    }

    // Add keyboard event listeners
    Keyboard.addListener('keyboardWillShow', handleKeyboardShow)
    Keyboard.addListener('keyboardWillHide', handleKeyboardHide)

    // Cleanup listeners on unmount
    return () => {
      Keyboard.removeAllListeners()
    }
  }, [])



  const handleSend = async () => {
    if (!inputValue.trim() || isSendingMessage) return
    
    handleSubmit()
  }



  const handleDeleteSandbox = async () => {
    if (!sandboxId) return
    if (!confirm('Are you sure you want to delete this sandbox?')) return
    
    try {
      await deleteSandbox(sandboxId)
      navigate('/conversations')
    } catch (error) {
      console.error('Failed to delete sandbox:', error)
    }
  }

  const handleStartSandbox = async () => {
    if (!sandboxId || isStarting) return
    
    try {
      setIsStarting(true)
      console.log('🚀 Starting sandbox:', sandboxId)
      await startSandbox(sandboxId)
      
      // Refresh sandbox status after operation completes
      await loadSandbox(sandboxId)
      console.log('✅ Sandbox started successfully')
    } catch (error) {
      console.error('❌ Failed to start sandbox:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const handleStopSandbox = async () => {
    if (!sandboxId || isStopping) return
    
    try {
      setIsStopping(true)
      console.log('🛑 Stopping sandbox:', sandboxId)
      await stopSandbox(sandboxId)
      
      // Refresh sandbox status after operation completes
      await loadSandbox(sandboxId)
      console.log('✅ Sandbox stopped successfully')
    } catch (error) {
      console.error('❌ Failed to stop sandbox:', error)
    } finally {
      setIsStopping(false)
    }
  }



  if (!sandbox) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Sandbox not found</p>
          <Button 
            onClick={() => navigate('/conversations')} 
            className="mt-4"
            variant="outline"
          >
            Back to Sandboxes
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="mobile-layout">
      {/* Fixed Header */}
      <div className="mobile-header">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/conversations')}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Avatar className="flex-shrink-0 h-8 w-8">
              <AvatarImage src={`/placeholder.svg?height=32&width=32&text=${sandbox.sandbox_name.slice(0, 2).toUpperCase()}`} />
              <AvatarFallback className="text-sm">{sandbox.sandbox_name.slice(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <Badge variant={sandbox.status === "running" ? "default" : "secondary"} className="text-xs">
                <Terminal className="h-3 w-3 mr-1" />
                {SandboxService.getStatusText(sandbox.status)}
              </Badge>
              
              {/* Sandbox Controls */}
              {(sandbox.status === 'stopped' || sandbox.status === 'error') && !isStopping ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStartSandbox}
                  disabled={isStarting}
                  className="h-6 px-2 text-xs"
                >
                  {isStarting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Play className="h-3 w-3" />
                  )}
                </Button>
              ) : sandbox.status === 'running' && !isStarting ? (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleStopSandbox}
                  disabled={isStopping}
                  className="h-6 px-2 text-xs"
                >
                  {isStopping ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Square className="h-3 w-3" />
                  )}
                </Button>
              ) : null}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex-shrink-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => loadHistory()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Chat
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => clearHistory()}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear History
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDeleteSandbox}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Sandbox
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Messages Content */}
      <div className="mobile-content">
        <div className="p-4 space-y-4 pb-28" ref={messagesParent}>
        {isLoadingHistory ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-16">
            <Terminal className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              No messages yet
            </p>
            <p className="text-sm text-gray-500">
              Type a command to get started
            </p>
          </div>
        ) : (
          messages.map((message) => {
            const metadata = (message as any).metadata || {}
            const messageType = message.role === 'user' ? 'user' : (metadata.messageType || 'system')
            const isOwnMessage = messageType === 'user'

            if (messageType === 'system' && metadata.exitCode !== undefined) {
              return (
                <Card key={message.id} className="max-w-full sm:max-w-[90%]">
                  <CardHeader className="pb-2">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <Terminal className="h-4 w-4 flex-shrink-0" />
                        <code className="text-sm font-mono truncate">$ {metadata.originalCommand}</code>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Badge variant={metadata.exitCode === 0 ? "default" : "destructive"} className="text-xs">
                          {metadata.exitCode === 0 ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          Exit {metadata.exitCode}
                        </Badge>
                        <Button variant="ghost" size="sm" onClick={() => navigator.clipboard.writeText(metadata.originalCommand)}>
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  {message.content && (
                    <CardContent className="pt-0">
                      <pre className="text-sm bg-muted/50 p-3 rounded whitespace-pre-wrap break-words">
                        <code>{message.content}</code>
                      </pre>
                    </CardContent>
                  )}
                </Card>
              )
            }

            if (messageType === 'error') {
              return (
                <Alert key={message.id} variant="destructive">
                  <AlertDescription>{message.content}</AlertDescription>
                </Alert>
              )
            }

            if (messageType === 'system') {
              return (
                <Alert key={message.id}>
                  <AlertDescription>{message.content}</AlertDescription>
                </Alert>
              )
            }

            return (
              <div key={message.id} className={isOwnMessage ? "flex justify-end" : "flex justify-start"}>
                <div className={cn(
                  "rounded-lg px-3 py-2 max-w-[85%] sm:max-w-[80%] break-words",
                  isOwnMessage 
                    ? "bg-primary text-primary-foreground" 
                    : "bg-muted"
                )}>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                </div>
              </div>
            )
          })
        )}
        
        {/* Temporarily always show TypingIndicator for styling */}
        {/* <TypingIndicator /> */}
        
        {isSendingMessage && (
          <TypingIndicator />
        )}
        
        <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed Footer - Input Area */}
      <div className="mobile-footer">
        <div className="px-4 pb-6">
          {/* Error display */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Chat Input */}
          <ChatInput
            value={inputValue}
            onChange={setInputValue}
            onSend={handleSend}
            isSending={isSendingMessage}
          />
        </div>
      </div>
    </div>
  )
}