import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { G<PERSON><PERSON>, Loader2 } from "lucide-react"
import { useAuthStore } from '@/store/auth.store'

export function Login() {
  const navigate = useNavigate()
  const { login, isLoading, error, isAuthenticated } = useAuthStore()

  console.log('🔑 Login Page: Rendered')
  console.log('🔑 Login Page: isAuthenticated =', isAuthenticated)
  console.log('🔑 Login Page: isLoading =', isLoading)
  console.log('🔑 Login Page: error =', error)

  useEffect(() => {
    if (isAuthenticated) {
      console.log('🔑 Login Page: User already authenticated, redirecting to conversations')
      navigate('/conversations')
    }
  }, [isAuthenticated, navigate])

  return (
    <div className="flex items-center justify-center min-h-[600px]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Welcome to VoiceCode</CardTitle>
          <CardDescription>
            Sign in to your account to access your development sandboxes and continue coding with voice commands.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            className="w-full" 
            size="lg"
            onClick={() => login()}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <Github className="mr-2 h-5 w-5" />
                Continue with GitHub
              </>
            )}
          </Button>
          {error && (
            <p className="mt-4 text-sm text-red-600 dark:text-red-400 text-center">
              {error}
            </p>
          )}
        </CardContent>
        <CardFooter className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            By signing in, you agree to our{" "}
            <a href="#" className="underline hover:text-primary">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="underline hover:text-primary">
              Privacy Policy
            </a>
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => navigate('/debug')}
            className="text-xs"
          >
            Debug Info
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}