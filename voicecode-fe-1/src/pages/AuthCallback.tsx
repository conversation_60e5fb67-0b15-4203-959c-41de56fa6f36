import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Card } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'
import { useAuthStore } from '@/store/auth.store'

export function AuthCallback() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { handleCallback } = useAuthStore()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const processCallback = async () => {
      const authKey = searchParams.get('auth_key')
      
      if (!authKey) {
        setError('No authentication key provided')
        return
      }

      try {
        await handleCallback(authKey)
        navigate('/conversations')
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Authentication failed')
        setTimeout(() => navigate('/login'), 3000)
      }
    }

    processCallback()
  }, [searchParams, handleCallback, navigate])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md p-8 text-center">
        {error ? (
          <>
            <h2 className="text-xl font-semibold text-red-600 mb-2">
              Authentication Failed
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <p className="text-sm text-gray-500">Redirecting to login...</p>
          </>
        ) : (
          <>
            <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-500" />
            <h2 className="text-xl font-semibold mb-2">Completing Authentication</h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we set up your account...
            </p>
          </>
        )}
      </Card>
    </div>
  )
}