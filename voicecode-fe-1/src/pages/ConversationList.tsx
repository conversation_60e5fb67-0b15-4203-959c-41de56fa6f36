import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Filter, Plus, Search, Clock, GitBranch, Cpu } from "lucide-react"
import { useSandboxStore } from '@/store/sandbox.store';
import { CreateSandboxWizard } from '@/components/sandbox/CreateSandboxWizard';
import { UserProfile } from '@/components/auth/UserProfile';
import type { Sandbox } from '@/types/sandbox.types';

export function ConversationList() {
  const navigate = useNavigate();
  const { sandboxes, isLoading, loadSandboxes } = useSandboxStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showRunning, setShowRunning] = useState(true)
  const [showStopped, setShowStopped] = useState(true)
  const [sortBy, setSortBy] = useState("recent")

  useEffect(() => {
    loadSandboxes();
  }, [loadSandboxes]);

  const filteredSandboxes = (sandboxes || []).filter(sandbox => {
    if (!sandbox || !sandbox.sandbox_name || !sandbox.repo_url || !sandbox.status) {
      return false;
    }

    const matchesSearch = sandbox.sandbox_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         sandbox.repo_url.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = (showRunning && sandbox.status === "running") || (showStopped && sandbox.status === "stopped")
    
    return matchesSearch && matchesStatus;
  });

  const handleSandboxClick = (sandbox: Sandbox) => {
    navigate(`/chat/${sandbox.sandbox_id || sandbox.id}`)
  }

  return (
    <>
      <div className="mobile-layout">
        {/* Fixed Header - Only contains the actual header */}
        <div className="mobile-header">
          <div className="px-4 py-3 flex items-center justify-between">
            <h1 className="text-2xl font-semibold tracking-tight">Sandboxes</h1>
            <div className="flex items-center gap-2">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="sm:hidden">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </SheetTrigger>
                <SheetContent side="right">
                  <SheetHeader>
                    <SheetTitle>Filter Options</SheetTitle>
                    <SheetDescription>Customize which sandboxes are displayed</SheetDescription>
                  </SheetHeader>
                  <div className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <h3 className="font-medium">Status</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="running">Show Running</Label>
                          <Switch id="running" checked={showRunning} onCheckedChange={setShowRunning} />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="stopped">Show Stopped</Label>
                          <Switch id="stopped" checked={showStopped} onCheckedChange={setShowStopped} />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="font-medium">Sort By</h3>
                      <RadioGroup value={sortBy} onValueChange={setSortBy}>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="recent" id="recent" />
                          <Label htmlFor="recent">Most Recent</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="name" id="name" />
                          <Label htmlFor="name">Name</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="status" id="status" />
                          <Label htmlFor="status">Status</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              <Button onClick={() => setShowCreateModal(true)} size="sm" className="font-medium">
                <Plus className="h-4 w-4" />
              </Button>
              
              <UserProfile />
            </div>
          </div>
        </div>

        {/* Content Area with Scroll */}
        <div className="mobile-content">
          {/* Search - Now part of scrollable content */}
          <div className="px-4 pt-4 pb-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search sandboxes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-background/50 border-border/50"
              />
            </div>
          </div>

          {/* Desktop Filters - Now part of scrollable content */}
          <div className="px-4 pb-3">
            <Card className="hidden sm:block">
              <CardContent className="p-4 pt-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Switch id="desktop-running" checked={showRunning} onCheckedChange={setShowRunning} />
                      <Label htmlFor="desktop-running">Running</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch id="desktop-stopped" checked={showStopped} onCheckedChange={setShowStopped} />
                      <Label htmlFor="desktop-stopped">Stopped</Label>
                    </div>
                  </div>

                  <RadioGroup value={sortBy} onValueChange={setSortBy} className="flex items-center gap-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="recent" id="desktop-recent" />
                      <Label htmlFor="desktop-recent">Recent</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="name" id="desktop-name" />
                      <Label htmlFor="desktop-name">Name</Label>
                    </div>
                  </RadioGroup>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-4">
          {/* Sandbox List */}
          <div className="grid gap-4">
            <TooltipProvider>
              {isLoading ? (
                <div className="text-center py-8">Loading sandboxes...</div>
              ) : filteredSandboxes.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No sandboxes found</p>
                </div>
              ) : (
                filteredSandboxes.map((sandbox) => (
                  <Card key={sandbox.sandbox_id || sandbox.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleSandboxClick(sandbox)}>
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex items-start gap-3 sm:gap-4">
                        <Avatar className="flex-shrink-0">
                          <AvatarImage src={`/placeholder.svg?height=40&width=40&text=${sandbox.sandbox_name.slice(0, 2).toUpperCase()}`} />
                          <AvatarFallback>{sandbox.sandbox_name.slice(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0 space-y-1">
                          <div className="flex items-center gap-2 flex-wrap">
                            <h3 className="font-semibold truncate">{sandbox.sandbox_name}</h3>
                            <Badge variant={sandbox.status === "running" ? "default" : "secondary"} className="flex-shrink-0">
                              {sandbox.status === "running" ? (
                                <>
                                  <Cpu className="h-3 w-3 mr-1" />
                                  Running
                                </>
                              ) : (
                                "Stopped"
                              )}
                            </Badge>
                          </div>

                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center gap-1 truncate max-w-full sm:max-w-[200px]">
                                  <GitBranch className="h-3 w-3 flex-shrink-0" />
                                  <span className="truncate">{sandbox.repo_url}</span>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{sandbox.repo_url}</p>
                              </TooltipContent>
                            </Tooltip>

                            <div className="flex items-center gap-1 flex-shrink-0">
                              <Clock className="h-3 w-3" />
                              <span className="hidden sm:inline">Recently active</span>
                              <span className="sm:hidden">Recent</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TooltipProvider>
          </div>
          </div>
        </div>
      </div>

      <CreateSandboxWizard
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={(sandboxId) => {
          navigate(`/chat/${sandboxId}`)
        }}
      />
    </>
  );
}