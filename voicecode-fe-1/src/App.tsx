import { createB<PERSON>er<PERSON><PERSON>er, RouterProvider, Navigate } from 'react-router-dom';
import { Login } from '@/pages/Login';
import { AuthCallback } from '@/pages/AuthCallback';
import { ConversationList } from './pages/ConversationList';
import { ChatView } from './pages/ChatView';
import { MobileLayout } from './layouts/MobileLayout';
import { SpeechTestComponent } from './components/speech/SpeechTestComponent';
import { ThemeProvider } from './providers/ThemeProvider';
import { ErrorBoundary } from './components/ErrorBoundary';
import { ErrorFallback } from './components/ui/ErrorFallback';
import { PWAInstallPrompt, IOSInstallPrompt } from './components/ui/PWAInstallPrompt';
import { UpdatePrompt } from './components/ui/UpdatePrompt';
import { OfflineIndicator } from './components/ui/OfflineIndicator';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { useServiceWorker } from './hooks/useServiceWorker';
import { useNotificationHandler } from './hooks/usePushNotifications';
import { useEffect } from 'react';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';

const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
    errorElement: <ErrorFallback error="Error loading login page" />
  },
  {
    path: '/auth/callback',
    element: <AuthCallback />,
    errorElement: <ErrorFallback error="Error processing authentication" />
  },
  {
    path: '/',
    element: <AuthGuard />,
    errorElement: <ErrorFallback error="Application error occurred" />,
    children: [
      {
        index: true,
        element: <Navigate to="/conversations" replace />,
      },
      {
        path: 'conversations',
        element: (
          <MobileLayout>
            <ConversationList />
            <PWAInstallPrompt />
            <IOSInstallPrompt />
          </MobileLayout>
        ),
        errorElement: <ErrorFallback error="Error loading conversations" />
      },
      {
        path: 'chat/:sandboxId',
        element: (
          <MobileLayout>
            <ChatView />
            <PWAInstallPrompt />
            <IOSInstallPrompt />
          </MobileLayout>
        ),
        errorElement: <ErrorFallback error="Error loading chat" />
      },
      {
        path: 'speech-test',
        element: (
          <MobileLayout>
            <SpeechTestComponent />
          </MobileLayout>
        ),
        errorElement: <ErrorFallback error="Error loading speech test" />
      },
    ],
  },
  {
    path: '*',
    element: <ErrorFallback error="Page not found (404)" showHomeButton={true} />
  },
]);

function App() {
  useServiceWorker();
  useNotificationHandler();

  useEffect(() => {
    // Handle PWA updates
    const handleCacheUpdate = () => {
      console.log('App cache updated');
      // You could show a toast notification here
    };

    window.addEventListener('sw:cache-updated', handleCacheUpdate);
    
    return () => {
      window.removeEventListener('sw:cache-updated', handleCacheUpdate);
    };
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <StagewiseToolbar
          config={{
            plugins: [ReactPlugin]
          }}
        />
        <OfflineIndicator />
        <RouterProvider router={router} />
        <UpdatePrompt />
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;