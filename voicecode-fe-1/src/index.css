@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Safe area variables for native apps */
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
    --background: oklch(0.9818 0.0054 95.0986);
    --foreground: oklch(0.3438 0.0269 95.7226);
    --card: oklch(0.9818 0.0054 95.0986);
    --card-foreground: oklch(0.1908 0.0020 106.5859);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.2671 0.0196 98.9390);
    --primary: oklch(0.6171 0.1375 39.0427);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9245 0.0138 92.9892);
    --secondary-foreground: oklch(0.4334 0.0177 98.6048);
    --muted: oklch(0.9341 0.0153 90.2390);
    --muted-foreground: oklch(0.6059 0.0075 97.4233);
    --accent: oklch(0.9245 0.0138 92.9892);
    --accent-foreground: oklch(0.2671 0.0196 98.9390);
    --destructive: oklch(0.1908 0.0020 106.5859);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.8847 0.0069 97.3627);
    --input: oklch(0.7621 0.0156 98.3528);
    --ring: oklch(0.6171 0.1375 39.0427);
    --radius: 0.5rem;
    --chart-1: oklch(0.5583 0.1276 42.9956);
    --chart-2: oklch(0.6898 0.1581 290.4107);
    --chart-3: oklch(0.8816 0.0276 93.1280);
    --chart-4: oklch(0.8822 0.0403 298.1792);
    --chart-5: oklch(0.5608 0.1348 42.0584);
    --sidebar: oklch(0.9663 0.0080 98.8792);
    --sidebar-foreground: oklch(0.3590 0.0051 106.6524);
    --sidebar-primary: oklch(0.6171 0.1375 39.0427);
    --sidebar-primary-foreground: oklch(0.9881 0 0);
    --sidebar-accent: oklch(0.9245 0.0138 92.9892);
    --sidebar-accent-foreground: oklch(0.3250 0 0);
    --sidebar-border: oklch(0.9401 0 0);
    --sidebar-ring: oklch(0.7731 0 0);
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --shadow-color: oklch(0 0 0);
    --shadow-opacity: 0.1;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0;
    --shadow-offset-y: 1px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
  }

  .dark {
    --background: oklch(0.2679 0.0036 106.6427);
    --foreground: oklch(0.8074 0.0142 93.0137);
    --card: oklch(0.2679 0.0036 106.6427);
    --card-foreground: oklch(0.9818 0.0054 95.0986);
    --popover: oklch(0.3085 0.0035 106.6039);
    --popover-foreground: oklch(0.9211 0.0040 106.4781);
    --primary: oklch(0.6724 0.1308 38.7559);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9818 0.0054 95.0986);
    --secondary-foreground: oklch(0.3085 0.0035 106.6039);
    --muted: oklch(0.2213 0.0038 106.7070);
    --muted-foreground: oklch(0.7713 0.0169 99.0657);
    --accent: oklch(0.2130 0.0078 95.4245);
    --accent-foreground: oklch(0.9663 0.0080 98.8792);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3618 0.0101 106.8928);
    --input: oklch(0.4336 0.0113 100.2195);
    --ring: oklch(0.6724 0.1308 38.7559);
    --chart-1: oklch(0.5583 0.1276 42.9956);
    --chart-2: oklch(0.6898 0.1581 290.4107);
    --chart-3: oklch(0.2130 0.0078 95.4245);
    --chart-4: oklch(0.3074 0.0516 289.3230);
    --chart-5: oklch(0.5608 0.1348 42.0584);
    --radius: 0.5rem;
    --sidebar: oklch(0.2357 0.0024 67.7077);
    --sidebar-foreground: oklch(0.8074 0.0142 93.0137);
    --sidebar-primary: oklch(0.3250 0 0);
    --sidebar-primary-foreground: oklch(0.9881 0 0);
    --sidebar-accent: oklch(0.1680 0.0020 106.6177);
    --sidebar-accent-foreground: oklch(0.8074 0.0142 93.0137);
    --sidebar-border: oklch(0.9401 0 0);
    --sidebar-ring: oklch(0.7731 0 0);
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --shadow-color: oklch(0 0 0);
    --shadow-opacity: 0.1;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0;
    --shadow-offset-y: 1px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }

  html, body, #root {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }

  /* Mobile layout base styles */
  .mobile-layout {
    width: 100%;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    position: relative;
    overflow: hidden;
    --mobile-header-height: 70px;
    --mobile-footer-height: 10px; /* Space for floating input card */
  }

  /* Fixed header positioning - applies to all platforms */
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--mobile-header-height);
    z-index: 40; /* Lower z-index to stay below modal overlays (z-50) */
    @apply bg-background border-b border-border;
    opacity: 0.95;
    backdrop-filter: blur(40px) saturate(180%) brightness(1.1);
    -webkit-backdrop-filter: blur(40px) saturate(180%) brightness(1.1);
    
    /* Hardware acceleration and iOS optimizations */
    transform: translateZ(0);
    -webkit-transform: translate3d(0, 0, 0);
    will-change: transform;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Fixed footer positioning - applies to all platforms */
  .mobile-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--mobile-footer-height);
    z-index: 500; /* Lower than header but above content */
    background: transparent;
    pointer-events: none; /* Allow clicks to pass through transparent areas */
    /* Removed: backdrop-filter, border-top - footer is now invisible */
  }

  /* Ensure floating input card can receive clicks */
  .mobile-footer > * {
    pointer-events: auto;
  }

  /* Content area with proper spacing */
  .mobile-content {
    position: absolute;
    top: var(--mobile-header-height);
    left: 0;
    right: 0;
    bottom: var(--mobile-footer-height);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    transform: translateZ(0);
    overscroll-behavior: contain;
    /* Ensure content doesn't scroll over header */
    scroll-padding-top: var(--mobile-header-height);
  }

  /* Dark mode support - Tailwind will handle this automatically */
  
  /* Footer remains transparent in dark mode too */
  .dark .mobile-footer {
    background: transparent;
  }

  /* Mobile viewport fixes */
  @media (max-width: 640px) {
    html {
      font-size: 16px; /* Prevent zoom on iOS */
    }

    input, textarea, select {
      font-size: 16px; /* Prevent zoom on iOS */
    }
  }

  /* Safe area support */
  @supports (padding: max(0px)) {
    .safe-top {
      padding-top: max(12px, env(safe-area-inset-top));
    }

    .safe-bottom {
      padding-bottom: max(12px, env(safe-area-inset-bottom));
    }
  }
  .theme {
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --radius: 0.5rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }

  /* iOS-specific viewport and positioning fixes */
  @supports (-webkit-touch-callout: none) {
    .mobile-layout {
      /* iOS Safari specific height calculation */
      height: -webkit-fill-available;
      min-height: 100vh;
      min-height: 100dvh;
    }
    
    .mobile-content {
      /* Ensure content fills available space on iOS */
      height: calc(100vh - var(--mobile-header-height) - var(--mobile-footer-height));
      height: calc(100dvh - var(--mobile-header-height) - var(--mobile-footer-height));
      height: calc(-webkit-fill-available - var(--mobile-header-height) - var(--mobile-footer-height));
    }
    
    .mobile-header {
      /* Additional iOS stability */
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      /* Fallback to fixed if sticky fails */
      position: fixed;
    }
  }

  /* Reduce paint operations during scroll */
  .mobile-header * {
    will-change: auto;
  }

  /* Platform-specific styles */
  .capacitor-native {
    /* Adjust positioning for safe areas on native platforms */
    .mobile-header {
      top: var(--safe-area-inset-top);
      left: var(--safe-area-inset-left);
      right: var(--safe-area-inset-right);
    }

    .mobile-footer {
      bottom: var(--safe-area-inset-bottom);
      left: var(--safe-area-inset-left);
      right: var(--safe-area-inset-right);
    }

    .mobile-content {
      top: calc(var(--mobile-header-height) + var(--safe-area-inset-top));
      left: var(--safe-area-inset-left);
      right: var(--safe-area-inset-right);
      bottom: calc(var(--mobile-footer-height) + var(--safe-area-inset-bottom));
    }
  }

  .capacitor-ios {
    /* iOS-specific styles */
    --safe-area-top: env(safe-area-inset-top);
    --safe-area-bottom: env(safe-area-inset-bottom);
  }

  .capacitor-ios .mobile-layout {
    /* Ensure full height on iOS native */
    height: 100vh;
    height: 100dvh;
    height: -webkit-fill-available;
  }

  .capacitor-ios .mobile-content {
    /* iOS native content height calculation */
    height: calc(100vh - var(--mobile-header-height) - var(--mobile-footer-height));
    height: calc(100dvh - var(--mobile-header-height) - var(--mobile-footer-height));
    height: calc(-webkit-fill-available - var(--mobile-header-height) - var(--mobile-footer-height));
  }

  /* iOS-specific header styling */
  .capacitor-ios .app-header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  }

  /* iOS button styling */
  .capacitor-ios .btn-primary {
    background: #007AFF;
    border-radius: 8px;
    font-weight: 600;
  }

  /* iOS list styling */
  .capacitor-ios .list-item {
    background: white;
    border-radius: 10px;
    margin: 8px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Disable text selection for better native feel */
  .capacitor-ios .no-select {
    -webkit-user-select: none;
    user-select: none;
  }

  /* iOS scroll indicators */
  .capacitor-ios ::-webkit-scrollbar {
    display: none;
  }

  /* Handle notch and home indicator */
  .capacitor-ios .full-screen {
    padding-top: var(--safe-area-top);
    padding-bottom: var(--safe-area-bottom);
  }

  .capacitor-android {
    /* Android-specific styles */
    --android-nav-height: 48px;
    --android-status-height: 24px;
  }

  /* Android Material Design styling */
  .capacitor-android .app-header {
    background: #3B82F6;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    elevation: 4;
  }

  /* Android button styling */
  .capacitor-android .btn-primary {
    background: #3B82F6;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .capacitor-android .btn-secondary {
    background: transparent;
    border: 1px solid #3B82F6;
    color: #3B82F6;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Android card styling */
  .capacitor-android .card {
    background: white;
    border-radius: 4px;
    margin: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    elevation: 2;
  }

  /* Android list styling */
  .capacitor-android .list-item {
    background: white;
    border-bottom: 1px solid #E5E7EB;
    padding: 16px;
    min-height: 56px;
    display: flex;
    align-items: center;
  }

  .capacitor-android .list-item:last-child {
    border-bottom: none;
  }

  /* Android navigation bar */
  .capacitor-android .nav-bar {
    background: #3B82F6;
    height: var(--android-nav-height);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Android status bar */
  .capacitor-android .status-bar {
    height: var(--android-status-height);
    background: #1E40AF; /* Darker shade of primary */
  }

  /* Android keyboard handling */
  .capacitor-android.keyboard-open {
    /* Adjust layout when keyboard is open */
    .app-container {
      height: calc(100vh - var(--keyboard-height, 0px));
    }
  }

  /* Android ripple effect */
  .capacitor-android .android-ripple {
    position: relative;
    overflow: hidden;
  }

  .capacitor-android .android-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
  }

  .capacitor-android .android-ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* Android Material Design elevation shadows */
  .capacitor-android .elevation-1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .capacitor-android .elevation-2 {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  }

  .capacitor-android .elevation-3 {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  }

  .capacitor-android .elevation-4 {
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  }

  .capacitor-android .elevation-5 {
    box-shadow: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
  }

  /* Android floating action button */
  .capacitor-android .fab {
    position: fixed;
    bottom: 16px;
    right: 16px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: #3B82F6;
    color: white;
    border: none;
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
  }

  .capacitor-android .fab:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
  }

  /* Android input styling */
  .capacitor-android .input-field {
    border: none;
    border-bottom: 2px solid #E5E7EB;
    background: transparent;
    padding: 8px 0;
    font-size: 16px;
    transition: border-color 0.3s ease;
  }

  .capacitor-android .input-field:focus {
    outline: none;
    border-bottom-color: #3B82F6;
  }

  .capacitor-android .input-field.filled {
    background: #F3F4F6;
    border: 1px solid #E5E7EB;
    border-radius: 4px;
    padding: 12px;
  }

  /* Android scroll optimization */
  .capacitor-android .scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    transform: translateZ(0);
  }

  /* Android navigation drawer */
  .capacitor-android .nav-drawer {
    background: white;
    width: 280px;
    height: 100vh;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .capacitor-android .nav-drawer.open {
    transform: translateX(0);
  }

  /* Android toast/snackbar */
  .capacitor-android .snackbar {
    position: fixed;
    bottom: 16px;
    left: 16px;
    right: 16px;
    background: #323232;
    color: white;
    padding: 14px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(100px);
    transition: transform 0.3s ease;
  }

  .capacitor-android .snackbar.show {
    transform: translateY(0);
  }

  /* Android performance optimizations */
  .capacitor-android .will-change-transform {
    will-change: transform;
  }

  .capacitor-android .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Ensure touch targets are at least 44px for iOS */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Keyboard-aware layout adjustments */
  .keyboard-open .mobile-layout {
    height: calc(100vh - var(--keyboard-height, 0px));
    height: calc(100dvh - var(--keyboard-height, 0px));
  }

  .keyboard-open .mobile-content {
    bottom: calc(var(--mobile-footer-height) + var(--keyboard-height, 0px));
  }

  .keyboard-open .mobile-footer {
    bottom: var(--keyboard-height, 0px);
  }

  /* iOS-specific keyboard handling */
  @supports (-webkit-touch-callout: none) {
    .keyboard-open .mobile-layout {
      height: calc(-webkit-fill-available - var(--keyboard-height, 0px));
    }
    
    .keyboard-open .mobile-header {
      /* Ensure header stays fixed during keyboard transitions */
      position: fixed !important;
      top: 0 !important;
      transform: translateZ(0);
      -webkit-transform: translate3d(0, 0, 0);
    }
  }

  /* Smooth transitions for keyboard show/hide */
  .mobile-layout,
  .mobile-content,
  .mobile-footer {
    transition: height 0.3s ease, bottom 0.3s ease;
  }

  /* Prevent layout shifts during keyboard transitions */
  .keyboard-open .mobile-header {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}