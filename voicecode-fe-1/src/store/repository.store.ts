import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { GitHubService } from '@/services/github.service'
import type { Repository, Branch } from '@/types/repository.types'

interface RepositoryState {
  repositories: Repository[]
  branches: Branch[]
  selectedRepository: Repository | null
  selectedBranch: string | null
  isLoadingRepos: boolean
  isLoadingBranches: boolean
  error: string | null
}

interface RepositoryActions {
  loadRepositories: () => Promise<void>
  loadBranches: (owner: string, repo: string) => Promise<void>
  selectRepository: (repo: Repository | null) => void
  selectBranch: (branch: string | null) => void
  clearSelection: () => void
  setError: (error: string | null) => void
}

export const useRepositoryStore = create<RepositoryState & RepositoryActions>()(
  devtools(
    (set, get) => ({
      repositories: [],
      branches: [],
      selectedRepository: null,
      selectedBranch: null,
      isLoadingRepos: false,
      isLoadingBranches: false,
      error: null,

      loadRepositories: async () => {
        set({ isLoadingRepos: true, error: null })
        try {
          const repositories = await GitHubService.listRepositories()
          set({ repositories, isLoadingRepos: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load repositories',
            isLoadingRepos: false 
          })
        }
      },

      loadBranches: async (owner: string, repo: string) => {
        set({ isLoadingBranches: true, error: null, branches: [] })
        try {
          const branches = await GitHubService.listBranches(owner, repo)
          set({ branches, isLoadingBranches: false })
          
          const defaultBranch = branches.find(b => b.name === 'main' || b.name === 'master')
          if (defaultBranch && !get().selectedBranch) {
            set({ selectedBranch: defaultBranch.name })
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load branches',
            isLoadingBranches: false 
          })
        }
      },

      selectRepository: (repo) => {
        console.log('🔍 [RepositoryStore] selectRepository called:', { 
          repoName: repo?.name || null,
          repoUrl: repo?.html_url || null 
        })
        set({ selectedRepository: repo, selectedBranch: null, branches: [] })
        if (repo) {
          const parsed = GitHubService.parseRepoUrl(repo.html_url)
          if (parsed) {
            console.log('🔍 [RepositoryStore] Loading branches for:', parsed)
            get().loadBranches(parsed.owner, parsed.repo)
          }
        }
      },

      selectBranch: (branch) => {
        console.log('🔍 [RepositoryStore] selectBranch called:', { branch })
        set({ selectedBranch: branch })
      },
      
      clearSelection: () => {
        console.log('🔍 [RepositoryStore] clearSelection called')
        set({ 
          selectedRepository: null, 
          selectedBranch: null, 
          branches: [],
          error: null 
        })
      },
      
      setError: (error) => set({ error })
    }),
    { name: 'repository-store' }
  )
)