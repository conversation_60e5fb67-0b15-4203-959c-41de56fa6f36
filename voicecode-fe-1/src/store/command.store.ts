import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { Command, CommandStatus } from '@/types/command.types'

interface CommandState {
  executions: Map<string, Command>
  currentExecution: string | null
  isExecuting: boolean
  error: string | null
}

interface CommandActions {
  startExecution: (sandboxId: string, command: string) => string
  updateExecution: (executionId: string, updates: Partial<Command>) => void
  completeExecution: (executionId: string, result: string, exitCode: number) => void
  failExecution: (executionId: string, error: string) => void
  getExecution: (executionId: string) => Command | undefined
  getExecutionsForSandbox: (sandboxId: string) => Command[]
  clearExecutions: (sandboxId?: string) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useCommandStore = create<CommandState & CommandActions>()(
  devtools(
    (set, get) => ({
      // State
      executions: new Map(),
      currentExecution: null,
      isExecuting: false,
      error: null,

      // Actions
      startExecution: (sandboxId, command) => {
        const executionId = `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const execution: Command = {
          id: executionId,
          originalVoice: '',
          transcription: '',
          finalCommand: command.trim(),
          sandboxId,
          executedAt: new Date(),
          duration: 0,
          exitCode: 0,
          output: '',
          status: 'pending' as CommandStatus,
          workingDirectory: undefined
        }
        
        const newExecutions = new Map(get().executions)
        newExecutions.set(executionId, execution)
        
        set({ 
          executions: newExecutions,
          currentExecution: executionId,
          isExecuting: true,
          error: null
        })
        
        return executionId
      },
      
      updateExecution: (executionId, updates) => {
        const execution = get().executions.get(executionId)
        if (!execution) return
        
        const newExecutions = new Map(get().executions)
        newExecutions.set(executionId, { ...execution, ...updates })
        set({ executions: newExecutions })
      },
      
      completeExecution: (executionId, result, exitCode) => {
        const execution = get().executions.get(executionId)
        if (!execution) return
        
        const endTime = new Date()
        const duration = endTime.getTime() - execution.executedAt.getTime()
        
        const newExecutions = new Map(get().executions)
        newExecutions.set(executionId, {
          ...execution,
          status: 'success' as CommandStatus,
          output: result,
          exitCode,
          duration
        })
        
        set({ 
          executions: newExecutions,
          currentExecution: null,
          isExecuting: false
        })
      },
      
      failExecution: (executionId, error) => {
        const execution = get().executions.get(executionId)
        if (!execution) return
        
        const endTime = new Date()
        const duration = endTime.getTime() - execution.executedAt.getTime()
        
        const newExecutions = new Map(get().executions)
        newExecutions.set(executionId, {
          ...execution,
          status: 'error' as CommandStatus,
          output: error,
          duration
        })
        
        set({ 
          executions: newExecutions,
          currentExecution: null,
          isExecuting: false,
          error
        })
      },
      
      getExecution: (executionId) => {
        return get().executions.get(executionId)
      },
      
      getExecutionsForSandbox: (sandboxId) => {
        return Array.from(get().executions.values())
          .filter(execution => execution.sandboxId === sandboxId)
          .sort((a, b) => b.executedAt.getTime() - a.executedAt.getTime())
      },
      
      clearExecutions: (sandboxId) => {
        if (sandboxId) {
          const newExecutions = new Map()
          for (const [id, execution] of get().executions.entries()) {
            if (execution.sandboxId !== sandboxId) {
              newExecutions.set(id, execution)
            }
          }
          set({ executions: newExecutions })
        } else {
          set({ executions: new Map() })
        }
      },
      
      setError: (error) => set({ error }),
      
      clearError: () => set({ error: null }),
    }),
    { name: 'command-store' }
  )
)