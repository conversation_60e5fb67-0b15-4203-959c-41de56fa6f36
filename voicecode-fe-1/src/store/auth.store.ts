import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Capacitor } from '@capacitor/core'
import { AuthService } from '@/services/auth.service'
import { SecureTokenManager } from '@/services/secure-token-manager'
import type { AuthUser } from '@/types/auth.types'

interface AuthState {
  user: AuthUser | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: () => Promise<void>
  handleCallback: (authKey: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  setError: (error: string | null) => void
  clearError: () => void
  clearStorage: () => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Actions
        login: async () => {
          console.log('🔑 Auth Store: Starting login process...');
          set({ isLoading: true, error: null });
          
          try {
            if (Capacitor.isNativePlatform()) {
              console.log('🔑 Auth Store: Using mobile PKCE authentication');
              
              // Direct mobile authentication with PKCE
              const authData = await AuthService.authenticateMobile();
              
              set({
                user: authData.user,
                token: authData.access_token,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              
              console.log('✅ Auth Store: Mobile authentication successful');
            } else {
              console.log('🔑 Auth Store: Using web authentication');
              
              // Existing web flow
              const response = await AuthService.initiateGitHubAuth();
              if (response?.auth_url) {
                console.log('🔑 Auth Store: Redirecting to GitHub...');
                window.location.href = response.auth_url;
              }
            }
          } catch (error) {
            console.error('🔑❌ Auth Store: Login failed:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Login failed',
              isLoading: false 
            });
          }
        },

        handleCallback: async (authKey: string) => {
          set({ isLoading: true, error: null })
          try {
            const authData = await AuthService.handleGitHubCallback(authKey)
            set({
              user: authData.user,
              token: authData.access_token,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Authentication failed',
              isLoading: false
            })
            throw error
          }
        },

        logout: async () => {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null
          })
          
          // Clear secure storage on mobile
          if (Capacitor.isNativePlatform()) {
            await SecureTokenManager.clearStoredTokens();
          }
          
          AuthService.logout()
        },

        checkAuth: async () => {
          const state = get()
          console.log('🔐 Auth Store: checkAuth called')
          console.log('🔐 Auth Store: Current state =', { 
            hasToken: !!state.token, 
            hasUser: !!state.user, 
            isAuthenticated: state.isAuthenticated 
          })
          
          // On mobile, check secure storage first
          if (Capacitor.isNativePlatform() && !state.token) {
            console.log('🔐 Auth Store: Checking secure storage for tokens...')
            const { token, user } = await SecureTokenManager.getStoredTokens();
            if (token && user) {
              console.log('🔐 Auth Store: Found tokens in secure storage')
              set({ token, user, isAuthenticated: true })
              return
            }
          }
          
          if (state.token && !state.user) {
            console.log('🔐 Auth Store: Token exists but no user, fetching user info...')
            try {
              const user = await AuthService.getCurrentUser()
              console.log('🔐 Auth Store: User fetched successfully:', user.username)
              set({ user, isAuthenticated: true })
            } catch (error) {
              console.log('🔐 Auth Store: Failed to fetch user, logging out:', error)
              get().logout()
            }
          } else if (!state.token) {
            console.log('🔐 Auth Store: No token found, user not authenticated')
            set({ isAuthenticated: false })
          } else {
            console.log('🔐 Auth Store: User already authenticated')
          }
        },

        setError: (error) => set({ error }),
        
        clearError: () => set({ error: null }),

        clearStorage: () => {
          console.log('🔐 Auth Store: Clearing all auth data')
          localStorage.removeItem('voicecode-auth-store')
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null
          })
        },
      }),
      {
        name: 'voicecode-auth-store',
        partialize: (state) => ({ 
          user: state.user, 
          token: state.token
        }),
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Only set isAuthenticated to true if we have both user and token
            state.isAuthenticated = !!(state.user && state.token)
            console.log('🔐 Auth Store: Rehydrated from localStorage', {
              hasUser: !!state.user,
              hasToken: !!state.token,
              isAuthenticated: state.isAuthenticated
            })
          }
        }
      }
    ),
    { name: 'auth-store' }
  )
)