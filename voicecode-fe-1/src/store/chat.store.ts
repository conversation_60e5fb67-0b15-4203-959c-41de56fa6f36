import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { useShallow } from 'zustand/react/shallow'
import { ChatService } from '@/services/chat.service'
import type { ChatMessage } from '@/types/chat.types'

interface ChatState {
  messagesBySandbox: Record<string, ChatMessage[]>
  isLoadingHistory: boolean
  isSendingMessage: boolean
  error: string | null
  pagination: {
    [sandboxId: string]: {
      currentPage: number
      totalPages: number
      totalMessages: number
    }
  }
}

interface ChatActions {
  loadHistory: (sandboxId: string, page?: number) => Promise<void>
  sendMessage: (sandboxId: string, content: string) => Promise<void>
  executeCommand: (sandboxId: string, command: string) => Promise<void>
  clearHistory: (sandboxId: string) => Promise<void>
  addMessage: (sandboxId: string, message: ChatMessage) => void
  setError: (error: string | null) => void
}

export const useChatStore = create<ChatState & ChatActions>()(
  devtools(
    (set, get) => ({
      messagesBySandbox: {},
      isLoadingHistory: false,
      isSendingMessage: false,
      error: null,
      pagination: {},

      loadHistory: async (sandboxId: string, page = 1) => {
        set({ isLoadingHistory: true, error: null })
        
        try {
          const response = await ChatService.loadHistory(sandboxId, { page })
          
          // Convert backend format to frontend format
          const messages: ChatMessage[] = response.messages.map((msg: any) => ({
            id: msg.id,
            sandboxId: msg.sandboxId || msg.sandbox_id,
            userId: msg.userId || msg.user_id,
            messageType: msg.messageType || msg.message_type,
            content: msg.content,
            commandId: msg.commandId || msg.command_id,
            createdAt: msg.createdAt || new Date(msg.created_at),
            metadata: msg.metadata
          }))
          
          // Update messages
          const currentMessages = get().messagesBySandbox[sandboxId] || []
          
          let updatedMessages: ChatMessage[]
          if (page === 1) {
            // Replace all messages if loading first page
            updatedMessages = messages
          } else {
            // Append messages for pagination
            const allMessages = [...currentMessages, ...messages]
            // Remove duplicates by id
            const messageMap = new Map(allMessages.map(m => [m.id, m]))
            updatedMessages = Array.from(messageMap.values())
          }
          
          set({
            messagesBySandbox: {
              ...get().messagesBySandbox,
              [sandboxId]: updatedMessages
            },
            pagination: {
              ...get().pagination,
              [sandboxId]: {
                currentPage: response.page,
                totalPages: Math.ceil(response.total / response.pageSize),
                totalMessages: response.total
              }
            },
            isLoadingHistory: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to load history',
            isLoadingHistory: false
          })
        }
      },

      sendMessage: async (sandboxId: string, content: string) => {
        const { addMessage, executeCommand } = get()
        
        set({ isSendingMessage: true, error: null })
        
        try {
          // Add optimistic user message
          const userMessage: ChatMessage = {
            id: `temp-${Date.now()}`,
            sandboxId,
            userId: 'current-user',
            messageType: 'user',
            content: content.trim(),
            createdAt: new Date(),
            metadata: {}
          }
          addMessage(sandboxId, userMessage)
          
          // Send to backend
          await ChatService.sendMessage(sandboxId, content, 'user')
          
          // Check if this is a command
          if (ChatService.isExecutableCommand(content)) {
            await executeCommand(sandboxId, content)
          }
          
          set({ isSendingMessage: false })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to send message',
            isSendingMessage: false
          })
          
          // Add error message
          const errorMessage: ChatMessage = {
            id: `error-${Date.now()}`,
            sandboxId,
            userId: 'system',
            messageType: 'error',
            content: error instanceof Error ? error.message : 'Failed to send message',
            createdAt: new Date(),
            metadata: {}
          }
          addMessage(sandboxId, errorMessage)
        }
      },

      executeCommand: async (sandboxId: string, command: string) => {
        const { addMessage } = get()
        const commandId = `cmd-${Date.now()}`
        
        set({ error: null })
        
        try {
          const response = await ChatService.executeCommand(sandboxId, command)
          const { content, isError } = ChatService.formatCommandOutput(response)
          
          // Add result message
          const resultMessage: ChatMessage = {
            id: `result-${Date.now()}`,
            sandboxId,
            userId: 'system',
            messageType: isError ? 'error' : 'system',
            content: content || '(No output)',
            commandId,
            createdAt: new Date(),
            metadata: {
              exitCode: response.exit_code,
              executionTime: response.execution_time,
              originalCommand: command
            }
          }
          addMessage(sandboxId, resultMessage)
          
        } catch (error) {
          // Add error message
          const errorMessage: ChatMessage = {
            id: `cmd-error-${Date.now()}`,
            sandboxId,
            userId: 'system',
            messageType: 'error',
            content: error instanceof Error ? error.message : 'Command execution failed',
            commandId,
            createdAt: new Date(),
            metadata: { originalCommand: command }
          }
          addMessage(sandboxId, errorMessage)
          
          set({
            error: error instanceof Error ? error.message : 'Command execution failed'
          })
        }
      },

      clearHistory: async (sandboxId: string) => {
        try {
          await ChatService.clearHistory(sandboxId)
          
          // Clear from store
          set({
            messagesBySandbox: {
              ...get().messagesBySandbox,
              [sandboxId]: []
            },
            pagination: {
              ...get().pagination,
              [sandboxId]: {
                currentPage: 1,
                totalPages: 0,
                totalMessages: 0
              }
            }
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to clear history'
          })
        }
      },

      addMessage: (sandboxId: string, message: ChatMessage) => {
        const messages = get().messagesBySandbox[sandboxId] || []
        const newMessages = [...messages, message].sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )
        
        set({ 
          messagesBySandbox: {
            ...get().messagesBySandbox,
            [sandboxId]: newMessages
          }
        })
      },

      setError: (error) => set({ error })
    }),
    { name: 'chat-store' }
  )
)

// Selector hooks for convenience
export const useChatMessages = (sandboxId: string) => {
  return useChatStore(useShallow(state => state.messagesBySandbox[sandboxId] || []))
}

export const useChatPagination = (sandboxId: string) => {
  return useChatStore(useShallow(state => state.pagination[sandboxId] || {
    currentPage: 1,
    totalPages: 0,
    totalMessages: 0
  }))
}