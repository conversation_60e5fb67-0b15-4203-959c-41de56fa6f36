import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { SandboxService } from '@/services/sandbox.service'
import type { Sandbox, SandboxStatus, CreateSandboxRequest } from '@/types/sandbox.types'

interface SandboxState {
  sandboxes: Sandbox[]
  isLoading: boolean
  error: string | null
  pollingIntervals: Record<string, NodeJS.Timeout>
}

interface SandboxActions {
  setSandboxes: (sandboxes: Sandbox[]) => void
  addSandbox: (sandbox: Sandbox) => void
  updateSandbox: (id: string, updates: Partial<Sandbox>) => void
  removeSandbox: (id: string) => void
  getSandbox: (id: string) => Sandbox | undefined
  updateSandboxStatus: (id: string, status: SandboxStatus) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  // API methods
  loadSandboxes: () => Promise<void>
  loadSandbox: (id: string) => Promise<void>
  createSandbox: (request: CreateSandboxRequest) => Promise<string>
  startSandbox: (id: string) => Promise<void>
  stopSandbox: (id: string) => Promise<void>
  deleteSandbox: (id: string) => Promise<void>
  // Status polling methods
  startStatusPolling: (sandboxId: string) => void
  stopStatusPolling: (sandboxId: string) => void
  stopAllPolling: () => void
}

export const useSandboxStore = create<SandboxState & SandboxActions>()(
  devtools(
    (set, get) => ({
      // State
      sandboxes: [],
      isLoading: false,
      error: null,
      pollingIntervals: {},

      // Actions
      setSandboxes: (sandboxes) => set({ sandboxes }),
      
      addSandbox: (sandbox) => set(state => ({
        sandboxes: [...(Array.isArray(state.sandboxes) ? state.sandboxes : []), sandbox]
      })),
      
      updateSandbox: (id, updates) => set(state => ({
        sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).map(s => 
          s.sandbox_id === id ? { ...s, ...updates } : s
        )
      })),
      
      removeSandbox: (id) => set(state => ({
        sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).filter(s => s.sandbox_id !== id)
      })),
      
      getSandbox: (id) => {
        const state = get()
        return (Array.isArray(state.sandboxes) ? state.sandboxes : []).find(s => s.sandbox_id === id)
      },
      
      updateSandboxStatus: (id, status) => set(state => ({
        sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).map(s => 
          s.sandbox_id === id ? { ...s, status } : s
        )
      })),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),
      
      clearError: () => set({ error: null }),

      // API methods
      loadSandboxes: async () => {
        try {
          set({ isLoading: true, error: null })
          const sandboxes = await SandboxService.listSandboxes()
          // Ensure sandboxes is always an array
          set({ sandboxes: Array.isArray(sandboxes) ? sandboxes : [], isLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load sandboxes',
            isLoading: false,
            sandboxes: [] // Ensure sandboxes is set to empty array on error
          })
        }
      },

      loadSandbox: async (id: string) => {
        try {
          const sandbox = await SandboxService.getSandbox(id)
          console.log(`📦 Loaded sandbox ${id} with status:`, sandbox.status)
          set(state => {
            const updatedSandboxes = (Array.isArray(state.sandboxes) ? state.sandboxes : []).map(s => 
              s.sandbox_id === id ? { ...s, ...sandbox } : s
            )
            console.log(`🔄 Updated sandbox in store:`, updatedSandboxes.find(s => s.sandbox_id === id)?.status)
            return { sandboxes: updatedSandboxes }
          })
        } catch (error) {
          console.error(`Failed to reload sandbox ${id}:`, error)
        }
      },

      createSandbox: async (request: CreateSandboxRequest) => {
        try {
          set({ isLoading: true, error: null })
          const sandbox = await SandboxService.createSandbox(request)
          set(state => ({
            sandboxes: [sandbox, ...(Array.isArray(state.sandboxes) ? state.sandboxes : [])],
            isLoading: false
          }))
          // Refresh the sandbox list to ensure consistency
          await get().loadSandboxes()
          return sandbox.sandbox_id
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to create sandbox',
            isLoading: false 
          })
          throw error
        }
      },

      startSandbox: async (id: string) => {
        try {
          await SandboxService.startSandbox(id)
          set(state => ({
            sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).map(s => 
              s.sandbox_id === id ? { ...s, status: 'starting' as SandboxStatus } : s
            )
          }))
          // Start polling for status updates
          get().startStatusPolling(id)
        } catch (error) {
          console.error(`Failed to start sandbox ${id}:`, error)
          throw error
        }
      },

      stopSandbox: async (id: string) => {
        try {
          await SandboxService.stopSandbox(id)
          set(state => ({
            sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).map(s => 
              s.sandbox_id === id ? { ...s, status: 'stopping' as SandboxStatus } : s
            )
          }))
          // Start polling for status updates
          get().startStatusPolling(id)
        } catch (error) {
          console.error(`Failed to stop sandbox ${id}:`, error)
          throw error
        }
      },

      deleteSandbox: async (id: string) => {
        try {
          await SandboxService.deleteSandbox(id)
          // Remove from store
          set(state => ({
            sandboxes: (Array.isArray(state.sandboxes) ? state.sandboxes : []).filter(s => s.sandbox_id !== id)
          }))
          // Stop any polling for this sandbox
          get().stopStatusPolling(id)
        } catch (error) {
          console.error(`Failed to delete sandbox ${id}:`, error)
          throw error
        }
      },

      // Status polling methods with actual implementation
      startStatusPolling: (sandboxId: string) => {
        const interval = setInterval(async () => {
          try {
            await get().loadSandbox(sandboxId)
            const sandbox = get().getSandbox(sandboxId)
            if (sandbox?.status === 'running' || sandbox?.status === 'stopped' || sandbox?.status === 'error') {
              get().stopStatusPolling(sandboxId)
            }
          } catch (error) {
            console.error(`Error polling sandbox ${sandboxId}:`, error)
            get().stopStatusPolling(sandboxId)
          }
        }, 2000) // Poll every 2 seconds

        set(state => ({
          pollingIntervals: {
            ...state.pollingIntervals,
            [sandboxId]: interval
          }
        }))
      },
      
      stopStatusPolling: (sandboxId: string) => {
        const interval = get().pollingIntervals[sandboxId]
        if (interval) {
          clearInterval(interval)
          set(state => {
            const { [sandboxId]: removed, ...newIntervals } = state.pollingIntervals
            return { pollingIntervals: newIntervals }
          })
        }
      },
      
      stopAllPolling: () => {
        Object.values(get().pollingIntervals).forEach((interval) => {
          clearInterval(interval)
        })
        set({ pollingIntervals: {} })
      }
    }),
    { name: 'sandbox-store' }
  ))