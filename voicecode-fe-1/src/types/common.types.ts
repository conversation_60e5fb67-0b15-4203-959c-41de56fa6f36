// Common utility types
export type ApiResponse<T> = {
  data: T
  error?: {
    message: string
    code: string
  }
  meta?: {
    total?: number
    page?: number
    limit?: number
  }
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export type Theme = 'light' | 'dark' | 'system'

export type Size = 'sm' | 'md' | 'lg'

export type Variant = 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost'