import type { CommandStatus } from './command.types'
import type { LoadingState } from './common.types'

// Chat message types
export type ChatMessageType = 'user' | 'system' | 'error' | 'status'

export interface ChatMessage {
  id: string
  sandboxId: string
  userId: string
  messageType: ChatMessageType
  content: string
  commandId?: string
  createdAt: Date
  metadata?: ChatMessageMetadata
}

export interface ChatMessageMetadata {
  exitCode?: number
  executionTime?: number
  workingDirectory?: string
  commandStatus?: CommandStatus
  originalCommand?: string
  transcription?: string
  isVoiceCommand?: boolean
  status?: string
}

// Chat session management
export interface ChatSession {
  id: string
  sandboxId: string
  userId: string
  lastMessageAt: Date
  messageCount: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Zustand store state structure
export interface ChatState {
  // Message management - organized by sandbox for efficient access
  messagesBySandbox: Map<string, ChatMessage[]>
  activeSessionId: string | null
  
  // Execution state
  executionProgress: number
  currentCommandId: string | null
  
  // UI state
  inputValue: string
  isInputFocused: boolean
  isLoadingHistory: boolean
  
  // Error handling
  error: string | null
  loadingState: LoadingState
  
  // Pagination for chat history
  pagination: {
    [sandboxId: string]: {
      currentPage: number
      hasNextPage: boolean
      isLoading: boolean
    }
  }
}

// Chat store actions
export interface ChatActions {
  // Message management
  sendMessage: (sandboxId: string, content: string) => Promise<void>
  addMessage: (message: ChatMessage) => void
  addSystemMessage: (sandboxId: string, content: string, metadata?: ChatMessageMetadata) => void
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void
  
  // Command execution
  executeCommand: (sandboxId: string, command: string) => Promise<void>
  updateCommandStatus: (commandId: string, status: CommandStatus, metadata?: ChatMessageMetadata) => void
  cancelCommand: (commandId: string) => void
  
  // History management
  loadChatHistory: (sandboxId: string, page?: number) => Promise<void>
  clearChatHistory: (sandboxId: string) => Promise<void>
  
  // Session management
  setActiveSession: (sessionId: string | null) => void
  createSession: (sandboxId: string) => Promise<ChatSession>
  
  // UI state management
  setInputValue: (value: string) => void
  setInputFocus: (focused: boolean) => void
  setExecutionProgress: (progress: number) => void
  
  // Error handling
  setError: (error: string | null) => void
  clearError: () => void
  setLoadingState: (state: LoadingState) => void
  
  // Cleanup
  cleanupSandboxData: (sandboxId: string) => void
  reset: () => void
}

// Combined store type
export type ChatStore = ChatState & ChatActions

// API request/response types
export interface SendChatMessageRequest {
  message: string
  messageType: string  // Backend accepts 'user', 'command', etc.
  commandId?: string
  metadata?: Record<string, any>
}

export interface SendChatMessageResponse {
  messageId: string
  status: string
  commandId?: string
}

export interface ChatHistoryRequest {
  page?: number
  pageSize?: number
  messageType?: ChatMessageType
  fromDate?: string
  toDate?: string
}

export interface ChatHistoryResponse {
  messages: ChatMessage[]
  total: number
  page: number
  pageSize: number
  hasNextPage: boolean
}

export interface ChatStatusResponse {
  commandId: string | null
  status: CommandStatus
  progress: number
  message?: string
}

export interface ClearChatHistoryResponse {
  success: boolean
  deletedCount: number
}

// Chat component props
export interface ChatInterfaceProps {
  sandboxId: string
  isActive: boolean
  onCommandExecute?: (command: string) => void
  className?: string
}

export interface ChatMessageProps {
  message: ChatMessage
  isLatest?: boolean
  onRetry?: (messageId: string) => void
  className?: string
}

export interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: (message: string) => void
  disabled?: boolean
  placeholder?: string
  className?: string
}

// Chat validation and filtering
export interface ChatMessageFilter {
  sandboxId?: string
  messageType?: ChatMessageType
  commandId?: string
  userId?: string
  dateFrom?: Date
  dateTo?: Date
  searchTerm?: string
}

export interface ChatValidationResult {
  isValid: boolean
  requiresConfirmation: boolean
  reason?: string
  warningMessage?: string
  suggestedCommand?: string
}

// Chat preferences and settings
export interface ChatSettings {
  autoScroll: boolean
  showTimestamps: boolean
  showMetadata: boolean
  messagePageSize: number
  enableSoundNotifications: boolean
  compactMode: boolean
}

// Real-time updates
export interface ChatUpdateEvent {
  type: 'message' | 'status' | 'error' | 'session'
  sandboxId: string
  data: ChatMessage | ChatStatusResponse | { error: string } | ChatSession
}

// Export constants
export const CHAT_MESSAGE_TYPES = {
  USER: 'user' as const,
  SYSTEM: 'system' as const,
  ERROR: 'error' as const,
  STATUS: 'status' as const,
}

export const CHAT_DEFAULTS = {
  PAGE_SIZE: 50,
  MAX_MESSAGE_LENGTH: 10000,
  MAX_HISTORY_PAGES: 100,
  EXECUTION_TIMEOUT: 300000, // 5 minutes
  POLLING_INTERVAL: 1000, // 1 second
} as const

// Type guards
export const isChatMessage = (obj: unknown): obj is ChatMessage => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as any).id === 'string' &&
    typeof (obj as any).sandboxId === 'string' &&
    typeof (obj as any).userId === 'string' &&
    typeof (obj as any).messageType === 'string' &&
    typeof (obj as any).content === 'string' &&
    (obj as any).createdAt instanceof Date
  )
}

export const isUserMessage = (message: ChatMessage): boolean => {
  return message.messageType === CHAT_MESSAGE_TYPES.USER
}

export const isSystemMessage = (message: ChatMessage): boolean => {
  return message.messageType === CHAT_MESSAGE_TYPES.SYSTEM
}

export const isErrorMessage = (message: ChatMessage): boolean => {
  return message.messageType === CHAT_MESSAGE_TYPES.ERROR
}

export const isStatusMessage = (message: ChatMessage): boolean => {
  return message.messageType === CHAT_MESSAGE_TYPES.STATUS
}