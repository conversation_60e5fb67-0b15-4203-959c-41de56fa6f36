export interface Repository {
  id: number
  name: string
  full_name: string
  description: string | null
  language: string | null
  stargazers_count: number
  updated_at: string
  clone_url: string
  html_url: string
  private: boolean
  default_branch: string
  topics: string[]
  owner: {
    login: string
    avatar_url: string
  }
}

export interface Branch {
  name: string
  commit: {
    sha: string
    message: string
    author: {
      name: string
      email: string
      date: string
    }
    committer: {
      name: string
      email: string
      date: string
    }
  }
  protected: boolean
}

export interface RepositorySearchParams {
  query?: string
  sort?: 'updated' | 'created' | 'pushed' | 'full_name'
  order?: 'asc' | 'desc'
  per_page?: number
  page?: number
}

export interface RepositoryListResponse {
  total_count: number
  incomplete_results: boolean
  items: Repository[]
}

export interface BranchListResponse {
  branches: Branch[]
}

export interface RepositoryState {
  repositories: Repository[]
  selectedRepository: Repository | null
  branches: Branch[]
  selectedBranch: string | null
  searchQuery: string
  recentRepositories: Repository[]
  favorites: number[]
  isLoading: boolean
  isLoadingBranches: boolean
  error: string | null
  pagination: {
    currentPage: number
    totalPages: number
    hasNextPage: boolean
  }
}

export interface RepositoryActions {
  setRepositories: (repositories: Repository[]) => void
  setSelectedRepository: (repository: Repository | null) => void
  setBranches: (branches: Branch[]) => void
  setSelectedBranch: (branch: string | null) => void
  setSearchQuery: (query: string) => void
  addRecentRepository: (repository: Repository) => void
  toggleFavorite: (repositoryId: number) => void
  setLoading: (loading: boolean) => void
  setLoadingBranches: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (pagination: Partial<RepositoryState['pagination']>) => void
  loadRepositories: (params?: RepositorySearchParams) => Promise<void>
  loadBranches: (repository: Repository) => Promise<void>
  searchRepositories: (query: string) => Promise<void>
  clearSearch: () => void
  reset: () => void
}

export type RepositoryStore = RepositoryState & RepositoryActions