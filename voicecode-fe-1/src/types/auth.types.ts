export interface AuthUser {
  id: string
  username: string
  email: string
  avatar_url: string
  github_token: string
  createdAt?: string
  updatedAt?: string
}

export interface AuthToken {
  accessToken: string
  refreshToken?: string
  expiresAt: string
  tokenType?: string
}

export interface LoginRequest {
  code: string
  state?: string
}

export interface LoginResponse {
  user: AuthUser
  token?: AuthToken
  access_token: string
  token_type: string
  expires_in: number
}

export interface GitHubAuthResponse {
  auth_url: string
  state: string
}

export interface TokenValidationResponse {
  user: AuthUser
}

export interface TokenValidationResult {
  isValid: boolean
  errorMessage?: string
}

export interface AuthState {
  user: AuthUser | null
  token: string | null
  isLoading: boolean
  error: string | null
  isAuthenticated: boolean
}

export interface AuthActions {
  setUser: (user: AuthUser) => void
  setToken: (token: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  logout: () => void
  clearError: () => void
}

export interface ServiceCredentials {
  claudeCodeToken?: string
  daytonaToken?: string
}

export interface CredentialValidation {
  service: 'claude' | 'daytona'
  isValid: boolean
  isLoading: boolean
  error?: string
}

export interface GitHubOAuthConfig {
  clientId: string
  redirectUri: string
  scope: string[]
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  accessToken: string
  refreshToken?: string
  expiresAt: string
}