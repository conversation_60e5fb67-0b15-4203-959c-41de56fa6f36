export type SandboxStatus = 'starting' | 'running' | 'stopped' | 'stopping' | 'pending' | 'error'

export interface ResourceUsage {
  cpu: number
  memory: number
  storage: number
}

export interface Sandbox {
  id: string
  sandbox_id: string
  sandbox_name: string
  name: string
  repo_url: string
  branch: string
  status: SandboxStatus
  created_at: string
  timestamp: string
  avatar?: string
  lastMessage?: string
  unreadCount?: number
  resource_usage?: ResourceUsage
}

export interface CreateSandboxRequest {
  repo_url: string
  branch: string
  github_token?: string
  claude_code_oauth_token?: string
  sandbox_name?: string
  config?: SandboxConfig
}

export interface SandboxCreateRequest {
  repo_url: string
  branch: string
  github_token?: string
  claude_code_oauth_token?: string
  sandbox_name?: string
  config?: SandboxConfig
}

export interface SandboxConfig {
  cpu?: number
  memory?: number
  storage?: number
  auto_stop_interval?: number
  environment_variables?: Record<string, string>
}

export interface SandboxState {
  sandboxes: Sandbox[]
  activeSandbox: Sandbox | null
  isCreating: boolean
  isLoading: boolean
  error: string | null
}

export interface SandboxOperation {
  type: 'create' | 'start' | 'stop' | 'delete'
  sandbox_id?: string
  progress?: number
  message?: string
}

export interface CreateSandboxResponse {
  data: {
    sandbox_id: string
    sandbox_name: string
    repo_url: string
    branch: string
    status: string
    created_at: string
  }
  message: string
}

export interface SandboxListResponse {
  data: {
    sandboxes: {
      sandbox_id: string
      sandbox_name: string
      repo_url: string
      branch: string
      status: string
      created_at: string
      user_id: number
      last_synced?: string
    }[]
  }
  message: string
}

export interface SandboxOperationResponse {
  data: {
    success: boolean
    message: string
    sandbox?: Sandbox
  }
}