export type CommandStatus = 'pending' | 'executing' | 'success' | 'error' | 'cancelled'

export interface CommandExecuteRequest {
  command: string
  working_directory?: string
}

export interface CommandExecuteResponse {
  exit_code: number
  result: string
  execution_time: number
}

export interface Command {
  id: string
  originalVoice: string
  transcription: string
  finalCommand: string
  sandboxId: string
  executedAt: Date
  duration: number
  exitCode: number
  output: string
  status: CommandStatus
  workingDirectory?: string
}

export interface CommandValidationResult {
  isValid: boolean
  requiresConfirmation: boolean
  reason?: string
  warningMessage?: string
}

export interface CommandHistory {
  commands: Command[]
  totalCount: number
  currentPage: number
  pageSize: number
}

export interface CommandState {
  activeCommand: Command | null
  commandHistory: Command[]
  isExecuting: boolean
  executionProgress: number
  currentOutput: string
  error: string | null
  streamingConnection: WebSocket | EventSource | null
}

export interface CommandFilter {
  sandboxId?: string
  status?: CommandStatus
  searchTerm?: string
  dateFrom?: Date
  dateTo?: Date
}

export interface CommandTemplate {
  id: string
  name: string
  description: string
  command: string
  category: string
  isFavorite: boolean
}

export interface CommandHistoryItem {
  id: string
  original_voice?: string
  transcription?: string
  command: string
  sandbox_id: string
  executed_at: string
  duration?: number
  exit_code: number
  output?: string
  status: CommandStatus
  working_directory?: string
}

export const DESTRUCTIVE_COMMANDS = [
  'rm', 'rmdir', 'del', 'delete', 'mv', 'move',
  'format', 'mkfs', 'dd', 'shred', 'wipe'
]

export const PRIVILEGED_COMMANDS = [
  'sudo', 'su', 'chmod', 'chown', 'mount', 'umount',
  'kill', 'killall', 'reboot', 'shutdown', 'halt'
]

export const COMMAND_CATEGORIES = {
  FILE_SYSTEM: 'File System',
  GIT: 'Git',
  NPM: 'Package Management',
  BUILD: 'Build & Test',
  DOCKER: 'Docker',
  CLAUDE: 'Claude Code',
  GENERAL: 'General'
} as const