import { faker } from '@faker-js/faker';

export interface FakeSandbox {
  id: string;
  name: string;
  description: string;
  lastMessage: string;
  timestamp: Date;
  unreadCount: number;
  status: 'active' | 'idle' | 'stopped';
  avatar?: string;
}

export interface FakeMessage {
  id: string;
  sandboxId: string;
  type: 'user' | 'system' | 'code' | 'error' | 'voice';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  metadata?: {
    language?: string;
    duration?: number;
    fileName?: string;
    waveform?: number[];
    transcription?: string;
  };
}

// Generate fake sandboxes
export function generateFakeSandboxes(count: number = 10): FakeSandbox[] {
  return Array.from({ length: count }, () => ({
    id: faker.string.uuid(),
    name: faker.helpers.arrayElement([
      'voicecode-backend',
      'react-native-app',
      'express-api',
      'nextjs-dashboard',
      'python-ml-project',
      'rust-cli-tool',
      'vue-frontend',
      'django-rest-api',
    ]),
    description: faker.company.catchPhrase(),
    lastMessage: faker.helpers.arrayElement([
      'npm install completed successfully',
      'Server running on port 3000',
      'Build failed with 3 errors',
      'Tests passed: 42/42',
      'Deployed to production',
      'git push origin main',
      'Database migrated successfully',
    ]),
    timestamp: faker.date.recent({ days: 7 }),
    unreadCount: faker.number.int({ min: 0, max: 5 }),
    status: faker.helpers.arrayElement(['active', 'idle', 'stopped']),
    avatar: faker.helpers.maybe(() => faker.image.avatar(), { probability: 0.7 }),
  }));
}

// Generate fake messages
export function generateFakeMessages(sandboxId: string, count: number = 50): FakeMessage[] {
  const messages: FakeMessage[] = [];
  
  for (let i = 0; i < count; i++) {
    const type = faker.helpers.weightedArrayElement([
      { value: 'user', weight: 40 },
      { value: 'system', weight: 30 },
      { value: 'code', weight: 20 },
      { value: 'error', weight: 5 },
      { value: 'voice', weight: 5 },
    ]);

    const message: FakeMessage = {
      id: faker.string.uuid(),
      sandboxId,
      type,
      content: generateMessageContent(type),
      timestamp: faker.date.recent({ days: 1 }),
      status: faker.helpers.arrayElement(['sent', 'delivered', 'read']),
    };

    // Add metadata based on type
    if (type === 'code') {
      message.metadata = {
        language: faker.helpers.arrayElement(['javascript', 'typescript', 'python', 'rust', 'go']),
      };
    } else if (type === 'voice') {
      message.metadata = {
        duration: faker.number.int({ min: 1, max: 60 }),
        waveform: generateWaveform(),
        transcription: message.content,
      };
    }

    messages.push(message);
  }

  // Sort by timestamp
  return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

function generateMessageContent(type: FakeMessage['type']): string {
  switch (type) {
    case 'user':
      return faker.helpers.arrayElement([
        'Run the test suite',
        'Show me the current git status',
        'Install lodash package',
        'Create a new React component called Button',
        'Fix the TypeScript errors',
        'Deploy to staging environment',
        'Update the README file',
      ]);
    
    case 'system':
      return faker.helpers.arrayElement([
        'Command executed successfully',
        'Installing dependencies...',
        'Building project...',
        'Running tests...',
        'Deployment initiated',
        'Analyzing code...',
      ]);
    
    case 'code':
      return faker.helpers.arrayElement([
        `function greet(name: string) {
  return \`Hello, \${name}!\`;
}`,
        `const result = await fetch('/api/data');
const data = await result.json();
console.log(data);`,
        `interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}`,
        `npm install --save-dev @types/node
npm install express
npm install -D nodemon`,
      ]);
    
    case 'error':
      return faker.helpers.arrayElement([
        'Error: Cannot find module "express"',
        'TypeError: Cannot read property "map" of undefined',
        'Build failed: TypeScript compilation errors',
        'Error: ENOENT: no such file or directory',
      ]);
    
    case 'voice':
      return faker.helpers.arrayElement([
        'Create a new file called index.js',
        'Run the development server',
        'Show me all TypeScript errors',
        'Deploy this to production',
      ]);
    
    default:
      return faker.lorem.sentence();
  }
}

function generateWaveform(): number[] {
  return Array.from({ length: 50 }, () => faker.number.float({ min: 0.1, max: 1, fractionDigits: 2 }));
}

// Mock API delay
export function mockApiDelay(ms: number = 300): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}