import type { Message } from 'ai'
import { ChatService } from '@/services/chat.service'
import type { ChatMessage, ChatMessageType } from '@/types/chat.types'
import type { CommandExecuteResponse } from '@/types/command.types'

/**
 * FastAPI adapter for Vercel AI SDK
 * Bridges existing FastAPI backend with AI SDK patterns
 */
export class FastAPIAdapter {
  constructor(_authToken?: string) {}

  /**
   * Convert FastAPI ChatMessage to AI SDK Message format
   */
  static convertToAIMessage(fastApiMessage: ChatMessage): Message & { metadata?: any } {
    // Map FastAPI message types to AI SDK roles
    const role = FastAPIAdapter.mapMessageTypeToRole(fastApiMessage.messageType)
    
    return {
      id: fastApiMessage.id,
      role,
      content: fastApiMessage.content,
      createdAt: fastApiMessage.createdAt,
      metadata: {
        // Preserve all original metadata from backend
        ...fastApiMessage.metadata,
        // Add our standard fields
        sandboxId: fastApiMessage.sandboxId,
        userId: fastApiMessage.userId,
        messageType: fastApiMessage.messageType,
        commandId: fastApiMessage.commandId
      }
    } as Message & { metadata?: any }
  }

  /**
   * Convert AI SDK Message to FastAPI ChatMessage format
   */
  static convertFromAIMessage(aiMessage: Message & { metadata?: any }, sandboxId: string): ChatMessage {
    const messageType = FastAPIAdapter.mapRoleToMessageType(aiMessage.role)
    
    return {
      id: aiMessage.id,
      sandboxId,
      userId: aiMessage.metadata?.userId || 'current-user',
      messageType,
      content: aiMessage.content,
      commandId: aiMessage.metadata?.commandId,
      createdAt: aiMessage.createdAt || new Date(),
      metadata: aiMessage.metadata
    }
  }

  /**
   * Map FastAPI message types to AI SDK roles
   */
  private static mapMessageTypeToRole(messageType: ChatMessageType): Message['role'] {
    switch (messageType) {
      case 'user':
        return 'user'
      case 'system':
      case 'error':
      case 'status':
      default:
        return 'assistant'
    }
  }

  /**
   * Map AI SDK roles to FastAPI message types
   */
  private static mapRoleToMessageType(role: Message['role']): ChatMessageType {
    switch (role) {
      case 'user':
        return 'user'
      case 'assistant':
      case 'system':
      default:
        return 'system'
    }
  }

  /**
   * Load chat history and convert to AI SDK format
   */
  async loadHistory(sandboxId: string, page = 1): Promise<{
    messages: Message[]
    pagination: {
      page: number
      total: number
      pageSize: number
      hasNextPage: boolean
    }
  }> {
    try {
      const response = await ChatService.loadHistory(sandboxId, { page })
      
      const messages = response.messages.map(msg => 
        FastAPIAdapter.convertToAIMessage(msg)
      )

      return {
        messages,
        pagination: {
          page: response.page,
          total: response.total,
          pageSize: response.pageSize,
          hasNextPage: response.hasNextPage
        }
      }
    } catch (error) {
      console.error('Failed to load history:', error)
      throw new Error('Failed to load chat history')
    }
  }

  /**
   * Send message using FastAPI backend
   */
  async sendMessage(
    sandboxId: string, 
    content: string, 
    messageType: ChatMessageType = 'user'
  ): Promise<{
    messageId: string
    status: string
    commandId?: string
  }> {
    try {
      const response = await ChatService.sendMessage(sandboxId, content, messageType)
      return response
    } catch (error) {
      console.error('Failed to send message:', error)
      throw new Error('Failed to send message')
    }
  }

  /**
   * Send message with metadata (for command results)
   */
  async sendMessageWithMetadata(
    sandboxId: string,
    content: string,
    messageType: ChatMessageType,
    metadata: Record<string, any> = {}
  ): Promise<{
    messageId: string
    status: string
    commandId?: string
  }> {
    try {

      
      const response = await ChatService.sendMessage(sandboxId, content, messageType, metadata)
      return response
    } catch (error) {
      console.error('Failed to send message with metadata:', error)
      throw new Error('Failed to send message with metadata')
    }
  }

  /**
   * Execute command in sandbox
   */
  async executeCommand(sandboxId: string, command: string): Promise<CommandExecuteResponse> {
    try {
      const response = await ChatService.executeCommand(sandboxId, command)
      return response
    } catch (error) {
      console.error('Failed to execute command:', error)
      throw new Error('Failed to execute command')
    }
  }

  /**
   * Clear chat history
   */
  async clearHistory(sandboxId: string): Promise<{ success: boolean; deletedCount: number }> {
    try {
      const response = await ChatService.clearHistory(sandboxId)
      return response
    } catch (error) {
      console.error('Failed to clear history:', error)
      throw new Error('Failed to clear chat history')
    }
  }

  /**
   * Check if message is an executable command
   */
  isExecutableCommand(content: string): boolean {
    return ChatService.isExecutableCommand(content)
  }

  /**
   * Format command output for display
   */
  formatCommandOutput(response: CommandExecuteResponse): {
    content: string
    isError: boolean
  } {
    return ChatService.formatCommandOutput(response)
  }

  /**
   * Create an optimistic message for immediate UI feedback
   */
  createOptimisticMessage(sandboxId: string, content: string): Message {
    const tempMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      sandboxId,
      userId: 'current-user',
      messageType: 'user',
      content: content.trim(),
      createdAt: new Date(),
      metadata: {}
    }

    return FastAPIAdapter.convertToAIMessage(tempMessage)
  }

  /**
   * Create system message for command execution feedback
   */
  createSystemMessage(
    sandboxId: string, 
    content: string, 
    metadata?: Record<string, any>
  ): Message {
    const systemMessage: ChatMessage = {
      id: `system-${Date.now()}`,
      sandboxId,
      userId: 'system',
      messageType: 'system',
      content,
      createdAt: new Date(),
      metadata: metadata || {}
    }

    return FastAPIAdapter.convertToAIMessage(systemMessage)
  }

  /**
   * Create error message
   */
  createErrorMessage(
    sandboxId: string, 
    content: string, 
    metadata?: Record<string, any>
  ): Message {
    const errorMessage: ChatMessage = {
      id: `error-${Date.now()}`,
      sandboxId,
      userId: 'system',
      messageType: 'error',
      content,
      createdAt: new Date(),
      metadata: metadata || {}
    }

    return FastAPIAdapter.convertToAIMessage(errorMessage)
  }
}

/**
 * Create adapter instance with authentication
 */
export function createFastAPIAdapter(authToken?: string): FastAPIAdapter {
  return new FastAPIAdapter(authToken)
}
