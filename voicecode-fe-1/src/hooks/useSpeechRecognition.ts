/**
 * React hook for speech recognition functionality
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { SpeechRecognition } from '@capacitor-community/speech-recognition';
import { Capacitor } from '@capacitor/core';
import { useSpeechPermission } from './useSpeechPermission';

interface SpeechConfig {
  language: string;
  maxResults: number;
  partialResults: boolean;
  popup: boolean;
}

interface UseSpeechRecognitionResult {
  isListening: boolean;
  isStopping: boolean;
  transcript: string;
  finalTranscript: string;
  error: string | null;
  isSupported: boolean;
  permissionGranted: boolean;
  start: () => Promise<boolean>;
  stop: () => Promise<void>;
  reset: () => void;
}

const DEFAULT_CONFIG: SpeechConfig = {
  language: 'en-US',
  maxResults: 5,
  partialResults: true,
  popup: false // Use custom UI instead of system popup
};

export const useSpeechRecognition = (
  config: Partial<SpeechConfig> = {}
): UseSpeechRecognitionResult => {
  const [isListening, setIsListening] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [finalTranscript, setFinalTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(false);
  
  const configRef = useRef<SpeechConfig>({ ...DEFAULT_CONFIG, ...config });
  const transcriptRef = useRef<string>('');
  const { isGranted: permissionGranted, ensurePermissions, isAvailable } = useSpeechPermission();

  // Keep transcript ref in sync with state
  useEffect(() => {
    transcriptRef.current = transcript;
  }, [transcript]);

  // Check if speech recognition is supported
  useEffect(() => {
    const checkSupport = async () => {
      try {
        const platform = Capacitor.getPlatform();
        
        if (platform === 'web') {
          // Check browser support for web
          const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
          const mediaDevicesAvailable = 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
          setIsSupported(!!SpeechRecognition && mediaDevicesAvailable);
          console.log('Web speech support:', !!SpeechRecognition && mediaDevicesAvailable);
        } else {
          // Use plugin availability for native platforms
          console.log('Native platform speech support check:', { platform, isAvailable });
          setIsSupported(isAvailable);
        }
      } catch (error) {
        console.error('Error checking speech recognition support:', error);
        setIsSupported(false);
      }
    };

    checkSupport();
  }, [isAvailable]);

  // Set up event listeners
  useEffect(() => {
    const platform = Capacitor.getPlatform();
    
    const setupListeners = () => {
      if (platform === 'web') {
        // Web platform doesn't use event listeners from the plugin
        // The web implementation is handled directly in start/stop methods
        return;
      }
      
      // Listen for partial results (real-time transcription)
      SpeechRecognition.addListener('partialResults', (data) => {
        const partialText = data.matches?.[0] || '';
        setTranscript(partialText);
        setError(null);
      });

      // Listen for listening state changes
      SpeechRecognition.addListener('listeningState', (data) => {
        const listening = data.status === 'started';
        setIsListening(listening);
        
        if (data.status === 'stopped') {
          // When stopped, treat the last transcript as final
          const currentTranscript = transcriptRef.current;
          
          // Add a delay to ensure we have the latest transcript
          setTimeout(() => {
            if (currentTranscript) {
              setFinalTranscript(currentTranscript);
            }
            setIsStopping(false);
            setError(null);
          }, 300); // Increased delay for iOS to catch final results
        }
      });
    };

    if (isSupported) {
      setupListeners();
    }

    // Cleanup listeners on unmount
    return () => {
      if (isSupported && platform !== 'web') {
        SpeechRecognition.removeAllListeners();
      } else if (platform === 'web') {
        // Cleanup web speech recognition
        const recognition = window.__voiceCodeRecognition;
        if (recognition) {
          recognition.stop();
          window.__voiceCodeRecognition = undefined;
        }
      }
    };
  }, [isSupported]);

  const start = useCallback(async (): Promise<boolean> => {
    const platform = Capacitor.getPlatform();
    
    try {
      // Clear previous state
      setError(null);
      setTranscript('');
      
      // Check if already listening
      if (isListening) {
        console.warn('Already listening');
        return true;
      }

      // Check support
      if (!isSupported) {
        setError('Speech recognition not supported on this device');
        return false;
      }

      // Ensure permissions
      const hasPermission = await ensurePermissions();
      if (!hasPermission) {
        setError('Speech recognition permission denied');
        return false;
      }

      if (platform === 'web') {
        return startWebSpeechRecognition();
      } else {
        // Start recognition
        await SpeechRecognition.start(configRef.current);
        console.log('Speech recognition started');
        return true;
      }

    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      setError(error instanceof Error ? error.message : 'Failed to start speech recognition');
      return false;
    }
  }, [isListening, isSupported, ensurePermissions]);

  const startWebSpeechRecognition = useCallback((): boolean => {
    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        setError('Speech recognition not supported in this browser');
        return false;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = configRef.current.partialResults;
      recognition.lang = configRef.current.language;
      recognition.maxAlternatives = configRef.current.maxResults;

      recognition.onstart = () => {
        setIsListening(true);
        console.log('Web speech recognition started');
      };

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; ++i) {
          if (event.results[i].isFinal) {
            finalTranscript += event.results[i][0].transcript;
          } else {
            interimTranscript += event.results[i][0].transcript;
          }
        }

        if (interimTranscript) {
          setTranscript(interimTranscript);
        }
        
        if (finalTranscript) {
          setFinalTranscript(finalTranscript);
          setTranscript(finalTranscript);
        }
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Web speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
        console.log('Web speech recognition ended');
      };

      // Store recognition instance for cleanup
      window.__voiceCodeRecognition = recognition;
      recognition.start();
      
      return true;
    } catch (error) {
      console.error('Failed to start web speech recognition:', error);
      setError('Failed to start speech recognition');
      return false;
    }
  }, []);

  const stop = useCallback(async (): Promise<void> => {
    const platform = Capacitor.getPlatform();
    
    try {
      if (!isListening || isStopping) {
        console.warn('Not currently listening or already stopping');
        return;
      }

      // Set stopping state immediately
      setIsStopping(true);

      if (platform === 'web') {
        const recognition = window.__voiceCodeRecognition;
        if (recognition) {
          recognition.stop();
          window.__voiceCodeRecognition = undefined;
        }
        setIsListening(false);
        console.log('Web speech recognition stopped');
        // Reset stopping state for web since we don't get a stopped event
        setTimeout(() => setIsStopping(false), 300);
      } else {
        // For iOS/Android, the stopping state will be cleared in the listeningState handler
        await SpeechRecognition.stop();
        console.log('Speech recognition stopped');
      }

    } catch (error) {
      console.error('Failed to stop speech recognition:', error);
      setError(error instanceof Error ? error.message : 'Failed to stop speech recognition');
      setIsStopping(false);
    }
  }, [isListening, isStopping]);

  const reset = useCallback(() => {
    setTranscript('');
    setFinalTranscript('');
    setError(null);
  }, []);

  return {
    isListening,
    isStopping,
    transcript,
    finalTranscript,
    error,
    isSupported,
    permissionGranted,
    start,
    stop,
    reset,
  };
};