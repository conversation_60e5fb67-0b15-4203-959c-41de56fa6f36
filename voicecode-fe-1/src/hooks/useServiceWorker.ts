import { useEffect, useState } from 'react'
import { PlatformUtils } from '@/utils/platform'

export function useServiceWorker() {
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false)
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null)
  const [isOffline, setIsOffline] = useState(!navigator.onLine)

  useEffect(() => {
    // Skip service worker registration in native apps
    if (PlatformUtils.isNative()) {
      console.log('🔧 Running in native app - service worker disabled')
      return
    }

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((reg) => {
          console.log('🔧 Service worker registered successfully')
          setRegistration(reg)
          
          // Check for updates
          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setIsUpdateAvailable(true)
                }
              })
            }
          })
        })
        .catch((error) => {
          console.error('❌ Service worker registration failed:', error)
        })
    }

    // Handle online/offline status
    const handleOnline = () => setIsOffline(false)
    const handleOffline = () => setIsOffline(true)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const updateServiceWorker = () => {
    if (PlatformUtils.isNative()) {
      // In native apps, updates come through app stores
      console.log('🔧 Native app updates handled by app store')
      return
    }

    if (registration?.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }

  return {
    isUpdateAvailable: PlatformUtils.isNative() ? false : isUpdateAvailable,
    updateServiceWorker,
    isOffline,
    isNative: PlatformUtils.isNative()
  }
}

