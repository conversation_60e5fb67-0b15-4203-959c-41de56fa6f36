import { useEffect, useState } from 'react';
import { Network } from '@capacitor/network';
import { PlatformUtils } from '@/utils/platform';

export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [networkType, setNetworkType] = useState<string>('unknown');

  useEffect(() => {
    if (PlatformUtils.isNative()) {
      // Use Capacitor Network API for native apps
      const initializeNetworkStatus = async () => {
        const status = await Network.getStatus();
        setIsOnline(status.connected);
        setNetworkType(status.connectionType);

        const networkListener = await Network.addListener('networkStatusChange', (status) => {
          setIsOnline(status.connected);
          setNetworkType(status.connectionType);
        });

        return networkListener;
      };

      let listenerHandle: { remove: () => void } | null = null;
      
      initializeNetworkStatus().then((handle) => {
        listenerHandle = handle;
      });

      return () => {
        if (listenerHandle) {
          listenerHandle.remove();
        }
      };
    } else {
      // Use web APIs for browser
      const handleOnline = () => setIsOnline(true);
      const handleOffline = () => setIsOnline(false);

      setIsOnline(navigator.onLine);

      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  return { isOnline, networkType };
}