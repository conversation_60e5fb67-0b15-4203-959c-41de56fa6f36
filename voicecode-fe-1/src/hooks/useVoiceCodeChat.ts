import { useChat } from 'ai/react'
import { useState, useEffect, useCallback, useRef } from 'react'
import type { Message } from 'ai'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'

interface UseVoiceCodeChatOptions {
  initialMessages?: Message[]
  onCommandExecute?: (command: string, sandboxId: string) => void
  onError?: (error: Error) => void
}

interface UseVoiceCodeChatReturn {
  messages: Message[]
  input: string
  setInput: (value: string) => void
  handleSubmit: (e?: React.FormEvent) => void
  isLoading: boolean
  isLoadingHistory: boolean
  isSendingMessage: boolean
  error: string | null
  reload: () => void
  clearHistory: () => Promise<void>
  executeCommand: (command: string) => Promise<void>
  pagination: {
    page: number
    total: number
    hasNextPage: boolean
  }
  loadMoreHistory: () => Promise<void>
}

/**
 * Custom hook that bridges Vercel AI SDK with VoiceCode's FastAPI backend
 */
export function useVoiceCodeChat(
  sandboxId: string,
  options: UseVoiceCodeChatOptions = {}
): UseVoiceCodeChatReturn {
  const [adapter] = useState(() => new FastAPIAdapter())
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isExecutingCommand, setIsExecutingCommand] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    hasNextPage: false
  })
  const [historyLoaded, setHistoryLoaded] = useState(false)
  const loadingRef = useRef(false)

  // Use AI SDK's useChat hook with custom configuration
  const {
    messages,
    input,
    setInput,
    isLoading,
    setMessages
  } = useChat({
    initialMessages: options.initialMessages || [],
    onError: (error) => {
      setError(error.message)
      options.onError?.(error)
    },
    // We'll handle API calls manually through FastAPI adapter
    api: '/api/chat/dummy', // Dummy endpoint, won't be used
    onFinish: () => {
      // This won't be called since we handle everything manually
    }
  })

  /**
   * Load initial chat history
   */
  const loadHistory = useCallback(async () => {
    if (loadingRef.current || historyLoaded) return
    
    loadingRef.current = true
    setIsLoadingHistory(true)
    setError(null)

    try {
      const response = await adapter.loadHistory(sandboxId, 1)
      
      setMessages(response.messages)
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
      
      setHistoryLoaded(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load history'
      setError(errorMessage)
      console.error('Failed to load chat history:', err)
    } finally {
      setIsLoadingHistory(false)
      loadingRef.current = false
    }
  }, [sandboxId, adapter, setMessages, historyLoaded])

  /**
   * Load more history for pagination
   */
  const loadMoreHistory = useCallback(async () => {
    if (!pagination.hasNextPage || isLoadingHistory) return

    setIsLoadingHistory(true)
    try {
      const response = await adapter.loadHistory(sandboxId, pagination.page + 1)
      
      // Prepend older messages
      setMessages(prevMessages => [...response.messages, ...prevMessages])
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more history'
      setError(errorMessage)
    } finally {
      setIsLoadingHistory(false)
    }
  }, [sandboxId, adapter, pagination, isLoadingHistory, setMessages])

  /**
   * Execute command in sandbox
   */
  const executeCommand = useCallback(async (command: string) => {
    setIsExecutingCommand(true)
    setError(null)

    try {
      const response = await adapter.executeCommand(sandboxId, command)
      const { content, isError } = adapter.formatCommandOutput(response)
      
      
      // Send the command result to backend for persistence
      const resultContent = content || '(No output)'
      const messageType = isError ? 'error' : 'system'
      
      try {
        // Persist the command result to backend with metadata
        const commandMetadata = {
          exitCode: response.exit_code,
          executionTime: response.execution_time,
          originalCommand: command
        }
        
        await adapter.sendMessageWithMetadata(
          sandboxId,
          resultContent,
          messageType as 'user' | 'system' | 'error' | 'status',
          commandMetadata
        )
        
        // Refresh history to get the persisted message with correct backend ID and metadata
        // This ensures persistence and prevents messages from disappearing on reload
        const historyResponse = await adapter.loadHistory(sandboxId, 1)
        setMessages(historyResponse.messages)
        setPagination({
          page: historyResponse.pagination.page,
          total: historyResponse.pagination.total,
          hasNextPage: historyResponse.pagination.hasNextPage
        })
      } catch (persistError) {
        console.error('Failed to persist command result:', persistError)
        // Still show the result locally even if persistence fails
        const resultMessage = isError 
          ? adapter.createErrorMessage(sandboxId, resultContent, {
              exitCode: response.exit_code,
              executionTime: response.execution_time,
              originalCommand: command
            })
          : adapter.createSystemMessage(sandboxId, resultContent, {
              exitCode: response.exit_code,
              executionTime: response.execution_time,
              originalCommand: command
            })

        setMessages(prev => [...prev, resultMessage])
      }
      
      options.onCommandExecute?.(command, sandboxId)
    } catch (err) {
      
      const errorMessage = err instanceof Error ? err.message : 'Command execution failed'
      
      try {
        // Persist error to backend with metadata
        const errorMetadata = { originalCommand: command }
        
        await adapter.sendMessageWithMetadata(sandboxId, errorMessage, 'error', errorMetadata)
        
        // Refresh history to get the persisted error message
        const historyResponse = await adapter.loadHistory(sandboxId, 1)
        setMessages(historyResponse.messages)
        setPagination({
          page: historyResponse.pagination.page,
          total: historyResponse.pagination.total,
          hasNextPage: historyResponse.pagination.hasNextPage
        })
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
        
        // Fallback: show local error message
        const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage, {
          originalCommand: command
        })
        
        setMessages(prev => [...prev, errorMsg])
      }
      setError(errorMessage)
    } finally {
      setIsExecutingCommand(false)
    }
  }, [sandboxId, adapter, setMessages, options])

  /**
   * Custom submit handler that uses FastAPI backend
   */
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault()
    
    if (!input.trim() || isLoading || isExecutingCommand) return

    const message = input.trim()
    setInput('') // Clear input immediately for better UX
    setError(null)

    // Add optimistic user message
    const userMessage = adapter.createOptimisticMessage(sandboxId, message)
    setMessages(prev => [...prev, userMessage])

    try {
      // Send message to FastAPI backend for persistence
      await adapter.sendMessage(sandboxId, message, 'user')
      
      // Check if this is a command that should be executed
      if (adapter.isExecutableCommand(message)) {
        await executeCommand(message)
      } else {
        // For non-commands, refresh history to get the persisted message with correct backend ID
        try {
          const historyResponse = await adapter.loadHistory(sandboxId, 1)
          setMessages(historyResponse.messages)
          setPagination({
            page: historyResponse.pagination.page,
            total: historyResponse.pagination.total,
            hasNextPage: historyResponse.pagination.hasNextPage
          })
        } catch (historyError) {
          console.error('Failed to refresh history after sending message:', historyError)
          // Keep the optimistic message if history refresh fails
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message'
      setError(errorMessage)
      
      // Remove the optimistic message since sending failed
      setMessages(prev => prev.filter(m => m.id !== userMessage.id))
      
      // Add error message to chat and persist it
      try {
        await adapter.sendMessage(sandboxId, errorMessage, 'error')
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
      }
      
      const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage)
      setMessages(prev => [...prev, errorMsg])
    }
  }, [input, isLoading, isExecutingCommand, sandboxId, adapter, setInput, setMessages, executeCommand])

  /**
   * Clear chat history
   */
  const clearHistory = useCallback(async () => {
    try {
      await adapter.clearHistory(sandboxId)
      setMessages([])
      setPagination({
        page: 1,
        total: 0,
        hasNextPage: false
      })
      setHistoryLoaded(false)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history'
      setError(errorMessage)
    }
  }, [sandboxId, adapter, setMessages])

  /**
   * Reload current conversation
   */
  const reloadConversation = useCallback(() => {
    setHistoryLoaded(false)
    loadHistory()
  }, [loadHistory])

  // Load history on mount or sandboxId change
  useEffect(() => {
    if (sandboxId) {
      setHistoryLoaded(false)
      loadHistory()
    }
  }, [sandboxId]) // Only depend on sandboxId to avoid infinite loops

  return {
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading: isLoading || false, // AI SDK isLoading
    isLoadingHistory,
    isSendingMessage: isLoading || isExecutingCommand, // Map AI SDK loading and command execution to our sending state
    error,
    reload: reloadConversation,
    clearHistory,
    executeCommand,
    pagination,
    loadMoreHistory
  }
}
