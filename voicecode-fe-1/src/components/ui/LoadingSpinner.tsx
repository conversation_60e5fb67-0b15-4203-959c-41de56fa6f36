import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <Loader2
      className={cn(
        'animate-spin',
        sizeClasses[size],
        className
      )}
    />
  );
}

interface LoadingStatesProps {
  isLoading: boolean;
  loadingText?: string;
  children: React.ReactNode;
  className?: string;
}

export function LoadingStates({ 
  isLoading, 
  loadingText = 'Loading...', 
  children, 
  className 
}: LoadingStatesProps) {
  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <div className="flex flex-col items-center gap-3">
          <LoadingSpinner size="lg" />
          <p className="text-sm text-gray-600 dark:text-gray-400 animate-pulse">
            {loadingText}
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

import { Skeleton } from '@/components/ui/skeleton';

interface PulseSkeletonProps {
  className?: string;
  width?: string;
  height?: string;
}

export function PulseSkeleton({ className, width = 'w-full', height = 'h-4' }: PulseSkeletonProps) {
  return (
    <Skeleton
      className={cn(
        width,
        height,
        className
      )}
    />
  );
}

interface SkeletonCardProps {
  showAvatar?: boolean;
  lines?: number;
  className?: string;
}

export function SkeletonCard({ showAvatar = false, lines = 3, className }: SkeletonCardProps) {
  return (
    <div className={cn('p-4 space-y-3', className)}>
      {showAvatar && (
        <div className="flex items-center space-x-3">
          <PulseSkeleton className="w-10 h-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <PulseSkeleton width="w-1/4" height="h-3" />
            <PulseSkeleton width="w-1/6" height="h-3" />
          </div>
        </div>
      )}
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <PulseSkeleton 
            key={i} 
            width={i === lines - 1 ? 'w-3/4' : 'w-full'} 
            height="h-4" 
          />
        ))}
      </div>
    </div>
  );
}

import { Progress } from '@/components/ui/progress';

interface ProgressBarProps {
  progress: number;
  className?: string;
  showLabel?: boolean;
}

export function ProgressBar({ progress, className, showLabel = false }: ProgressBarProps) {
  const clampedProgress = Math.max(0, Math.min(100, progress));
  
  return (
    <div className={cn('space-y-2', className)}>
      {showLabel && (
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
          <span>Progress</span>
          <span>{clampedProgress}%</span>
        </div>
      )}
      <Progress value={clampedProgress} className="w-full" />
    </div>
  );
}

interface DotLoaderProps {
  className?: string;
}

export function DotLoader({ className }: DotLoaderProps) {
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
          style={{
            animationDelay: `${i * 0.1}s`,
            animationDuration: '0.6s',
          }}
        />
      ))}
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
}

export function LoadingOverlay({ isLoading, message = 'Loading...', children }: LoadingOverlayProps) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-3">
            <LoadingSpinner size="lg" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {message}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}