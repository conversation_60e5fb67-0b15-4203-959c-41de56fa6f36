import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { RefreshCw } from 'lucide-react'
import { useServiceWorker } from '@/hooks/useServiceWorker'

export function UpdatePrompt() {
  const { isUpdateAvailable, updateServiceWorker } = useServiceWorker()

  if (!isUpdateAvailable) return null

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 sm:left-auto sm:right-4 sm:w-96">
      <Card className="p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Update Available</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              A new version is ready to install
            </p>
          </div>
          <Button
            size="sm"
            onClick={updateServiceWorker}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Update
          </Button>
        </div>
      </Card>
    </div>
  )
}