import * as React from "react"
import { Search, Plus } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { useAutoAnimate } from "@formkit/auto-animate/react"

interface SearchableListProps<T> {
  title: string
  subtitle?: string
  items: T[]
  isLoading?: boolean
  searchQuery: string
  onSearchChange: (query: string) => void
  onCreateNew?: () => void
  createButtonText?: string
  searchPlaceholder?: string
  emptyStateText?: string
  noResultsText?: string
  renderItem: (item: T, index: number) => React.ReactNode
  getItemKey: (item: T, index: number) => string | number
  className?: string
  headerActions?: React.ReactNode
}

function SearchableList<T>({
  title,
  subtitle,
  items,
  isLoading = false,
  searchQuery,
  onSearchChange,
  onCreateNew,
  createButtonText = "Create New",
  searchPlaceholder = "Search...",
  emptyStateText = "No items yet",
  noResultsText = "No results found",
  renderItem,
  getItemKey,
  className,
  headerActions,
}: SearchableListProps<T>) {
  const [listParent] = useAutoAnimate()

  return (
    <div className={cn("h-full bg-gray-50 dark:bg-gray-900 flex flex-col", className)}>
      {/* Header */}
      <header className="bg-white dark:bg-gray-950 border-b border-gray-200 dark:border-gray-800 shadow-sm">
        <div className="px-4 xs:px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl xs:text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
              {subtitle && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {headerActions}
              {onCreateNew && (
                <Button
                  size="icon"
                  variant="ghost"
                  className="min-touch-target smooth-transition hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  onClick={onCreateNew}
                >
                  <Plus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </Button>
              )}
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-9 h-11 text-[16px] bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-lg"
            />
          </div>
        </div>
      </header>

      {/* List */}
      <ScrollArea className="flex-1">
        <div className="py-2" ref={listParent}>
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="px-3 xs:px-4 py-2">
                <Card className="p-3 xs:p-4">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 xs:h-12 xs:w-12 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-24 xs:w-32" />
                      <Skeleton className="h-3 w-full" />
                    </div>
                  </div>
                </Card>
              </div>
            ))
          ) : items.length === 0 ? (
            // Empty state
            <div className="px-3 xs:px-4 py-16 text-center">
              <p className="text-sm xs:text-base text-gray-500 dark:text-gray-400 mb-4">
                {searchQuery ? noResultsText : emptyStateText}
              </p>
              {!searchQuery && onCreateNew && (
                <Button onClick={onCreateNew}>
                  <Plus className="mr-2 h-4 w-4" />
                  {createButtonText}
                </Button>
              )}
            </div>
          ) : (
            // Items list
            items.map((item, index) => (
              <div key={getItemKey(item, index)}>
                {renderItem(item, index)}
              </div>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

export { SearchableList, type SearchableListProps }