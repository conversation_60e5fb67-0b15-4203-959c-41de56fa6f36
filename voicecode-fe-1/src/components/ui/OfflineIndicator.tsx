import { WifiOff } from 'lucide-react'
import { useServiceWorker } from '@/hooks/useServiceWorker'

export function OfflineIndicator() {
  const { isOffline } = useServiceWorker()

  if (!isOffline) return null

  return (
    <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-white text-center py-2 z-50">
      <div className="flex items-center justify-center gap-2 text-sm">
        <WifiOff className="h-4 w-4" />
        You're offline - Some features may be limited
      </div>
    </div>
  )
}