import React from 'react'
import { cn } from '@/lib/utils'

export interface SliderProps {
  value: number[]
  onValueChange: (value: number[]) => void
  min?: number
  max?: number
  step?: number
  disabled?: boolean
  className?: string
}

const Slider = React.forwardRef<HTMLDivElement, SliderProps>(
  ({ value, onValueChange, min = 0, max = 100, step = 1, disabled = false, className }, ref) => {
    const percentage = ((value[0] - min) / (max - min)) * 100

    const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
      if (disabled) return

      const rect = e.currentTarget.getBoundingClientRect()
      const updateValue = (clientX: number) => {
        const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100))
        const newValue = min + (percentage / 100) * (max - min)
        const steppedValue = Math.round(newValue / step) * step
        onValueChange([Math.max(min, Math.min(max, steppedValue))])
      }

      updateValue(e.clientX)

      const handleMouseMove = (e: MouseEvent) => {
        updateValue(e.clientX)
      }

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return (
      <div
        ref={ref}
        className={cn(
          'relative flex w-full touch-none select-none items-center',
          className
        )}
      >
        <div
          className={cn(
            'relative h-1.5 w-full grow overflow-hidden rounded-full bg-secondary',
            disabled && 'opacity-50'
          )}
          onMouseDown={handleMouseDown}
        >
          <div
            className="absolute h-full bg-primary"
            style={{ width: `${percentage}%` }}
          />
        </div>
        <div
          className={cn(
            'absolute block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
            disabled && 'opacity-50'
          )}
          style={{ left: `calc(${percentage}% - 8px)` }}
        />
      </div>
    )
  }
)
Slider.displayName = 'Slider'

export { Slider }