import { AlertTriangle, Home, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface ErrorFallbackProps {
  error?: Error | string
  resetError?: () => void
  showHomeButton?: boolean
}

export function ErrorFallback({ 
  error = "Something went wrong", 
  resetError, 
  showHomeButton = true 
}: ErrorFallbackProps) {
  const errorMessage = typeof error === 'string' ? error : error.message

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md p-8 text-center">
        <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-6" />
        
        <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
          Oops! Something went wrong
        </h1>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {errorMessage}
        </p>
        
        <div className="space-y-3">
          {resetError && (
            <Button onClick={resetError} className="w-full" variant="default">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try again
            </Button>
          )}
          
          {showHomeButton && (
            <Button 
              onClick={() => window.location.href = '/login'} 
              className="w-full" 
              variant="outline"
            >
              <Home className="mr-2 h-4 w-4" />
              Go to login
            </Button>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mt-6">
          If this problem persists, please check your internet connection or try refreshing the page.
        </p>
      </Card>
    </div>
  )
}