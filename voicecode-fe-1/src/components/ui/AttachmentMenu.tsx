import { Plus, Image, FileText, Code, Paperclip } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface AttachmentMenuProps {
  onSelectType: (type: 'image' | 'document' | 'code' | 'other') => void;
}

export function AttachmentMenu({ onSelectType }: AttachmentMenuProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="min-h-touch-target min-w-touch-target"
        >
          <Plus className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" side="top" className="w-48">
        <DropdownMenuItem onClick={() => onSelectType('image')}>
          <Image className="mr-2 h-4 w-4" />
          Photo & Video
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onSelectType('document')}>
          <FileText className="mr-2 h-4 w-4" />
          Document
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onSelectType('code')}>
          <Code className="mr-2 h-4 w-4" />
          Code File
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onSelectType('other')}>
          <Paperclip className="mr-2 h-4 w-4" />
          Other
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}