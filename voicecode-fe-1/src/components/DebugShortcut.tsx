import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export const DebugShortcut: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Press Ctrl+Shift+D (or Cmd+Shift+D on Mac) to go to debug page
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        navigate('/debug');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [navigate]);

  return null; // This component doesn't render anything
};