import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import { useRepositoryStore } from '@/store/repository.store'
import { useSandboxStore } from '@/store/sandbox.store'
import { RepositoryStep } from './wizard-steps/RepositoryStep'

import { CredentialsStep } from './wizard-steps/CredentialsStep'
import type { SandboxCreateRequest } from '@/types/sandbox.types'

interface CreateSandboxWizardProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (sandboxId: string) => void
}

type WizardStep = 'repository' | 'credentials'

export function CreateSandboxWizard({ isOpen, onClose, onSuccess }: CreateSandboxWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>('repository')
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isTransitioning, setIsTransitioning] = useState(false)

  console.log('🔍 [CreateSandboxWizard] Component render:', {
    isOpen,
    currentStep,
    isCreating,
    error,
    isTransitioning,
    platform: navigator.userAgent.includes('iPhone') ? 'iOS' : 'other'
  })
  
  const [formData, setFormData] = useState<Partial<SandboxCreateRequest>>({
    branch: 'main',
    config: {
      cpu: 1,
      memory: 1,
      storage: 3,
      auto_stop_interval: 15
    }
  })
  
  const { clearSelection } = useRepositoryStore()
  const { selectedRepository, selectedBranch } = useRepositoryStore()
  const createSandbox = useSandboxStore(state => state.createSandbox)

  useEffect(() => {
    console.log('🔍 [CreateSandboxWizard] useEffect triggered:', { isOpen })
    
    if (!isOpen) {
      console.log('🔍 [CreateSandboxWizard] Dialog closed, resetting state')
      setCurrentStep('repository')
      setFormData({
        branch: 'main',
        config: {
          cpu: 1,
          memory: 2,
          storage: 3,
          auto_stop_interval: 15
        }
      })
      setError(null)
      clearSelection()
    }
  }, [isOpen, clearSelection])

  const steps: WizardStep[] = ['repository', 'credentials']
  const currentStepIndex = steps.indexOf(currentStep)
  
  const canProceed = () => {
    switch (currentStep) {
      case 'repository':
        const canProceedRepo = selectedRepository && selectedBranch
        console.log('🔍 [CreateSandboxWizard] canProceed repository step:', {
          selectedRepository: selectedRepository?.name || null,
          selectedBranch,
          canProceed: canProceedRepo
        })
        return canProceedRepo
      case 'credentials':
        const canProceedCreds = formData.claude_code_oauth_token?.trim()
        console.log('🔍 [CreateSandboxWizard] canProceed credentials step:', {
          hasToken: !!canProceedCreds,
          canProceed: canProceedCreds
        })
        return canProceedCreds
      default:
        console.log('🔍 [CreateSandboxWizard] canProceed unknown step:', currentStep)
        return false
    }
  }

  const handleNext = () => {
    console.log('🔍 [CreateSandboxWizard] handleNext called:', {
      currentStep,
      currentStepIndex,
      stepsLength: steps.length,
      canProceed: canProceed(),
      selectedRepository: selectedRepository?.name || null,
      selectedBranch,
      platform: navigator.userAgent.includes('iPhone') ? 'iOS' : 'other'
    })
    
    if (currentStepIndex < steps.length - 1) {
      const nextStep = steps[currentStepIndex + 1]
      console.log('🔍 [CreateSandboxWizard] Moving to next step:', nextStep)
      setIsTransitioning(true)
      
      // Add a small delay for iOS to prevent dialog closing
      if (navigator.userAgent.includes('iPhone')) {
        setTimeout(() => {
          setCurrentStep(nextStep)
          setTimeout(() => setIsTransitioning(false), 200)
        }, 50)
      } else {
        setCurrentStep(nextStep)
        setTimeout(() => setIsTransitioning(false), 100)
      }
    } else {
      console.log('🔍 [CreateSandboxWizard] Already at last step, cannot proceed')
    }
  }

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1])
    }
  }

  const handleCreate = async () => {
    if (!selectedRepository || !selectedBranch || !formData.claude_code_oauth_token) {
      setError('Missing required fields')
      return
    }

    setIsCreating(true)
    setError(null)

    try {
      const request: SandboxCreateRequest = {
        repo_url: selectedRepository.html_url,
        branch: selectedBranch,
        claude_code_oauth_token: formData.claude_code_oauth_token,
        sandbox_name: formData.sandbox_name || selectedRepository.name,
        config: formData.config
      }

      const sandboxId = await createSandbox(request)
      onSuccess(sandboxId)
      onClose()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create sandbox')
    } finally {
      setIsCreating(false)
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 'repository':
        return <RepositoryStep />
      case 'credentials':
        return (
          <CredentialsStep
            token={formData.claude_code_oauth_token || ''}
            sandboxName={formData.sandbox_name || ''}
            onTokenChange={(token) => setFormData({ ...formData, claude_code_oauth_token: token })}
            onNameChange={(name: string) => setFormData({ ...formData, sandbox_name: name })}
          />
        )
    }
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case 'repository':
        return 'Select Repository'
      case 'credentials':
        return 'Configure & Authenticate'
    }
  }

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={(open) => {
        console.log('🔍 [CreateSandboxWizard] Dialog onOpenChange:', { 
          open, 
          currentOpen: isOpen, 
          currentStep,
          isTransitioning,
          stackTrace: new Error().stack?.split('\n').slice(0, 5).join('\n'),
          platform: navigator.userAgent.includes('iPhone') ? 'iOS' : 'other'
        })
        if (!open) {
          console.log('🔍 [CreateSandboxWizard] Dialog closing via onOpenChange - investigating cause')
          // Prevent dialog from closing during step transitions on iOS
          if (isTransitioning && navigator.userAgent.includes('iPhone')) {
            console.log('🔍 [CreateSandboxWizard] Preventing dialog close during iOS step transition')
            return
          }
        }
        onClose()
      }}
    >
      <DialogContent className="max-w-3xl max-h-[90vh] w-[85vw] sm:w-full overflow-hidden flex flex-col border-0 shadow-2xl rounded-2xl">
        <DialogHeader className="pb-4 sm:pb-6">
          <DialogTitle className="text-xl sm:text-2xl font-semibold tracking-tight text-center">
            {getStepTitle()}
          </DialogTitle>
        </DialogHeader>

        {/* Progress indicator */}
        <div className="flex items-center justify-center mb-6 sm:mb-8">
          {steps.map((step, index) => (
            <div key={step} className="flex items-center">
              <div className={`
                w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-200
                ${index <= currentStepIndex 
                  ? 'bg-primary text-primary-foreground shadow-md' 
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                {index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  w-16 sm:w-20 h-0.5 mx-2 sm:mx-3 transition-all duration-200
                  ${index < currentStepIndex 
                    ? 'bg-primary' 
                    : 'bg-muted'
                  }
                `} />
              )}
            </div>
          ))}
        </div>

        {/* Step content */}
        <div className="flex-1 overflow-y-auto min-h-[280px] sm:min-h-[400px] px-1">
          {renderStep()}
        </div>

        {/* Error message */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3 mt-4">
            <div className="text-sm text-destructive font-medium">
              {error}
            </div>
          </div>
        )}

        {/* Navigation buttons */}
        <div className="flex justify-between items-center mt-6 sm:mt-8 pt-4 sm:pt-6">
          <Button
            variant="ghost"
            onClick={handleBack}
            disabled={currentStepIndex === 0 || isCreating}
            className="h-9 sm:h-10 px-4 sm:px-6"
          >
            <ChevronLeft className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Back</span>
          </Button>

          <div className="flex gap-2 sm:gap-3">
            <Button 
              variant="outline" 
              onClick={onClose} 
              disabled={isCreating}
              className="h-9 sm:h-10 px-4 sm:px-6"
            >
              Cancel
            </Button>
            
            {currentStepIndex < steps.length - 1 ? (
              <Button
                onClick={() => {
                  console.log('🔍 [CreateSandboxWizard] Next button clicked')
                  handleNext()
                }}
                disabled={!canProceed()}
                className="h-9 sm:h-10 px-4 sm:px-6"
              >
                Next
                <ChevronRight className="ml-1 sm:ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleCreate}
                disabled={!canProceed() || isCreating}
                className="h-9 sm:h-10 px-4 sm:px-8"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="mr-1 sm:mr-2 h-4 w-4 animate-spin" />
                    <span className="hidden sm:inline">Creating...</span>
                    <span className="sm:hidden">...</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Create Sandbox</span>
                    <span className="sm:hidden">Create</span>
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}