import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/card'
import { Info, Eye, EyeOff } from 'lucide-react'
import { useRepositoryStore } from '@/store/repository.store'

interface CredentialsStepProps {
  token: string
  sandboxName: string
  onTokenChange: (token: string) => void
  onNameChange: (name: string) => void
}

export function CredentialsStep({ token, sandboxName, onTokenChange, onNameChange }: CredentialsStepProps) {
  const [showToken, setShowToken] = useState(false)
  const { selectedRepository } = useRepositoryStore()

  console.log('🔍 [CredentialsStep] Component render:', {
    token: token ? '***' : 'empty',
    sandboxName,
    selectedRepository: selectedRepository?.name || null,
    platform: navigator.userAgent.includes('iPhone') ? 'iOS' : 'other'
  })

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="sandbox-name">
          Sandbox Name
        </Label>
        <Input
          id="sandbox-name"
          type="text"
          placeholder={selectedRepository?.name || "Enter sandbox name"}
          value={sandboxName}
          onChange={(e) => onNameChange(e.target.value)}
          className="mt-2"
        />
        <p className="text-xs text-muted-foreground mt-2">
          Choose a memorable name for your sandbox
        </p>
      </div>

      <Card className="p-4 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
        <div className="flex gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 dark:text-blue-100">
              Claude Code OAuth Token Required
            </p>
            <p className="text-blue-700 dark:text-blue-300 mt-1">
              This token allows Claude Code CLI to operate in your sandbox. 
              You can obtain it from your Claude Code settings.
            </p>
          </div>
        </div>
      </Card>

      <div>
        <Label htmlFor="claude-token">
          Claude Code OAuth Token <span className="text-red-500">*</span>
        </Label>
        <div className="relative mt-2">
          <Input
            id="claude-token"
            type={showToken ? 'text' : 'password'}
            placeholder="Enter your Claude Code OAuth token"
            value={token}
            onChange={(e) => onTokenChange(e.target.value)}
            className="pr-10"
          />
          <button
            type="button"
            onClick={() => setShowToken(!showToken)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          This token will be securely stored as an environment variable in your sandbox
        </p>
      </div>

      <Card className="p-4 mt-6">
        <h4 className="font-medium mb-3">Summary</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Repository:</span>
            <span className="font-medium truncate max-w-48">
              {/* This will be populated from parent component's state */}
              {useRepositoryStore.getState().selectedRepository?.name || 'Selected repository'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Branch:</span>
            <span className="font-medium">
              {useRepositoryStore.getState().selectedBranch || 'Selected branch'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Resources:</span>
            <span className="font-medium">
              {1} CPU, {1}GB RAM, {3}GB Storage
            </span>
          </div>
        </div>
      </Card>
    </div>
  )
}