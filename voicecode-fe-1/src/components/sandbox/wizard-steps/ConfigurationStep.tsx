import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card } from '@/components/ui/card'
import type { SandboxConfig } from '@/types/sandbox.types'

interface ConfigurationStepProps {
  config: Partial<SandboxConfig>
  sandboxName: string
  onConfigChange: (config: Partial<SandboxConfig>) => void
  onNameChange: (name: string) => void
}

export function ConfigurationStep({ 
  config, 
  sandboxName, 
  onConfigChange, 
  onNameChange 
}: ConfigurationStepProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="sandbox-name">Sandbox Name (optional)</Label>
        <Input
          id="sandbox-name"
          placeholder="my-awesome-project"
          value={sandboxName}
          onChange={(e) => onNameChange(e.target.value)}
          className="mt-2"
        />
        <p className="text-xs text-gray-500 mt-1">
          Leave empty to use repository name
        </p>
      </div>

      <Card className="p-4 space-y-4">
        <h3 className="font-medium">Resources</h3>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label>CPU Cores</Label>
            <span className="text-sm font-medium">{config.cpu || 2}</span>
          </div>
          <Slider
            value={[config.cpu || 2]}
            onValueChange={([value]) => onConfigChange({ ...config, cpu: value })}
            min={1}
            max={8}
            step={1}
          />
        </div>

        <div>
          <div className="flex justify-between mb-2">
            <Label>Memory (GB)</Label>
            <span className="text-sm font-medium">{config.memory || 4}</span>
          </div>
          <Slider
            value={[config.memory || 4]}
            onValueChange={([value]) => onConfigChange({ ...config, memory: value })}
            min={1}
            max={32}
            step={1}
          />
        </div>

        <div>
          <div className="flex justify-between mb-2">
            <Label>Storage (GB)</Label>
            <span className="text-sm font-medium">{config.storage || 10}</span>
          </div>
          <Slider
            value={[config.storage || 10]}
            onValueChange={([value]) => onConfigChange({ ...config, storage: value })}
            min={10}
            max={100}
            step={10}
          />
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex justify-between items-center">
          <div>
            <Label>Auto-stop after inactivity</Label>
            <p className="text-xs text-gray-500 mt-1">
              Automatically stop sandbox when idle
            </p>
          </div>
          <select
            value={config.auto_stop_interval || 15}
            onChange={(e) => onConfigChange({ 
              ...config, 
              auto_stop_interval: parseInt(e.target.value) 
            })}
            className="h-9 px-3 border rounded-md bg-white dark:bg-gray-900"
          >
            <option value={0}>Never</option>
            <option value={15}>15 minutes</option>
            <option value={30}>30 minutes</option>
            <option value={60}>1 hour</option>
            <option value={120}>2 hours</option>
          </select>
        </div>
      </Card>
    </div>
  )
}