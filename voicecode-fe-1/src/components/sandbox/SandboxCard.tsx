import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import type { Sandbox } from '@/types/sandbox.types';
import { SandboxService } from '@/services/sandbox.service';

interface SandboxCardProps {
  sandbox: Sandbox;
}

export function SandboxCard({ sandbox }: SandboxCardProps) {
  const navigate = useNavigate();

  const getStatusColor = (status: Sandbox['status']) => {
    return SandboxService.getStatusColor(status);
  };

  return (
    <div className="px-3 xs:px-4 py-1">
      <Card
        className="p-3 xs:p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 smooth-transition-normal active:scale-[0.98] transform touch-feedback"
        onClick={() => navigate(`/chat/${sandbox.sandbox_id || sandbox.id}`)}
      >
        <div className="flex items-center space-x-2 xs:space-x-3">
          <div className="relative flex-shrink-0">
            <Avatar className="h-10 w-10 xs:h-12 xs:w-12 smooth-transition">
              <AvatarImage src={sandbox.avatar} alt={sandbox.sandbox_name || sandbox.name || 'Sandbox'} />
              <AvatarFallback className="bg-blue-500 text-white font-medium text-sm xs:text-base">
                {(sandbox.sandbox_name || sandbox.name || 'S')[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className={cn(
              "absolute bottom-0 right-0 h-2.5 w-2.5 xs:h-3 xs:w-3 rounded-full border-2 border-white dark:border-gray-950 smooth-transition",
              getStatusColor(sandbox.status)
            )} />
          </div>
          
          <div className="flex-1 min-w-0 overflow-hidden">
            <div className="flex flex-col xs:flex-row xs:justify-between xs:items-start mb-1 gap-1 xs:gap-2">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm xs:text-base truncate flex-1 min-w-0">
                {sandbox.sandbox_name || sandbox.name || 'Unnamed Sandbox'}
              </h3>
              <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 font-medium whitespace-nowrap">
                {sandbox.timestamp ? formatDistanceToNow(sandbox.timestamp, { addSuffix: true }) : 'Unknown time'}
              </span>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <p className="text-xs xs:text-sm text-gray-600 dark:text-gray-400 leading-relaxed line-clamp-2 sm:line-clamp-1 cursor-help break-words">
                    {sandbox.lastMessage || sandbox.repo_url || 'No recent activity'}
                  </p>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="max-w-xs break-words">
                  <p>{sandbox.lastMessage || sandbox.repo_url || 'No recent activity'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {sandbox.unreadCount && sandbox.unreadCount > 0 && (
            <Badge variant="default" className="ml-1 xs:ml-2 bg-blue-500 font-medium min-h-5 min-w-5 xs:min-h-6 xs:min-w-6 smooth-transition text-xs flex-shrink-0">
              {sandbox.unreadCount}
            </Badge>
          )}
        </div>
      </Card>
    </div>
  );
}