/**
 * Speech Recognition Test Component
 * Used for testing and verifying speech recognition functionality
 */

import React, { useState, useEffect } from 'react';
import { VoiceInput } from './VoiceInput';
import { useSpeechPermission } from '../../hooks/useSpeechPermission';
import { verifySpeechRecognitionSetup, type VerificationResult } from '../../utils/speech-verification';

export const SpeechTestComponent: React.FC = () => {
  const [transcript, setTranscript] = useState('');
  const [finalTranscript, setFinalTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);

  const { status, isGranted, isAvailable } = useSpeechPermission();

  // Run verification on mount
  useEffect(() => {
    runVerification();
  }, []);

  const runVerification = async () => {
    setIsVerifying(true);
    try {
      const result = await verifySpeechRecognitionSetup();
      setVerificationResult(result);
    } catch (error) {
      console.error('Verification failed:', error);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleTranscript = (text: string, isFinal: boolean) => {
    setTranscript(text);
    if (isFinal) {
      setFinalTranscript(text);
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const clearResults = () => {
    setTranscript('');
    setFinalTranscript('');
    setError(null);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Speech Recognition Test
        </h1>
        <p className="text-gray-600">
          Test speech recognition functionality and verify setup
        </p>
      </div>

      {/* Verification Status */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">Setup Verification</h2>
          <button
            onClick={runVerification}
            disabled={isVerifying}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isVerifying ? 'Verifying...' : 'Re-verify'}
          </button>
        </div>

        {verificationResult && (
          <div className="space-y-2 text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Platform:</span> {verificationResult.platform}
              </div>
              <div>
                <span className="font-medium">Plugin:</span> {verificationResult.pluginInstalled ? '✅' : '❌'}
              </div>
              <div>
                <span className="font-medium">Available:</span> {verificationResult.speechRecognitionAvailable ? '✅' : '❌'}
              </div>
              <div>
                <span className="font-medium">Permission:</span> {verificationResult.permissionStatus}
              </div>
            </div>

            {verificationResult.errors.length > 0 && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                <p className="font-medium text-red-800 mb-1">Errors:</p>
                <ul className="text-red-700 space-y-1">
                  {verificationResult.errors.map((error, index) => (
                    <li key={index} className="text-xs">• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {verificationResult.warnings.length > 0 && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="font-medium text-yellow-800 mb-1">Warnings:</p>
                <ul className="text-yellow-700 space-y-1">
                  {verificationResult.warnings.map((warning, index) => (
                    <li key={index} className="text-xs">• {warning}</li>
                  ))}
                </ul>
              </div>
            )}

            {verificationResult.recommendations.length > 0 && (
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="font-medium text-blue-800 mb-1">Recommendations:</p>
                <ul className="text-blue-700 space-y-1">
                  {verificationResult.recommendations.map((rec, index) => (
                    <li key={index} className="text-xs">• {rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Voice Input Test */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Voice Input Test</h2>
        
        <VoiceInput
          onTranscript={handleTranscript}
          onError={handleError}
          placeholder="Say something to test voice recognition..."
          className="mb-4"
        />

        {/* Results Display */}
        {(transcript || finalTranscript || error) && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Results</h3>
              <button
                onClick={clearResults}
                className="px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
              >
                Clear
              </button>
            </div>

            {transcript && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm font-medium text-blue-800 mb-1">Live Transcript:</p>
                <p className="text-blue-700">{transcript}</p>
              </div>
            )}

            {finalTranscript && finalTranscript !== transcript && (
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <p className="text-sm font-medium text-green-800 mb-1">Final Result:</p>
                <p className="text-green-700">{finalTranscript}</p>
              </div>
            )}

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-sm font-medium text-red-800 mb-1">Error:</p>
                <p className="text-red-700">{error}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Permission Status */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Permission Status</h2>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Available:</span> {isAvailable ? '✅ Yes' : '❌ No'}
          </div>
          <div>
            <span className="font-medium">Granted:</span> {isGranted ? '✅ Yes' : '❌ No'}
          </div>
        </div>

        {status && (
          <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded">
            <p className="text-sm font-medium text-gray-800 mb-1">Detailed Status:</p>
            <div className="text-xs text-gray-600 space-y-1">
              <div>Granted: {status.granted ? '✅' : '❌'}</div>
              <div>Denied: {status.denied ? '⚠️' : '✅'}</div>
              <div>Prompt: {status.prompt ? '⚠️' : '✅'}</div>
              <div>Restricted: {status.restricted ? '⚠️' : '✅'}</div>
            </div>
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Usage Instructions</h2>
        <ol className="text-sm text-gray-700 space-y-2 list-decimal list-inside">
          <li>Grant microphone permissions when prompted</li>
          <li>Click the microphone icon to start voice recognition</li>
          <li>Speak clearly and wait for the transcript to appear</li>
          <li>Click stop or wait for automatic timeout</li>
          <li>Check the results in the sections above</li>
        </ol>
      </div>
    </div>
  );
};