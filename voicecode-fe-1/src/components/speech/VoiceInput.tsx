/**
 * Voice Input Component
 * Provides a voice input interface with visual feedback
 */

import React, { useState, useEffect } from 'react';
import { useSpeechRecognition } from '../../hooks/useSpeechRecognition';
import { SpeechPermissionPrompt } from './SpeechPermissionPrompt';

interface VoiceInputProps {
  onTranscript?: (transcript: string, isFinal: boolean) => void;
  onError?: (error: string) => void;
  placeholder?: string;
  className?: string;
  autoStart?: boolean;
}

export const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  onError,
  placeholder = "Tap to speak...",
  className = '',
  autoStart = false
}) => {
  const {
    isListening,
    transcript,
    finalTranscript,
    error,
    isSupported,
    permissionGranted,
    start,
    stop,
    reset
  } = useSpeechRecognition();

  const [hasStarted, setHasStarted] = useState(false);

  // Handle transcript changes
  useEffect(() => {
    if (transcript && onTranscript) {
      onTranscript(transcript, false);
    }
  }, [transcript, onTranscript]);

  // Handle final transcript
  useEffect(() => {
    if (finalTranscript && onTranscript) {
      onTranscript(finalTranscript, true);
    }
  }, [finalTranscript, onTranscript]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  // Auto-start if requested and permissions are available
  useEffect(() => {
    if (autoStart && permissionGranted && isSupported && !hasStarted) {
      handleStart();
    }
  }, [autoStart, permissionGranted, isSupported, hasStarted]);

  const handleStart = async () => {
    const success = await start();
    if (success) {
      setHasStarted(true);
    }
  };

  const handleStop = async () => {
    await stop();
  };

  const handleToggle = async () => {
    if (isListening) {
      await handleStop();
    } else {
      await handleStart();
    }
  };

  const handleReset = () => {
    reset();
    setHasStarted(false);
  };

  // Show permission prompt if not granted
  if (!permissionGranted) {
    return (
      <SpeechPermissionPrompt
        onPermissionGranted={() => {
          if (autoStart) {
            setTimeout(handleStart, 100);
          }
        }}
        className={className}
      />
    );
  }

  // Show unsupported message
  if (!isSupported) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="text-center text-gray-600">
          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
          <p className="text-sm">Voice input not supported on this device</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`voice-input ${className}`}>
      {/* Main input area */}
      <div className="relative">
        <div 
          className={`
            w-full p-4 border-2 rounded-lg transition-all duration-200 cursor-pointer
            ${isListening 
              ? 'border-red-400 bg-red-50' 
              : 'border-gray-300 bg-white hover:border-gray-400'
            }
            ${error ? 'border-red-500 bg-red-50' : ''}
          `}
          onClick={handleToggle}
        >
          <div className="flex items-center space-x-3">
            {/* Microphone icon */}
            <div className={`
              flex-shrink-0 w-6 h-6 transition-colors duration-200
              ${isListening ? 'text-red-500' : 'text-gray-400'}
            `}>
              {isListening ? (
                <svg className="w-6 h-6 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              )}
            </div>

            {/* Transcript display */}
            <div className="flex-1 min-h-[24px]">
              {transcript ? (
                <p className={`text-sm ${isListening ? 'text-red-700' : 'text-gray-900'}`}>
                  {transcript}
                </p>
              ) : (
                <p className="text-sm text-gray-500">
                  {isListening ? 'Listening...' : placeholder}
                </p>
              )}
            </div>

            {/* Status indicator */}
            {isListening && (
              <div className="flex-shrink-0">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        {(transcript || isListening) && (
          <div className="mt-2 flex justify-end space-x-2">
            {isListening && (
              <button
                onClick={handleStop}
                className="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200 transition-colors"
              >
                Stop
              </button>
            )}
            
            {transcript && !isListening && (
              <button
                onClick={handleReset}
                className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
              >
                Clear
              </button>
            )}
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          <div className="flex items-start">
            <svg className="w-4 h-4 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}

      {/* Usage hint */}
      {!isListening && !transcript && !error && (
        <div className="mt-2 text-xs text-gray-500 text-center">
          Click the microphone to start voice input
        </div>
      )}
    </div>
  );
};