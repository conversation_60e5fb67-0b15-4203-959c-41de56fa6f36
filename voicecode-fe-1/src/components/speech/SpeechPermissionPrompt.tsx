/**
 * Speech Permission Prompt Component
 * Handles permission requests and guidance for speech recognition
 */

import React, { useState } from 'react';
import { useSpeechPermission } from '../../hooks/useSpeechPermission';
import { Capacitor } from '@capacitor/core';

interface SpeechPermissionPromptProps {
  onPermissionGranted?: () => void;
  onPermissionDenied?: () => void;
  onClose?: () => void;
  className?: string;
}

export const SpeechPermissionPrompt: React.FC<SpeechPermissionPromptProps> = ({
  onPermissionGranted,
  onPermissionDenied,
  onClose,
  className = ''
}) => {
  const {
    status,
    isChecking,
    isGranted,
    isAvailable,
    requestPermissions,
    guidanceMessage
  } = useSpeechPermission();

  const [isRequesting, setIsRequesting] = useState(false);

  // Don't show prompt if permissions are already granted
  if (isGranted) {
    return null;
  }

  // Don't show prompt if speech recognition is not available
  if (!isAvailable) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Speech Recognition Not Available
            </h3>
            <p className="mt-1 text-sm text-yellow-700">
              Voice commands are not supported on this device or browser.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const handleRequestPermission = async () => {
    setIsRequesting(true);
    try {
      const granted = await requestPermissions();
      
      if (granted) {
        onPermissionGranted?.();
      } else {
        onPermissionDenied?.();
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      onPermissionDenied?.();
    } finally {
      setIsRequesting(false);
    }
  };

  const openSettings = () => {
    const platform = Capacitor.getPlatform();
    
    if (platform === 'ios') {
      // On iOS, we can try to open app settings
      window.open('app-settings:', '_system');
    } else if (platform === 'android') {
      // On Android, we could implement a native plugin to open settings
      // For now, just show guidance
      alert(guidanceMessage);
    } else {
      // Web - show browser-specific guidance
      alert('Please allow microphone access when your browser prompts you.');
    }
  };

  const isLoading = isChecking || isRequesting;

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 715 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          {onClose && (
            <div className="float-right">
              <button
                onClick={onClose}
                className="text-blue-400 hover:text-blue-600 focus:outline-none"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          )}
          <h3 className="text-sm font-medium text-blue-800">
            Microphone Access Required
          </h3>
          <p className="mt-1 text-sm text-blue-700">
            VoiceCode needs access to your microphone to recognize voice commands and convert them to code operations.
          </p>
          
          <div className="mt-4 flex flex-col sm:flex-row gap-3">
            {status?.denied ? (
              <>
                <div className="text-sm text-blue-700 mb-2">
                  <strong>Permission Denied:</strong> Please enable microphone access in your device settings.
                </div>
                <button
                  onClick={openSettings}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  disabled={isLoading}
                >
                  Open Settings
                </button>
              </>
            ) : (
              <button
                onClick={handleRequestPermission}
                disabled={isLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Requesting...
                  </>
                ) : (
                  'Grant Permission'
                )}
              </button>
            )}
          </div>
          
          <div className="mt-3 text-xs text-blue-600">
            <strong>Privacy Note:</strong> Audio is processed locally on your device and never stored or transmitted.
          </div>
        </div>
      </div>
    </div>
  );
};