import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { format } from 'date-fns';

interface ErrorMessageProps {
  content: string;
  timestamp: Date;
}

export function ErrorMessage({ content, timestamp }: ErrorMessageProps) {
  return (
    <div className="ml-2 xs:ml-4 mr-2 xs:mr-4 mb-3 max-w-[90%] xs:max-w-[85%]">
      <Alert variant="destructive" className="smooth-transition">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="space-y-1">
          <p className="text-sm font-mono">
            {content}
          </p>
          <time className="text-xs block opacity-80">
            {format(timestamp, 'HH:mm')}
          </time>
        </AlertDescription>
      </Alert>
    </div>
  );
}