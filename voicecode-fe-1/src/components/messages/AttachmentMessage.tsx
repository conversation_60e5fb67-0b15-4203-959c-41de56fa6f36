import { Download, Eye, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface AttachmentMessageProps {
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl?: string;
  timestamp: Date;
  isOwnMessage?: boolean;
}

export function AttachmentMessage({ 
  fileName, 
  fileSize, 
  fileType, 
  fileUrl,
  timestamp,
  isOwnMessage = false 
}: AttachmentMessageProps) {
  const isImage = fileType.startsWith('image/');

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <div className={cn("flex flex-col mb-1", isOwnMessage && "items-end")}>
      <div className={cn(
        "relative max-w-[70%] rounded-2xl shadow-sm",
        isOwnMessage 
          ? "ml-auto mr-4 bg-blue-500 text-white rounded-br-sm" 
          : "ml-4 mr-auto bg-gray-200 dark:bg-gray-800 rounded-bl-sm"
      )}>
        {isImage && fileUrl ? (
          <div className="p-1">
            <img 
              src={fileUrl} 
              alt={fileName}
              className="rounded-lg max-w-full max-h-[300px] object-contain"
            />
            <div className="px-3 py-2">
              <p className="text-sm font-medium truncate">{fileName}</p>
              <p className="text-xs opacity-70">{formatFileSize(fileSize)}</p>
            </div>
          </div>
        ) : (
          <div className="p-4 flex items-center gap-3">
            <div className="w-10 h-10 flex-shrink-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded">
              <FileText className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{fileName}</p>
              <p className="text-xs opacity-70">{formatFileSize(fileSize)}</p>
            </div>
            <div className="flex gap-1">
              {fileUrl && (
                <>
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="h-8 w-8"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="h-8 w-8"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        <div className={cn(
          "flex items-center gap-1 px-3 pb-2",
          isOwnMessage ? "justify-end" : "justify-start"
        )}>
          <time className="text-xs opacity-70">
            {format(timestamp, 'HH:mm')}
          </time>
        </div>
      </div>
    </div>
  );
}