import { Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface VoiceMessageProps {
  duration: number;
  waveform: number[];
  transcription?: string;
  isOwnMessage?: boolean;
  timestamp: Date;
}

export function VoiceMessage({ 
  duration, 
  waveform, 
  transcription, 
  isOwnMessage = false,
  timestamp 
}: VoiceMessageProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const progressInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isPlaying) {
      progressInterval.current = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 0.1;
        });
      }, 100);
    } else {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }
    }

    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }
    };
  }, [isPlaying, duration]);

  const progress = (currentTime / duration) * 100;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn("flex flex-col mb-3", isOwnMessage && "items-end")}>
      <div className={cn(
        "relative max-w-[85%] xs:max-w-[80%] sm:max-w-[70%] rounded-2xl px-3 xs:px-4 py-3 shadow-sm smooth-transition",
        isOwnMessage 
          ? "ml-auto mr-2 xs:mr-4 message-user-bg text-white rounded-br-sm" 
          : "ml-2 xs:ml-4 mr-auto message-system-bg dark:bg-gray-800 rounded-bl-sm"
      )}>
        <div className="flex items-center gap-3">
          {/* Play/Pause Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className={cn(
              "h-10 w-10 rounded-full p-0",
              isOwnMessage 
                ? "bg-white/20 hover:bg-white/30 text-white" 
                : "bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
            )}
          >
            {isPlaying ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4 ml-0.5" />
            )}
          </Button>

          {/* Waveform */}
          <div className="flex-1 flex items-center gap-0.5 h-8 relative">
            {waveform.map((amplitude, i) => {
              const isActive = (i / waveform.length) * 100 <= progress;
              return (
                <div
                  key={i}
                  className={cn(
                    "flex-1 rounded-full transition-all duration-150",
                    isOwnMessage
                      ? isActive ? "bg-white" : "bg-white/40"
                      : isActive ? "bg-gray-600 dark:bg-gray-400" : "bg-gray-300 dark:bg-gray-600"
                  )}
                  style={{ 
                    height: `${Math.max(4, amplitude * 32)}px`,
                    minWidth: '2px',
                    maxWidth: '3px'
                  }}
                />
              );
            })}
          </div>

          {/* Duration */}
          <span className={cn(
            "text-xs font-medium min-w-[40px]",
            isOwnMessage ? "text-white/80" : "text-gray-600 dark:text-gray-400"
          )}>
            {formatTime(isPlaying ? currentTime : duration)}
          </span>
        </div>

        {/* Transcription */}
        {transcription && (
          <p className={cn(
            "mt-2 text-sm",
            isOwnMessage ? "text-white/90" : "text-gray-700 dark:text-gray-300"
          )}>
            "{transcription}"
          </p>
        )}

        {/* Timestamp */}
        <div className={cn(
          "flex items-center gap-1 mt-1",
          isOwnMessage ? "justify-end" : "justify-start"
        )}>
          <time className="text-xs opacity-70">
            {format(timestamp, 'HH:mm')}
          </time>
        </div>
      </div>
    </div>
  );
}