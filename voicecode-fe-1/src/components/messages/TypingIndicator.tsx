import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  userName?: string;
  className?: string;
}

export function TypingIndicator({ userName = 'AI Assistant', className }: TypingIndicatorProps) {
  const [dots, setDots] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev + 1) % 4);
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={cn("flex justify-start", className)}>
      <div className="rounded-lg px-3 py-2 max-w-[85%] sm:max-w-[80%] bg-muted inline-flex items-center space-x-2 whitespace-nowrap">
        <span className="text-sm">
          {userName} is typing
        </span>
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "w-1.5 h-1.5 bg-gray-500 dark:bg-gray-400 rounded-full transition-all duration-300",
                dots > i ? "opacity-100 scale-100" : "opacity-30 scale-75"
              )}
              style={{
                animationDelay: `${i * 150}ms`,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}