import { cn } from '@/lib/utils';
import type { FakeMessage } from '@/lib/fake-data';
import { Check, CheckCheck } from 'lucide-react';
import { format } from 'date-fns';
import { Card } from '@/components/ui/card';

interface MessageBubbleProps {
  message: FakeMessage;
  isOwnMessage?: boolean;
}

export function MessageBubble({ message, isOwnMessage = false }: MessageBubbleProps) {
  return (
    <div className={cn("flex mb-4", isOwnMessage ? "justify-end" : "justify-start")}>
      <Card className={cn(
        "max-w-xs p-3",
        isOwnMessage ? "bg-primary text-primary-foreground" : "bg-card"
      )}>
        <p className="text-sm">
          {message.content}
        </p>
        
        <div className={cn(
          "flex items-center gap-1 mt-2",
          isOwnMessage ? "justify-end" : "justify-start"
        )}>
          <time className="text-xs opacity-70">
            {format(message.timestamp, 'HH:mm')}
          </time>
          
          {isOwnMessage && (
            <span className="text-xs opacity-70">
              {message.status === 'sent' && <Check className="w-3 h-3" />}
              {message.status === 'delivered' && <CheckCheck className="w-3 h-3" />}
              {message.status === 'read' && <CheckCheck className="w-3 h-3" />}
            </span>
          )}
        </div>
      </Card>
    </div>
  );
}