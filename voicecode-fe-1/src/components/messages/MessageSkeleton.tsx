import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface MessageSkeletonProps {
  align?: 'left' | 'right';
}

export function MessageSkeleton({ align = 'left' }: MessageSkeletonProps) {
  return (
    <div className={cn("flex", align === 'right' && "justify-end")}>
      <div className={cn("space-y-2", align === 'right' ? "mr-4" : "ml-4")}>
        <Skeleton 
          className={cn(
            "h-16 rounded-2xl",
            align === 'right' 
              ? "w-48 bg-blue-100 dark:bg-blue-900/20" 
              : "w-56 bg-gray-200 dark:bg-gray-800"
          )}
        />
        <Skeleton 
          className={cn(
            "h-3 w-12",
            align === 'right' ? "ml-auto" : "mr-auto"
          )} 
        />
      </div>
    </div>
  );
}