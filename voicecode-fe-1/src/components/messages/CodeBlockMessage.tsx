import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Terminal, Copy, Check } from 'lucide-react'
import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'

interface CodeBlockMessageProps {
  content: string
  language?: string
  timestamp: Date
  metadata?: {
    command?: string
    exitCode?: number
    executionTime?: number
  }
}

export function CodeBlockMessage({ 
  content, 
  language = 'text', 
  timestamp,
  metadata 
}: CodeBlockMessageProps) {
  const [copied, setCopied] = useState(false)

  const handleCopy = () => {
    navigator.clipboard.writeText(content)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const isError = metadata?.exitCode !== undefined && metadata.exitCode !== 0

  return (
    <Card className={cn(
      "p-4",
      isError && "border-red-200 dark:border-red-800"
    )}>
      {metadata?.command && (
        <div className="flex items-center gap-2 mb-3">
          <Terminal className="h-4 w-4 text-gray-400" />
          <code className="text-sm font-mono text-gray-600 dark:text-gray-400">
            $ {metadata.command}
          </code>
          {metadata.exitCode !== undefined && (
            <Badge 
              variant={isError ? "destructive" : "default"}
              className="text-xs"
            >
              Exit: {metadata.exitCode}
            </Badge>
          )}
        </div>
      )}
      
      <div className="relative">
        <pre className={cn(
          "overflow-x-auto p-3 rounded-md text-sm",
          isError 
            ? "bg-red-50 dark:bg-red-950 text-red-900 dark:text-red-100"
            : "bg-gray-100 dark:bg-gray-800"
        )}>
          <code className={`language-${language}`}>
            {content || '(No output)'}
          </code>
        </pre>
        
        <button
          onClick={handleCopy}
          className="absolute top-2 right-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4 text-gray-400" />
          )}
        </button>
      </div>
      
      <div className="flex items-center gap-3 mt-3 text-xs text-gray-500">
        <span>{formatDistanceToNow(timestamp, { addSuffix: true })}</span>
        {metadata?.executionTime && (
          <span>Executed in {metadata.executionTime.toFixed(2)}s</span>
        )}
      </div>
    </Card>
  )
}