import { Info } from 'lucide-react';
import { format } from 'date-fns';

interface SystemMessageProps {
  content: string;
  timestamp: Date;
  icon?: React.ReactNode;
}

export function SystemMessage({ content, timestamp, icon }: SystemMessageProps) {
  return (
    <div className="ml-2 xs:ml-4 mr-2 xs:mr-4 mb-3 max-w-[90%] xs:max-w-[85%]">
      <div className="rounded-lg bg-gray-100 dark:bg-gray-800 p-3 smooth-transition">
        <div className="flex items-start gap-2">
          {icon || <Info className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0 mt-0.5" />}
          <div className="flex-1">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {content}
            </p>
            <time className="text-xs text-gray-500 dark:text-gray-400 mt-1 block">
              {format(timestamp, 'HH:mm')}
            </time>
          </div>
        </div>
      </div>
    </div>
  );
}