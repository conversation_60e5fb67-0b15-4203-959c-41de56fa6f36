import { format, isToday, isYesterday } from 'date-fns';

interface DateSeparatorProps {
  date: Date;
}

export function DateSeparator({ date }: DateSeparatorProps) {
  const formatDate = () => {
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'EEEE, MMMM d');
  };

  return (
    <div className="flex items-center my-4 px-4">
      <div className="flex-1 h-px bg-gray-300 dark:bg-gray-700" />
      <span className="px-3 text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 rounded-full">
        {formatDate()}
      </span>
      <div className="flex-1 h-px bg-gray-300 dark:bg-gray-700" />
    </div>
  );
}