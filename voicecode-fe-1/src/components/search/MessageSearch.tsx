import { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import type { FakeMessage } from '@/lib/fake-data';
import Highlighter from 'react-highlight-words';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface MessageSearchProps {
  messages: FakeMessage[];
  isOpen: boolean;
  onClose: () => void;
  onSelectMessage: (messageId: string) => void;
}

export function MessageSearch({ messages, isOpen, onClose, onSelectMessage }: MessageSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSelect = (messageId: string) => {
    onSelectMessage(messageId);
    onClose();
    setSearchQuery('');
  };

  const filteredMessages = messages.filter(msg => 
    searchQuery.trim() ? msg.content.toLowerCase().includes(searchQuery.toLowerCase()) : false
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg h-[80vh] p-0">
        <Command className="h-full">
          <CommandInput 
            placeholder="Search in conversation..." 
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No messages found.</CommandEmpty>
            <CommandGroup>
              {filteredMessages.map((message) => (
                <CommandItem
                  key={message.id}
                  onSelect={() => handleSelect(message.id)}
                  className="flex flex-col items-start p-3"
                >
                  <div className="flex items-start justify-between w-full mb-1">
                    <span className={cn(
                      "text-xs font-medium",
                      message.type === 'user' ? "text-blue-600" : "text-gray-600"
                    )}>
                      {message.type === 'user' ? 'You' : 'Assistant'}
                    </span>
                    <time className="text-xs text-gray-500">
                      {format(message.timestamp, 'MMM d, HH:mm')}
                    </time>
                  </div>
                  <p className="text-sm line-clamp-2 w-full text-left">
                    <Highlighter
                      highlightClassName="bg-yellow-200 dark:bg-yellow-900"
                      searchWords={[searchQuery]}
                      autoEscape={true}
                      textToHighlight={message.content}
                    />
                  </p>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  );
}