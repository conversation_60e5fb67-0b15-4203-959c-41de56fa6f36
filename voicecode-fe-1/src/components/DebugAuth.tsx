import { useAuthStore } from '@/store/auth.store'

export function DebugAuth() {
  const { user, token, isAuthenticated, isLoading, error } = useAuthStore()
  
  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      right: 0, 
      background: 'red', 
      color: 'white', 
      padding: '10px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h3>🔐 Auth Debug</h3>
      <div>isAuthenticated: {isAuthenticated ? 'TRUE' : 'FALSE'}</div>
      <div>isLoading: {isLoading ? 'TRUE' : 'FALSE'}</div>
      <div>hasToken: {token ? 'TRUE' : 'FALSE'}</div>
      <div>hasUser: {user ? 'TRUE' : 'FALSE'}</div>
      <div>error: {error || 'none'}</div>
      <div>localStorage: {localStorage.getItem('voicecode-auth-store') ? 'exists' : 'empty'}</div>
      <button 
        onClick={() => useAuthStore.getState().clearStorage()}
        style={{ marginTop: '5px', fontSize: '10px' }}
      >
        Clear Auth
      </button>
    </div>
  )
}