import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Terminal, Clock, ChevronUp, ChevronDown } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { useChatMessages } from '@/store/chat.store'

interface CommandHistoryProps {
  sandboxId: string
  onSelectCommand: (command: string) => void
}

export function CommandHistory({ sandboxId, onSelectCommand }: CommandHistoryProps) {
  const [isOpen, setIsOpen] = useState(false)
  const messages = useChatMessages(sandboxId)
  
  // Extract commands from messages
  const commands = messages
    .filter(msg => msg.metadata?.originalCommand)
    .map(msg => ({
      command: msg.metadata!.originalCommand as string,
      timestamp: msg.createdAt,
      exitCode: msg.metadata?.exitCode as number | undefined
    }))
    .reverse() // Most recent first
    .slice(0, 10) // Last 10 commands

  if (commands.length === 0) return null

  return (
    <div className="absolute bottom-full left-0 right-0 mb-2 px-4">
      <Card className="overflow-hidden">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full p-3 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Clock className="h-4 w-4" />
            Command History ({commands.length})
          </div>
          {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
        </button>
        
        {isOpen && (
          <div className="border-t max-h-64 overflow-y-auto">
            {commands.map((cmd, index) => (
              <button
                key={index}
                onClick={() => {
                  onSelectCommand(cmd.command)
                  setIsOpen(false)
                }}
                className="w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors border-b last:border-0"
              >
                <div className="flex items-center gap-2">
                  <Terminal className="h-4 w-4 text-gray-400 flex-shrink-0" />
                  <code className="text-sm font-mono truncate flex-1">
                    {cmd.command}
                  </code>
                  {cmd.exitCode !== undefined && cmd.exitCode !== 0 && (
                    <span className="text-xs text-red-500">
                      Exit {cmd.exitCode}
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1 ml-6">
                  {formatDistanceToNow(cmd.timestamp, { addSuffix: true })}
                </p>
              </button>
            ))}
          </div>
        )}
      </Card>
    </div>
  )
}