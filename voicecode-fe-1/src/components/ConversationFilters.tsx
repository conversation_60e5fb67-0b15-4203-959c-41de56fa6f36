import { useState } from 'react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Filter } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

export type SortOption = 'recent' | 'alphabetical' | 'unread';
export type FilterOptions = {
  showActive: boolean;
  showIdle: boolean;
  showStopped: boolean;
  onlyUnread: boolean;
  onlyPinned: boolean;
};

interface ConversationFiltersProps {
  sortBy: SortOption;
  filters: FilterOptions;
  onSortChange: (sort: SortOption) => void;
  onFiltersChange: (filters: FilterOptions) => void;
}

export function ConversationFilters({
  sortBy,
  filters,
  onSortChange,
  onFiltersChange,
}: ConversationFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon">
          <Filter className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-80">
        <SheetHeader>
          <SheetTitle>Filter & Sort</SheetTitle>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Sort Options */}
          <div>
            <h4 className="text-sm font-medium mb-3">Sort by</h4>
            <RadioGroup value={sortBy} onValueChange={(value) => onSortChange(value as SortOption)}>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="recent" id="recent" />
                <Label htmlFor="recent">Most Recent</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="alphabetical" id="alphabetical" />
                <Label htmlFor="alphabetical">Alphabetical</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="unread" id="unread" />
                <Label htmlFor="unread">Unread First</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Filter Options */}
          <div>
            <h4 className="text-sm font-medium mb-3">Filter</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="unread-only">Only unread</Label>
                <Switch
                  id="unread-only"
                  checked={filters.onlyUnread}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, onlyUnread: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="pinned-only">Only pinned</Label>
                <Switch
                  id="pinned-only"
                  checked={filters.onlyPinned}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, onlyPinned: checked })
                  }
                />
              </div>
            </div>
          </div>

          {/* Status Filters */}
          <div>
            <h4 className="text-sm font-medium mb-3">Sandbox Status</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-active">Show active</Label>
                <Switch
                  id="show-active"
                  checked={filters.showActive}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, showActive: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="show-idle">Show idle</Label>
                <Switch
                  id="show-idle"
                  checked={filters.showIdle}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, showIdle: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="show-stopped">Show stopped</Label>
                <Switch
                  id="show-stopped"
                  checked={filters.showStopped}
                  onCheckedChange={(checked) =>
                    onFiltersChange({ ...filters, showStopped: checked })
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}