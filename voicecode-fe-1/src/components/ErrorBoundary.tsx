import React from 'react'
import { Alert<PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 ErrorBoundary caught error:', {
      message: error?.message || 'No message',
      name: error?.name || 'No name',
      stack: error?.stack || 'No stack',
      errorInfo: errorInfo,
      componentStack: errorInfo?.componentStack || 'No component stack'
    })
    this.setState({ errorInfo })
    
    // Log to error reporting service
    if (import.meta.env.PROD) {
      // Send to error tracking service
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      const isNetworkError = this.state.error?.message?.includes('network')
      const isSandboxError = this.state.error?.message?.includes('sandbox')
      
      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
          <Card className="max-w-md w-full p-8 text-center">
            <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            
            <h1 className="text-2xl font-semibold mb-2">
              {isNetworkError ? 'Connection Error' : 'Something went wrong'}
            </h1>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {isNetworkError
                ? 'Unable to connect to the server. Please check your internet connection.'
                : isSandboxError
                ? 'There was a problem with the sandbox. Please try again.'
                : 'An unexpected error occurred. Please try refreshing the page.'}
            </p>
            
            {import.meta.env.DEV && this.state.error && (
              <details className="text-left mb-6">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  Error Details
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
            
            <div className="flex gap-3 justify-center">
              <Button
                onClick={this.handleReset}
                variant="outline"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              
              <Button
                onClick={() => window.location.href = '/'}
              >
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </div>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}