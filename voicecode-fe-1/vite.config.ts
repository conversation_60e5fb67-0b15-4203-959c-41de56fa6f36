import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: true, // Listen on all network interfaces
    port: 9080,
  },
  build: {
    // Enable source maps for mobile debugging
    sourcemap: true,
    // Optimize for mobile performance
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': [
            '@radix-ui/react-avatar', 
            '@radix-ui/react-scroll-area',
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu'
          ],
          'capacitor': ['@capacitor/core'], // Separate Capacitor chunk
          'utils': ['clsx', 'tailwind-merge', 'class-variance-authority']
        }
      }
    },
    // Optimize chunk size for mobile
    chunkSizeWarningLimit: 1000,
    // Ensure compatibility with mobile webviews
    target: ['es2020', 'edge88', 'firefox78', 'chrome87', 'safari14']
  },
  // Optimize dependencies for mobile
  optimizeDeps: {
    include: ['react', 'react-dom', '@capacitor/core']
  }
})
