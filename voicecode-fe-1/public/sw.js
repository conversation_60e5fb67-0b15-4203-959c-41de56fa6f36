const CACHE_NAME = 'voicecode-v1'
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
      .then(() => self.skipWaiting())
  )
})

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => self.clients.claim())
  )
})

// Fetch event with network-first strategy for API calls
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Network-first for API calls
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Only cache GET requests for API calls
          if (request.method === 'GET' && response.ok) {
            const responseToCache = response.clone()
            caches.open(CACHE_NAME).then((cache) => {
              cache.put(request, responseToCache)
            })
          }
          return response
        })
        .catch((error) => {
          // Fallback to cache only for GET requests
          if (request.method === 'GET') {
            return caches.match(request)
          }
          // For non-GET requests, throw the error
          throw error
        })
    )
    return
  }

  // Cache-first for assets
  event.respondWith(
    caches.match(request)
      .then((response) => response || fetch(request))
      .catch(() => {
        // Offline fallback
        if (request.destination === 'document') {
          return caches.match('/index.html')
        }
      })
  )
})

// Background sync for offline messages
self.addEventListener('sync', (event) => {
  if (event.tag === 'send-messages') {
    event.waitUntil(sendQueuedMessages())
  }
})

async function sendQueuedMessages() {
  // Get queued messages from IndexedDB
  const db = await openDB()
  const messages = await getQueuedMessages(db)
  
  for (const message of messages) {
    try {
      await fetch('/api/sandbox/' + message.sandboxId + '/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + message.token
        },
        body: JSON.stringify({
          message: message.content,
          message_type: 'user'
        })
      })
      
      // Remove from queue on success
      await removeFromQueue(db, message.id)
    } catch (error) {
      console.error('Failed to send queued message:', error)
    }
  }
}

// Push notifications
self.addEventListener('push', (event) => {
  const options = {
    body: event.data?.text() || 'New notification from VoiceCode',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    }
  }
  
  event.waitUntil(
    self.registration.showNotification('VoiceCode', options)
  )
})

// Notification click
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  event.waitUntil(
    clients.openWindow('/')
  )
})

// Skip waiting message
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

// IndexedDB helpers for offline message queue
async function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('voicecode-offline', 1)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result
      if (!db.objectStoreNames.contains('messages')) {
        db.createObjectStore('messages', { keyPath: 'id' })
      }
    }
  })
}

async function getQueuedMessages(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['messages'], 'readonly')
    const store = transaction.objectStore('messages')
    const request = store.getAll()
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
  })
}

async function removeFromQueue(db, messageId) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['messages'], 'readwrite')
    const store = transaction.objectStore('messages')
    const request = store.delete(messageId)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve()
  })
}