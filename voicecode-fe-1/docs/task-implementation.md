# Task Action Buttons Implementation Plan

## Overview

This document outlines the comprehensive implementation plan for adding task action buttons (View Logs and Cancel Task) to the VoiceCode chat interface. The implementation will use only Shadcn UI components without custom styles or colors.

## Current Architecture

### Existing Components
- `ChatView` - Main chat interface
- `useVoiceCodeChat` - Chat management hook
- `TaskService` - Task operations service
- `FastAPIAdapter` - Backend communication adapter

### Current Task Flow
1. User sends command → `executeCommand()`
2. Task created → `TaskService.createTask()`
3. System message added with task metadata
4. Task runs in background (no UI feedback)

## New Implementation Plan

### 1. Component Architecture

```
src/components/
├── TaskActionButtons.tsx       # Main action buttons component
├── TaskLogsDialog.tsx         # Log viewing modal
├── TaskStatusBadge.tsx        # Status indicator badge
└── ui/                        # Existing Shadcn components
    ├── button.tsx
    ├── dialog.tsx
    ├── alert-dialog.tsx
    ├── badge.tsx
    └── scroll-area.tsx
```

### 2. Component Specifications

#### TaskActionButtons Component
```tsx
interface TaskActionButtonsProps {
  sandboxId: string
  taskId: string
  status: TaskStatus
  onStatusUpdate?: (status: TaskStatusResponse) => void
}
```

**Features:**
- View Logs button (Shadcn Button variant="outline")
- Cancel Task button (Shadcn Button variant="destructive")
- Status-aware button visibility
- Loading states with Shadcn Loader2 icon

**Shadcn Components Used:**
- `Button` (outline, destructive variants)
- `AlertDialog` (for cancel confirmation)
- Icons from `lucide-react` (Eye, X, Loader2)

#### TaskLogsDialog Component
```tsx
interface TaskLogsDialogProps {
  sandboxId: string
  taskId: string
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}
```

**Features:**
- Real-time log streaming via EventSource
- Scrollable log content with auto-scroll
- Task status display
- Cancel task option within modal

**Shadcn Components Used:**
- `Dialog` (DialogContent, DialogHeader, DialogTitle, DialogFooter)
- `ScrollArea` (for log content)
- `Badge` (for status display)
- `Button` (for actions)
- `Separator` (for visual separation)

#### TaskStatusBadge Component
```tsx
interface TaskStatusBadgeProps {
  status: TaskStatus
  taskId: string
  command?: string
  showDetails?: boolean
}
```

**Features:**
- Status-specific badge variants
- Optional command display
- Responsive design

**Shadcn Components Used:**
- `Badge` with variants:
  - `default` (blue) - Running
  - `success` (green) - Completed  
  - `destructive` (red) - Failed
  - `secondary` (gray) - Cancelled

### 3. Integration Points

#### Chat Message Enhancement
```tsx
// In ChatView component
{messages.map(message => (
  <div key={message.id} className="space-y-2">
    <MessageContent>{message.content}</MessageContent>
    
    {/* NEW: Task components for messages with task metadata */}
    {message.metadata?.taskId && (
      <div className="space-y-2">
        <TaskStatusBadge
          status={message.metadata.status}
          taskId={message.metadata.taskId}
          command={message.metadata.originalCommand}
          showDetails={true}
        />
        <TaskActionButtons
          sandboxId={sandboxId}
          taskId={message.metadata.taskId}
          status={message.metadata.status}
          onStatusUpdate={handleTaskStatusUpdate}
        />
      </div>
    )}
  </div>
))}
```

#### Hook Enhancement
```tsx
// Enhanced useVoiceCodeChat hook
const useVoiceCodeChat = (sandboxId: string) => {
  // ... existing code ...
  
  // NEW: Task status update handler
  const handleTaskStatusUpdate = useCallback((taskId: string, status: TaskStatusResponse) => {
    // Update message metadata with new status
    setMessages(prev => prev.map(msg => 
      msg.metadata?.taskId === taskId 
        ? { ...msg, metadata: { ...msg.metadata, status: status.status } }
        : msg
    ))
    
    // Add completion message if task finished
    if (status.status === 'completed' || status.status === 'failed') {
      const resultMessage = createTaskResultMessage(status)
      setMessages(prev => [...prev, resultMessage])
    }
  }, [setMessages])
  
  return {
    // ... existing returns ...
    handleTaskStatusUpdate
  }
}
```

### 4. User Experience Flow

#### View Logs Flow
1. User clicks "View Logs" button
2. `TaskLogsDialog` opens with task details
3. Real-time log streaming starts via `TaskService.createLogStream()`
4. Logs display in scrollable area with timestamps
5. Auto-scroll to latest logs
6. User can cancel task from within modal

#### Cancel Task Flow
1. User clicks "Cancel Task" button
2. `AlertDialog` opens with confirmation
3. User confirms cancellation
4. `TaskService.cancelTask()` called
5. Task status updates to "cancelled"
6. UI reflects new status immediately
7. Log streaming stops if active

### 5. ASCII UI Layout

```
┌─────────────────────────────────────────────────────────────┐
│ Chat Message with Task                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ "Task started: Tell me about the current project"      │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ TaskStatusBadge                                     │ │ │
│ │ │ [Running] ● Command: "Tell me about..."            │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ TaskActionButtons                                   │ │ │
│ │ │ [👁 View Logs] [❌ Cancel Task]                     │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ TaskLogsDialog (Modal)                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ "Task Logs - abc123"                    [❌ Close]      │ │
│ │ ─────────────────────────────────────────────────────── │ │
│ │ [Running] ● Command: "Tell me about..."                │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ ScrollArea (Live Logs)                              │ │ │
│ │ │ [2024-01-15 10:30:01] Starting analysis...         │ │ │
│ │ │ [2024-01-15 10:30:02] Reading project files...     │ │ │
│ │ │ [2024-01-15 10:30:03] ● Live streaming...           │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │                              [Close] [Cancel Task]      │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6. Implementation Steps

#### Phase 1: Core Components
1. **TaskStatusBadge** - Simple status display with Shadcn Badge
2. **TaskActionButtons** - Button layout with click handlers
3. **TaskLogsDialog** - Basic modal structure with Shadcn Dialog

#### Phase 2: Functionality
4. **Log Streaming** - Integrate TaskService.createLogStream()
5. **Task Cancellation** - Add AlertDialog confirmation
6. **Status Updates** - Real-time status polling

#### Phase 3: Integration
7. **Chat Integration** - Add components to message rendering
8. **Hook Enhancement** - Update useVoiceCodeChat for task management
9. **Error Handling** - Proper error states and user feedback

### 7. Technical Requirements

#### Dependencies
- All Shadcn UI components already installed
- `lucide-react` for icons (already available)
- Existing `TaskService` and `FastAPIAdapter`

#### API Endpoints Used
- `GET /api/sandbox/{sandboxId}/execute/{taskId}/status`
- `GET /api/sandbox/{sandboxId}/execute/{taskId}/logs` (EventSource)
- `POST /api/sandbox/{sandboxId}/execute/{taskId}/cancel`

#### State Management
- Task status updates via message metadata
- Log streaming state in TaskLogsDialog
- Modal open/close state management

### 8. Error Handling

#### Network Errors
- Graceful fallback when log streaming fails
- Retry logic for status updates
- User-friendly error messages

#### Task Errors
- Handle task cancellation failures
- Display task failure reasons
- Proper cleanup of event sources

### 9. Accessibility

#### Keyboard Navigation
- All buttons keyboard accessible
- Modal focus management
- Proper ARIA labels

#### Screen Readers
- Descriptive button labels
- Status announcements
- Log content accessibility

### 10. Testing Strategy

#### Unit Tests
- Component rendering with different task states
- Button click handlers
- Status badge variants

#### Integration Tests
- Task cancellation flow
- Log streaming functionality
- Chat message integration

#### E2E Tests
- Complete user workflow
- Error scenarios
- Real-time updates

## Conclusion

This implementation provides a comprehensive task management interface using only Shadcn UI components. The design maintains consistency with the existing UI while adding powerful task monitoring and control capabilities. The modular approach ensures easy maintenance and future enhancements.