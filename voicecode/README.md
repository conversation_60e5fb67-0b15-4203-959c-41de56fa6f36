# VoiceCode - Phase 1 Implementation

This is the Phase 1 implementation for VoiceCode, focusing on creating a custom Daytona image with Claude Code CLI and setting up GitHub Apps integration.

## Project Structure

```
voicecode/
├── docker/
│   └── Dockerfile              # Custom Daytona image with Claude Code CLI
├── services/
│   └── github-auth/            # GitHub Apps OAuth service
│       ├── index.js
│       ├── package.json
│       └── .env.example
├── scripts/
│   └── build-image.sh         # Script to build and push Daytona image
├── tests/
│   ├── test-image.js          # Test Claude Code CLI in sandbox
│   └── test-github.js         # Test GitHub API integration
└── README.md
```

## Phase 1 Objectives

### ✅ Custom Daytona Image (TV1)
- [x] Dockerfile with Claude Code CLI pre-installed
- [x] Build script for creating Daytona snapshots
- [ ] Test image in Daytona sandbox

### ✅ GitHub Apps Integration (TV2)
- [x] OAuth flow implementation
- [x] Repository listing API
- [x] Branch listing API
- [ ] Create actual GitHub App
- [ ] Test complete OAuth flow

## Quick Start

### 1. Build Custom Daytona Image

```bash
cd scripts
./build-image.sh
```

This will:
- Build a Docker image with Claude Code CLI pre-installed
- Create a Daytona snapshot named `voicecode-claude:1.0.0`
- Make it available for sandbox creation

### 2. Set Up GitHub Apps Service

```bash
cd services/github-auth
npm install
cp .env.example .env
# Edit .env with your GitHub App credentials
npm start
```

### 3. Create GitHub App

1. Go to [GitHub Apps settings](https://github.com/settings/apps)
2. Click "New GitHub App"
3. Configure with these settings:
   - **App name**: VoiceCode
   - **Homepage URL**: Your app URL
   - **Webhook URL**: `https://your-domain.com/webhooks` (optional for Phase 1)
   - **Repository permissions**:
     - Contents: Write
     - Metadata: Read
     - Pull requests: Write
     - Actions: Read (for CI/CD integration)
   - **Account permissions**:
     - Email addresses: Read
4. Generate a client secret
5. Note the Client ID and Client Secret for your `.env` file

### 4. Install Daytona CLI (if needed)

```bash
# Install Daytona CLI
brew install daytonaio/cli/daytona

# Configure with your API key (get from https://app.daytona.io/dashboard/)
daytona configure
```

### 5. Test the Implementation

Test the Daytona image (run inside a Daytona sandbox):
```bash
cd tests
node test-image.js
```

Test the GitHub service:
```bash
cd tests
node test-github.js
```

## Environment Variables

For the GitHub Auth service, configure these variables in `services/github-auth/.env`:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# GitHub App Configuration
GITHUB_CLIENT_ID=your_github_app_client_id
GITHUB_CLIENT_SECRET=your_github_app_client_secret

# OAuth Configuration
REDIRECT_URI=http://localhost:3000/api/auth/github/callback
MOBILE_APP_SCHEME=voicecode

# Security
JWT_SECRET=your_jwt_secret_key_here
ALLOWED_ORIGINS=http://localhost:3000
```

## API Endpoints

### Authentication
- `GET /api/auth/github` - Initialize OAuth flow
- `GET /api/auth/github/callback` - Handle OAuth callback
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - Logout user

### Repositories
- `GET /api/repositories` - List user's repositories
- `GET /api/repositories/:owner/:repo/branches` - List repository branches

### Utility
- `GET /health` - Service health check

## Next Steps for Phase 2

1. **Repository Pulling in Sandbox (TV3)**
   - Implement sandbox creation with specific repositories
   - Test git operations in sandbox environment

2. **Request Mechanism to Sandbox (TV4)**
   - Build HTTP server in sandbox for command processing
   - Implement Claude Code CLI execution endpoint
   - Set up completion notifications via hooks

3. **Authentication Bridge**
   - Implement `claude setup-token` automation
   - Handle OAuth URL extraction from sandbox logs
   - Test token persistence and CLI usage

## Security Notes

- JWT tokens expire after 7 days
- GitHub access tokens are stored in memory (use Redis in production)
- Rate limiting is applied to all API endpoints
- CORS is configured for allowed origins only

## Troubleshooting

### Daytona Image Issues
- Ensure Docker is running and you have AMD64 platform support
- Check if Daytona CLI is properly configured
- Verify Node.js 20.x is compatible with Claude Code CLI

### GitHub App Issues
- Verify client ID and secret are correct
- Check redirect URI matches exactly
- Ensure GitHub App has correct permissions
- Test OAuth flow in browser first

## Technical Validation Status

- **TV1 (Custom Daytona Image)**: ✅ Ready for testing
- **TV2 (GitHub Apps Integration)**: ✅ Ready for testing
- **TV3 (Repository Pulling)**: ⏳ Next phase
- **TV4 (Sandbox Communication)**: ⏳ Next phase