"""Database configuration and models for VoiceCode API"""

from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer, ForeignKey, Index, text, <PERSON>olean, Float, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import uuid
from datetime import datetime, timezone
import logging
import enum

logger = logging.getLogger(__name__)

class TaskStatus(str, enum.Enum):
    """Enum for task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DatabaseSettings(BaseSettings):
    """Database configuration settings"""
    database_url: str = Field(
        default="postgresql://voicecode:voicecode@localhost:5432/voicecode",
        env="DATABASE_URL",
        description="PostgreSQL database connection URL"
    )
    
    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields from .env

# Database settings
db_settings = DatabaseSettings()

# Create SQLAlchemy engine
engine = create_engine(
    db_settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # Set to True for SQL debugging
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all models
Base = declarative_base()

class ChatMessage(Base):
    """Chat message model for storing sandbox chat history"""
    __tablename__ = "chat_messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sandbox_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    message_type = Column(String(20), nullable=False)  # 'user', 'system', 'error', 'status'
    content = Column(Text, nullable=False)
    command_id = Column(UUID(as_uuid=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), index=True)
    message_metadata = Column(JSONB, default=dict)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_chat_messages_sandbox_created', 'sandbox_id', 'created_at'),
        Index('idx_chat_messages_user_created', 'user_id', 'created_at'),
    )
    
    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": str(self.id),
            "sandbox_id": str(self.sandbox_id),
            "user_id": str(self.user_id),
            "message_type": self.message_type,
            "content": self.content,
            "command_id": str(self.command_id) if self.command_id else None,
            "created_at": self.created_at.isoformat(),
            "metadata": self.message_metadata or {}
        }

class WaitlistEntry(Base):
    """Waitlist entry model for storing email submissions"""
    __tablename__ = "waitlist_entries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), nullable=False, unique=True, index=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), index=True)
    is_notified = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_suspicious = Column(Boolean, default=False, nullable=False, index=True)
    entry_metadata = Column("metadata", JSONB, default=dict)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_waitlist_email', 'email'),
        Index('idx_waitlist_created', 'created_at'),
        Index('idx_waitlist_suspicious', 'is_suspicious'),
    )
    
    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": str(self.id),
            "email": self.email,
            "created_at": self.created_at.isoformat(),
            "is_notified": self.is_notified,
            "is_verified": self.is_verified,
            "is_suspicious": self.is_suspicious,
            "metadata": self.entry_metadata or {}
        }

class Task(Base):
    """Task model for tracking command execution state and history"""
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sandbox_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    command = Column(Text, nullable=False)
    status = Column(SQLEnum(TaskStatus, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=TaskStatus.PENDING, index=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    exit_code = Column(Integer, nullable=True)
    execution_time = Column(Float, nullable=True)
    logs = Column(JSONB, default=list)
    task_metadata = Column("metadata", JSONB, default=dict)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), index=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Optional relationship to ChatMessage
    chat_message_id = Column(UUID(as_uuid=True), ForeignKey('chat_messages.id'), nullable=True)
    chat_message = relationship("ChatMessage")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_tasks_sandbox', 'sandbox_id'),
        Index('idx_tasks_user', 'user_id'),
        Index('idx_tasks_status', 'status'),
        Index('idx_tasks_sandbox_status', 'sandbox_id', 'status'),
        Index('idx_tasks_created', 'created_at'),
    )
    
    def calculate_execution_time(self) -> Optional[float]:
        """Calculate execution time in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        elif self.started_at and self.status == TaskStatus.RUNNING:
            return (datetime.now(timezone.utc) - self.started_at).total_seconds()
        return None
    
    @property
    def is_active(self) -> bool:
        """Check if task is currently active (running or pending)"""
        return self.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
    
    def add_log_entry(self, log_entry: dict):
        """Add a log entry to the logs array"""
        if self.logs is None:
            self.logs = []
        self.logs.append({
            **log_entry,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    def to_dict(self):
        """Convert model to dictionary"""
        execution_time = self.calculate_execution_time()
        return {
            "id": str(self.id),
            "sandbox_id": str(self.sandbox_id),
            "user_id": str(self.user_id),
            "command": self.command,
            "status": self.status.value,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "exit_code": self.exit_code,
            "execution_time": execution_time,
            "logs": self.logs or [],
            "metadata": self.task_metadata or {},
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "chat_message_id": str(self.chat_message_id) if self.chat_message_id else None
        }

def get_db() -> Session:
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database tables created successfully")
    except Exception as e:
        logger.error(f"❌ Failed to create database tables: {e}")
        raise

def test_database_connection():
    """Test database connection"""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        logger.info("✅ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False