[project]
name = "fastapi-app"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core FastAPI dependencies
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "python-multipart>=0.0.20",
    
    # Pydantic and validation
    "pydantic[email]>=2.11.7",
    "pydantic-settings>=2.10.1",
    "email-validator>=2.1.0",
    
    # HTTP client
    "httpx>=0.28.1",
    
    # Database
    "sqlalchemy>=2.0.41",
    "alembic>=1.16.4",
    "psycopg2-binary>=2.9.10",
    
    # Redis
    "redis>=6.2.0",
    
    # Rate limiting
    "slowapi>=0.1.9",
    
    # DNS resolution
    "dnspython>=2.4.2",
    
    # JWT and Security (use PyJWT only, removed python-jose conflict)
    "pyjwt>=2.10.1",
    "cryptography>=45.0.5",
    "passlib[bcrypt]>=1.7.4",
    
    # Environment management
    "python-dotenv>=1.1.1",
    
    # Daytona SDK
    "daytona>=0.22.0",
]
