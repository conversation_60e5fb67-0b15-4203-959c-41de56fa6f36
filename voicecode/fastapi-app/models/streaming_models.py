"""Pydantic models for streaming API endpoints"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime


class TaskExecutionRequest(BaseModel):
    """Request model for task execution with streaming"""
    command: str = Field(..., description="Command to execute in the sandbox")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Optional metadata for the task")
    working_directory: Optional[str] = Field(
        "/home/<USER>/workspace/repository", 
        description="Working directory for command execution"
    )


class SSEMessage(BaseModel):
    """Server-Sent Event message format compatible with Vercel AI SDK"""
    id: str = Field(..., description="Unique message identifier")
    role: str = Field("assistant", description="Message role (assistant by default)")
    content: str = Field(..., description="Message content")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")


class TaskStreamResponse(BaseModel):
    """Response model for streaming task execution"""
    task_id: str = Field(..., description="Unique task identifier")
    status: str = Field(..., description="Task status")
    stream_url: Optional[str] = Field(None, description="SSE stream URL if applicable")
    message: str = Field(..., description="Response message")


class StreamingError(BaseModel):
    """Error model for streaming operations"""
    error_type: str = Field(..., description="Type of error")
    message: str = Field(..., description="Error message")
    task_id: Optional[str] = Field(None, description="Related task ID if applicable")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")