"""Log models for Daytona streaming integration"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any


class LogType(Enum):
    """Types of log entries from Daytona streaming"""
    STDOUT = "stdout"
    STDERR = "stderr"
    SYSTEM = "system"
    ERROR = "error"
    START = "start"
    END = "end"


@dataclass
class LogEntry:
    """Individual log entry from Daytona streaming"""
    timestamp: datetime
    log_type: LogType
    content: str
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "log_type": self.log_type.value,
            "content": self.content,
            "metadata": self.metadata or {}
        }
    
    @classmethod
    def from_daytona_log(cls, daytona_log: Dict[str, Any]) -> "LogEntry":
        """Create LogEntry from Daytona SDK log format"""
        # Map Daytona log types to our LogType enum
        log_type_mapping = {
            "stdout": LogType.STDOUT,
            "stderr": LogType.STDERR,
            "system": LogType.SYSTEM,
            "error": LogType.ERROR,
            "start": LogType.START,
            "end": LogType.END
        }
        
        log_type_str = daytona_log.get("type", "system")
        log_type = log_type_mapping.get(log_type_str, LogType.SYSTEM)
        
        # Extract timestamp - try different formats
        timestamp = datetime.now()
        if "timestamp" in daytona_log:
            try:
                if isinstance(daytona_log["timestamp"], str):
                    timestamp = datetime.fromisoformat(daytona_log["timestamp"].replace("Z", "+00:00"))
                elif isinstance(daytona_log["timestamp"], datetime):
                    timestamp = daytona_log["timestamp"]
            except (ValueError, TypeError):
                # Fallback to current time if timestamp parsing fails
                pass
        
        return cls(
            timestamp=timestamp,
            log_type=log_type,
            content=daytona_log.get("content", daytona_log.get("message", "")),
            metadata=daytona_log.get("metadata", {})
        )


@dataclass
class StreamingSession:
    """Represents a Daytona streaming session"""
    session_id: str
    sandbox_id: str
    command: Optional[str] = None
    created_at: datetime = None
    status: str = "active"
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "session_id": self.session_id,
            "sandbox_id": self.sandbox_id,
            "command": self.command,
            "created_at": self.created_at.isoformat(),
            "status": self.status,
            "metadata": self.metadata or {}
        }