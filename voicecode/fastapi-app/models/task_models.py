"""Pydantic models for Task API endpoints"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class TaskStatus(str, Enum):
    """Task execution status enum"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskCreate(BaseModel):
    """Model for creating a new task"""
    sandbox_id: uuid.UUID = Field(..., description="UUID of the sandbox")
    user_id: uuid.UUID = Field(..., description="UUID of the user")
    command: str = Field(..., min_length=1, description="Command to execute")
    chat_message_id: Optional[uuid.UUID] = Field(None, description="Optional chat message that initiated the task")
    task_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional task metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "sandbox_id": "123e4567-e89b-12d3-a456-************",
                "user_id": "123e4567-e89b-12d3-a456-************",
                "command": "npm run test",
                "chat_message_id": "123e4567-e89b-12d3-a456-************",
                "task_metadata": {"priority": "high", "source": "voice_command"}
            }
        }

class TaskUpdate(BaseModel):
    """Model for updating task status and results"""
    status: Optional[TaskStatus] = Field(None, description="Task execution status")
    started_at: Optional[datetime] = Field(None, description="When task execution started")
    completed_at: Optional[datetime] = Field(None, description="When task execution completed")
    exit_code: Optional[int] = Field(None, description="Command exit code")
    execution_time: Optional[float] = Field(None, ge=0, description="Execution time in seconds")
    logs: Optional[List[Dict[str, Any]]] = Field(None, description="Array of log entries")
    task_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional task metadata")
    
    @validator('exit_code')
    def validate_exit_code(cls, v):
        if v is not None and not (0 <= v <= 255):
            raise ValueError('Exit code must be between 0 and 255')
        return v
    
    @validator('status')
    def validate_status_transition(cls, v):
        # Basic validation - more complex validation should be in the service layer
        if v is not None and v not in TaskStatus:
            raise ValueError(f'Invalid status: {v}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "status": "completed",
                "started_at": "2024-01-15T10:30:00Z",
                "completed_at": "2024-01-15T10:32:15Z",
                "exit_code": 0,
                "execution_time": 135.5,
                "logs": [
                    {"level": "info", "message": "Starting test suite"},
                    {"level": "success", "message": "All tests passed"}
                ],
                "task_metadata": {"tests_passed": 42, "tests_failed": 0}
            }
        }

class TaskResponse(BaseModel):
    """Model for task API responses"""
    id: uuid.UUID = Field(..., description="Task UUID")
    sandbox_id: uuid.UUID = Field(..., description="Sandbox UUID")
    user_id: uuid.UUID = Field(..., description="User UUID")
    command: str = Field(..., description="Executed command")
    status: TaskStatus = Field(..., description="Current task status")
    started_at: Optional[datetime] = Field(None, description="Task start time")
    completed_at: Optional[datetime] = Field(None, description="Task completion time")
    exit_code: Optional[int] = Field(None, description="Command exit code")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    logs: List[Dict[str, Any]] = Field(default_factory=list, description="Task logs")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Task metadata")
    created_at: datetime = Field(..., description="Task creation time")
    updated_at: datetime = Field(..., description="Task last update time")
    chat_message_id: Optional[uuid.UUID] = Field(None, description="Associated chat message")
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "sandbox_id": "123e4567-e89b-12d3-a456-************",
                "user_id": "123e4567-e89b-12d3-a456-************",
                "command": "npm run test",
                "status": "completed",
                "started_at": "2024-01-15T10:30:00Z",
                "completed_at": "2024-01-15T10:32:15Z",
                "exit_code": 0,
                "execution_time": 135.5,
                "logs": [
                    {"level": "info", "message": "Starting test suite", "timestamp": "2024-01-15T10:30:01Z"},
                    {"level": "success", "message": "All tests passed", "timestamp": "2024-01-15T10:32:14Z"}
                ],
                "metadata": {"tests_passed": 42, "tests_failed": 0},
                "created_at": "2024-01-15T10:29:45Z",
                "updated_at": "2024-01-15T10:32:15Z",
                "chat_message_id": "123e4567-e89b-12d3-a456-426614174003"
            }
        }

class TaskListResponse(BaseModel):
    """Model for paginated task list responses"""
    tasks: List[TaskResponse] = Field(..., description="List of tasks")
    total: int = Field(..., ge=0, description="Total number of tasks")
    page: int = Field(..., ge=1, description="Current page number")
    limit: int = Field(..., ge=1, le=100, description="Number of tasks per page")
    has_next: bool = Field(..., description="Whether there are more pages")
    
    class Config:
        schema_extra = {
            "example": {
                "tasks": [],
                "total": 156,
                "page": 1,
                "limit": 20,
                "has_next": True
            }
        }

class LogEntry(BaseModel):
    """Model for individual log entries"""
    level: str = Field(..., description="Log level (info, warning, error, etc.)")
    message: str = Field(..., description="Log message")
    timestamp: Optional[datetime] = Field(None, description="Log entry timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional log metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "level": "info",
                "message": "Command executed successfully",
                "timestamp": "2024-01-15T10:30:15Z",
                "metadata": {"exit_code": 0, "duration": 2.5}
            }
        }