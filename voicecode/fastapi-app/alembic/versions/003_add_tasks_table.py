"""Add tasks table

Revision ID: 003
Revises: 002
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None

def upgrade() -> None:
    """Create tasks table with all required fields and indexes"""
    
    # Create TaskStatus enum type
    task_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'cancelled',
        name='taskstatus',
        create_type=False
    )
    task_status_enum.create(op.get_bind(), checkfirst=True)
    
    # Create tasks table
    op.create_table('tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('sandbox_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('command', sa.Text(), nullable=False),
        sa.Column('status', task_status_enum, nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('exit_code', sa.Integer(), nullable=True),
        sa.Column('execution_time', sa.Float(), nullable=True),
        sa.Column('logs', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('chat_message_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(['chat_message_id'], ['chat_messages.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('id', name=op.f('uq_tasks_id'))
    )
    
    # Create indexes for performance optimization
    op.create_index('idx_tasks_sandbox', 'tasks', ['sandbox_id'], unique=False)
    op.create_index('idx_tasks_user', 'tasks', ['user_id'], unique=False)
    op.create_index('idx_tasks_status', 'tasks', ['status'], unique=False)
    op.create_index('idx_tasks_sandbox_status', 'tasks', ['sandbox_id', 'status'], unique=False)
    op.create_index('idx_tasks_created', 'tasks', ['created_at'], unique=False)
    
    # Set default values for new columns
    op.execute(text("ALTER TABLE tasks ALTER COLUMN status SET DEFAULT 'pending'"))
    op.execute(text("ALTER TABLE tasks ALTER COLUMN logs SET DEFAULT '[]'"))
    op.execute(text("ALTER TABLE tasks ALTER COLUMN metadata SET DEFAULT '{}'"))
    op.execute(text("ALTER TABLE tasks ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP"))
    op.execute(text("ALTER TABLE tasks ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP"))

def downgrade() -> None:
    """Drop tasks table and related indexes"""
    
    # Drop indexes first
    op.drop_index('idx_tasks_created', table_name='tasks')
    op.drop_index('idx_tasks_sandbox_status', table_name='tasks')
    op.drop_index('idx_tasks_status', table_name='tasks')
    op.drop_index('idx_tasks_user', table_name='tasks')
    op.drop_index('idx_tasks_sandbox', table_name='tasks')
    
    # Drop the table
    op.drop_table('tasks')
    
    # Drop the enum type
    task_status_enum = postgresql.ENUM(name='taskstatus')
    task_status_enum.drop(op.get_bind(), checkfirst=True)