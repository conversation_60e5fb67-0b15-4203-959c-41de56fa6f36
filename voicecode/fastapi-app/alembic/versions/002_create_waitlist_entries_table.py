"""Create waitlist_entries table

Revision ID: 002
Revises: 001
Create Date: 2025-01-27 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('waitlist_entries',
    sa.<PERSON>umn('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_notified', sa.<PERSON>(), nullable=False),
    sa.Column('is_verified', sa.<PERSON>(), nullable=False),
    sa.Column('is_suspicious', sa.<PERSON>(), nullable=False),
    sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    op.create_index('idx_waitlist_created', 'waitlist_entries', ['created_at'], unique=False)
    op.create_index('idx_waitlist_email', 'waitlist_entries', ['email'], unique=False)
    op.create_index('idx_waitlist_suspicious', 'waitlist_entries', ['is_suspicious'], unique=False)
    op.create_index(op.f('ix_waitlist_entries_created_at'), 'waitlist_entries', ['created_at'], unique=False)
    op.create_index(op.f('ix_waitlist_entries_email'), 'waitlist_entries', ['email'], unique=False)
    op.create_index(op.f('ix_waitlist_entries_is_suspicious'), 'waitlist_entries', ['is_suspicious'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_waitlist_entries_is_suspicious'), table_name='waitlist_entries')
    op.drop_index(op.f('ix_waitlist_entries_email'), table_name='waitlist_entries')
    op.drop_index(op.f('ix_waitlist_entries_created_at'), table_name='waitlist_entries')
    op.drop_index('idx_waitlist_suspicious', table_name='waitlist_entries')
    op.drop_index('idx_waitlist_email', table_name='waitlist_entries')
    op.drop_index('idx_waitlist_created', table_name='waitlist_entries')
    op.drop_table('waitlist_entries')
    # ### end Alembic commands ###