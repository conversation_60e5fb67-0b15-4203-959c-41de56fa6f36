"""Unit tests for TaskService"""

import pytest
import asyncio
from unittest.mock import Mock, MagicMock, patch
from uuid import uuid4, UUID
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session

from database import Task, TaskStatus
from services.task_service import TaskService
from services.exceptions import (
    TaskServiceError,
    TaskAlreadyRunningError,
    TaskNotFoundError,
    UnauthorizedTaskAccessError
)


class TestTaskService:
    """Test suite for TaskService"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client"""
        redis_mock = Mock()
        redis_mock.set.return_value = True  # Lock acquired successfully
        redis_mock.delete.return_value = True
        return redis_mock
    
    @pytest.fixture
    def mock_daytona_service(self):
        """Mock Daytona service"""
        return Mock()
    
    @pytest.fixture
    def task_service(self, mock_db_session, mock_redis_client, mock_daytona_service):
        """TaskService instance with mocked dependencies"""
        return TaskService(
            db_session=mock_db_session,
            redis_client=mock_redis_client,
            daytona_service=mock_daytona_service
        )
    
    @pytest.fixture
    def sample_task(self):
        """Sample task for testing"""
        task_id = uuid4()
        sandbox_id = uuid4()
        user_id = uuid4()
        
        task = Task(
            id=task_id,
            sandbox_id=sandbox_id,
            user_id=user_id,
            command="echo 'hello world'",
            status=TaskStatus.PENDING,
            task_metadata={"test": True}
        )
        return task
    
    @pytest.mark.asyncio
    async def test_create_task_success(self, task_service, mock_db_session):
        """Test successful task creation"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "echo 'test'"
        metadata = {"test": True}
        
        # Mock no existing active tasks - need to mock the full query chain
        mock_query = Mock()
        mock_filter = Mock()
        mock_filter.first.return_value = None
        mock_query.filter.return_value = mock_filter
        mock_db_session.query.return_value = mock_query
        
        # Mock task creation
        created_task = Mock(spec=Task)
        created_task.id = uuid4()
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()
        mock_db_session.refresh = Mock()
        
        with patch('services.task_service.Task') as mock_task_class, \
             patch('services.task_service.and_'):  # Mock the and_ function
            mock_task_class.return_value = created_task
            
            result = await task_service.create_task(sandbox_id, user_id, command, metadata)
            
            # Verify task was created correctly
            mock_task_class.assert_called_once_with(
                sandbox_id=sandbox_id,
                user_id=user_id,
                command=command,
                status=TaskStatus.PENDING,
                task_metadata=metadata
            )
            
            mock_db_session.add.assert_called_once_with(created_task)
            mock_db_session.commit.assert_called_once()
            mock_db_session.refresh.assert_called_once_with(created_task)
            
            assert result == created_task
    
    @pytest.mark.asyncio
    async def test_create_task_already_running(self, task_service, mock_db_session, sample_task):
        """Test task creation when task already running"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "echo 'test'"
        
        # Mock existing active task
        existing_task = sample_task
        existing_task.status = TaskStatus.RUNNING
        
        # Mock the query chain properly
        mock_query = Mock()
        mock_filter = Mock()
        mock_filter.first.return_value = existing_task
        mock_query.filter.return_value = mock_filter
        mock_db_session.query.return_value = mock_query
        
        # Should raise TaskAlreadyRunningError
        with patch('services.task_service.and_'), \
             pytest.raises(TaskAlreadyRunningError) as exc_info:
            await task_service.create_task(sandbox_id, user_id, command)
        
        assert str(sandbox_id) in str(exc_info.value)
        assert str(existing_task.id) in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_create_task_redis_lock_failure(self, task_service, mock_redis_client):
        """Test task creation when Redis lock fails"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "echo 'test'"
        
        # Mock Redis lock failure
        mock_redis_client.set.return_value = False
        
        with pytest.raises(TaskServiceError) as exc_info:
            await task_service.create_task(sandbox_id, user_id, command)
        
        assert "Failed to acquire lock" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_current_task_exists(self, task_service, mock_db_session, sample_task):
        """Test getting current active task"""
        sandbox_id = sample_task.sandbox_id
        user_id = sample_task.user_id
        
        # Mock finding active task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        result = await task_service.get_current_task(sandbox_id, user_id)
        
        assert result == sample_task
    
    @pytest.mark.asyncio
    async def test_get_current_task_none(self, task_service, mock_db_session):
        """Test getting current task when none exists"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Mock no active task found
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await task_service.get_current_task(sandbox_id, user_id)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cancel_task_success(self, task_service, mock_db_session, sample_task):
        """Test successful task cancellation"""
        task_id = sample_task.id
        user_id = sample_task.user_id
        sample_task.status = TaskStatus.RUNNING
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        # Mock datetime for completed_at
        with patch('services.task_service.datetime') as mock_datetime:
            mock_now = datetime.now(timezone.utc)
            mock_datetime.now.return_value = mock_now
            
            result = await task_service.cancel_task(task_id, user_id)
            
            assert result is True
            assert sample_task.status == TaskStatus.CANCELLED
            assert sample_task.completed_at == mock_now
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cancel_task_not_found(self, task_service, mock_db_session):
        """Test cancelling non-existent task"""
        task_id = uuid4()
        user_id = uuid4()
        
        # Mock task not found
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(TaskNotFoundError) as exc_info:
            await task_service.cancel_task(task_id, user_id)
        
        assert str(task_id) in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_cancel_task_unauthorized(self, task_service, mock_db_session, sample_task):
        """Test cancelling task by unauthorized user"""
        task_id = sample_task.id
        unauthorized_user_id = uuid4()  # Different user
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        with pytest.raises(UnauthorizedTaskAccessError) as exc_info:
            await task_service.cancel_task(task_id, unauthorized_user_id)
        
        assert str(task_id) in str(exc_info.value)
        assert str(unauthorized_user_id) in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_cancel_task_wrong_status(self, task_service, mock_db_session, sample_task):
        """Test cancelling already completed task"""
        task_id = sample_task.id
        user_id = sample_task.user_id
        sample_task.status = TaskStatus.COMPLETED  # Already completed
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        result = await task_service.cancel_task(task_id, user_id)
        
        assert result is False  # Cannot cancel completed task
    
    @pytest.mark.asyncio
    async def test_complete_task_success(self, task_service, mock_db_session, sample_task):
        """Test successful task completion"""
        task_id = sample_task.id
        exit_code = 0
        final_logs = ["Task completed successfully", "All tests passed"]
        
        sample_task.status = TaskStatus.RUNNING
        sample_task.started_at = datetime.now(timezone.utc) - timedelta(seconds=10)
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        # Mock calculate_execution_time
        sample_task.calculate_execution_time = Mock(return_value=10.5)
        sample_task.add_log_entry = Mock()
        
        with patch('services.task_service.datetime') as mock_datetime:
            mock_now = datetime.now(timezone.utc)
            mock_datetime.now.return_value = mock_now
            
            result = await task_service.complete_task(task_id, exit_code, final_logs)
            
            assert result == sample_task
            assert sample_task.status == TaskStatus.COMPLETED
            assert sample_task.exit_code == exit_code
            assert sample_task.completed_at == mock_now
            
            # Verify log entries were added
            assert sample_task.add_log_entry.call_count == len(final_logs) + 1  # final_logs + completion log
    
    @pytest.mark.asyncio
    async def test_complete_task_failure(self, task_service, mock_db_session, sample_task):
        """Test task completion with failure exit code"""
        task_id = sample_task.id
        exit_code = 1  # Failure
        
        sample_task.status = TaskStatus.RUNNING
        sample_task.calculate_execution_time = Mock(return_value=5.0)
        sample_task.add_log_entry = Mock()
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        result = await task_service.complete_task(task_id, exit_code)
        
        assert result == sample_task
        assert sample_task.status == TaskStatus.FAILED
        assert sample_task.exit_code == exit_code
    
    @pytest.mark.asyncio
    async def test_start_task_success(self, task_service, mock_db_session, sample_task):
        """Test successful task start"""
        task_id = sample_task.id
        sample_task.status = TaskStatus.PENDING
        sample_task.add_log_entry = Mock()
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        with patch('services.task_service.datetime') as mock_datetime:
            mock_now = datetime.now(timezone.utc)
            mock_datetime.now.return_value = mock_now
            
            result = await task_service.start_task(task_id)
            
            assert result == sample_task
            assert sample_task.status == TaskStatus.RUNNING
            assert sample_task.started_at == mock_now
            sample_task.add_log_entry.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_task_wrong_status(self, task_service, mock_db_session, sample_task):
        """Test starting task with wrong status"""
        task_id = sample_task.id
        sample_task.status = TaskStatus.RUNNING  # Already running
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        with pytest.raises(TaskServiceError) as exc_info:
            await task_service.start_task(task_id)
        
        assert "Cannot start task with status" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_task_success(self, task_service, mock_db_session, sample_task):
        """Test getting task with authorization"""
        task_id = sample_task.id
        user_id = sample_task.user_id
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        result = await task_service.get_task(task_id, user_id)
        
        assert result == sample_task
    
    @pytest.mark.asyncio
    async def test_get_task_unauthorized(self, task_service, mock_db_session, sample_task):
        """Test getting task by unauthorized user"""
        task_id = sample_task.id
        unauthorized_user_id = uuid4()
        
        # Mock finding task
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_task
        
        with pytest.raises(UnauthorizedTaskAccessError):
            await task_service.get_task(task_id, unauthorized_user_id)
    
    @pytest.mark.asyncio
    async def test_list_tasks(self, task_service, mock_db_session):
        """Test listing tasks with filters"""
        sandbox_id = uuid4()
        user_id = uuid4()
        status = TaskStatus.COMPLETED
        limit = 10
        offset = 5
        
        # Mock query chain
        mock_query = mock_db_session.query.return_value
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [Mock(spec=Task) for _ in range(3)]
        
        result = await task_service.list_tasks(
            sandbox_id=sandbox_id,
            user_id=user_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        assert len(result) == 3
        # Verify query was built with filters
        assert mock_query.filter.call_count == 3  # sandbox_id, user_id, status
        mock_query.offset.assert_called_once_with(offset)
        mock_query.limit.assert_called_once_with(limit)
    
    @pytest.mark.asyncio
    async def test_cleanup_old_tasks(self, task_service, mock_db_session):
        """Test cleaning up old tasks"""
        days_old = 7
        
        # Mock query and delete
        mock_query = mock_db_session.query.return_value
        mock_query.filter.return_value = mock_query
        mock_query.delete.return_value = 5  # 5 tasks deleted
        
        with patch('services.task_service.datetime') as mock_datetime:
            mock_now = datetime.now(timezone.utc)
            mock_datetime.now.return_value = mock_now
            
            result = await task_service.cleanup_old_tasks(days_old)
            
            assert result == 5
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_task_stats(self, task_service, mock_db_session):
        """Test getting task statistics"""
        sandbox_id = uuid4()
        
        # Mock query chain for counts
        mock_query = mock_db_session.query.return_value
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 10
        mock_query.with_entities.return_value = mock_query
        mock_query.scalar.return_value = 15.5  # Average execution time
        
        result = await task_service.get_task_stats(sandbox_id)
        
        assert "total_count" in result
        assert "avg_execution_time" in result
        assert result["avg_execution_time"] == 15.5
        
        # Should have counts for each status
        for status in TaskStatus:
            assert f"{status.value}_count" in result
    
    @pytest.mark.asyncio
    async def test_redis_lock_context_manager(self, task_service, mock_redis_client):
        """Test Redis lock context manager"""
        lock_key = "test_lock"
        
        # Test successful lock acquisition
        with task_service._redis_lock(lock_key) as acquired:
            assert acquired is True
        
        mock_redis_client.set.assert_called_once()
        mock_redis_client.delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_redis_lock_failure(self, task_service, mock_redis_client):
        """Test Redis lock failure"""
        lock_key = "test_lock"
        mock_redis_client.set.return_value = False  # Lock not acquired
        
        with task_service._redis_lock(lock_key) as acquired:
            assert acquired is False
    
    @pytest.mark.asyncio
    async def test_task_service_without_redis(self, mock_db_session, mock_daytona_service):
        """Test TaskService works without Redis"""
        task_service = TaskService(
            db_session=mock_db_session,
            redis_client=None,  # No Redis
            daytona_service=mock_daytona_service
        )
        
        # Test lock context manager without Redis
        with task_service._redis_lock("test") as acquired:
            assert acquired is True  # Should always succeed without Redis
    
    @pytest.mark.asyncio
    async def test_database_rollback_on_error(self, task_service, mock_db_session):
        """Test database rollback on error"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "echo 'test'"
        
        # Mock database error
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add.side_effect = Exception("Database error")
        
        with pytest.raises(TaskServiceError):
            await task_service.create_task(sandbox_id, user_id, command)
        
        mock_db_session.rollback.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])