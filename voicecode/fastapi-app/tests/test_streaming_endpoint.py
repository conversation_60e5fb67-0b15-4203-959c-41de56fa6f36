"""Unit tests for streaming API endpoints"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from uuid import UUID, uuid4
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>
from sqlalchemy.orm import Session

from routers.streaming import router
from models.streaming_models import TaskExecutionRequest, SSEMessage
from utils.sse_formatter import SSEFormatter
from database import Task, TaskStatus
from services.task_service import TaskService
from services.exceptions import TaskAlreadyRunningError, TaskNotFoundError


# Test fixtures
@pytest.fixture
def app():
    """Create test FastAPI app"""
    test_app = FastAPI()
    test_app.include_router(router)
    return test_app


@pytest.fixture
def client(app):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_current_user():
    """Mock authenticated user"""
    return {
        "user": {
            "user": {"id": 12345},
            "id": 12345,
            "auth_type": "oauth"
        }
    }


@pytest.fixture
def mock_task():
    """Mock task object"""
    task_id = uuid4()
    sandbox_id = uuid4()
    user_id = uuid4()
    
    task = Mock(spec=Task)
    task.id = task_id
    task.sandbox_id = sandbox_id
    task.user_id = user_id
    task.command = "echo 'test'"
    task.status = TaskStatus.PENDING
    task.created_at = datetime.now(timezone.utc)
    task.started_at = None
    task.completed_at = None
    task.task_metadata = {}
    
    return task


@pytest.fixture
def mock_task_service():
    """Mock TaskService"""
    service = Mock(spec=TaskService)
    service.create_task = AsyncMock()
    service.get_current_task = AsyncMock()
    service.start_task = AsyncMock()
    service.complete_task = AsyncMock()
    service.cancel_task = AsyncMock()
    service.get_task = AsyncMock()
    return service


@pytest.fixture
def mock_daytona_service():
    """Mock DaytonaService"""
    service = Mock()
    service.execute_command = Mock()
    return service


@pytest.fixture
def mock_redis_client():
    """Mock Redis client"""
    redis_mock = Mock()
    redis_mock.get = Mock()
    return redis_mock


class TestSSEFormatter:
    """Test cases for SSE message formatting"""
    
    def test_format_message_basic(self):
        """Test basic message formatting"""
        formatter = SSEFormatter()
        result = formatter.format_message("test content", "task_log", "task-123")
        
        assert result.startswith("data: ")
        assert result.endswith("\n\n")
        
        # Parse the JSON content
        json_content = result[6:-2]  # Remove "data: " and "\n\n"
        message_data = json.loads(json_content)
        
        assert message_data["content"] == "test content"
        assert message_data["role"] == "assistant"
        assert message_data["metadata"]["type"] == "task_log"
        assert message_data["metadata"]["task_id"] == "task-123"
        assert "timestamp" in message_data["metadata"]
        assert "msg-" in message_data["id"]
    
    def test_format_task_start(self):
        """Test task start message formatting"""
        formatter = SSEFormatter()
        result = formatter.format_task_start("npm test", "task-456")
        
        json_content = result[6:-2]
        message_data = json.loads(json_content)
        
        assert message_data["content"] == "Executing: npm test"
        assert message_data["metadata"]["type"] == "task_start"
        assert message_data["metadata"]["task_id"] == "task-456"
        assert message_data["metadata"]["command"] == "npm test"
    
    def test_format_task_complete(self):
        """Test task completion message formatting"""
        formatter = SSEFormatter()
        result = formatter.format_task_complete("task-789", 0, 5.25)
        
        json_content = result[6:-2]
        message_data = json.loads(json_content)
        
        assert "Task completed (exit code: 0)" in message_data["content"]
        assert "5.25s" in message_data["content"]
        assert message_data["metadata"]["type"] == "task_complete"
        assert message_data["metadata"]["exit_code"] == 0
        assert message_data["metadata"]["execution_time"] == 5.25
    
    def test_format_task_error(self):
        """Test task error message formatting"""
        formatter = SSEFormatter()
        result = formatter.format_task_error("Command not found", "task-error")
        
        json_content = result[6:-2]
        message_data = json.loads(json_content)
        
        assert message_data["content"] == "Error: Command not found"
        assert message_data["metadata"]["type"] == "task_error"
        assert message_data["metadata"]["error"] == "Command not found"
    
    def test_format_done_message(self):
        """Test stream termination message"""
        formatter = SSEFormatter()
        result = formatter.format_done_message()
        
        assert result == "data: [DONE]\n\n"
    
    def test_message_id_uniqueness(self):
        """Test that message IDs are unique and sequential"""
        formatter = SSEFormatter()
        
        result1 = formatter.format_message("test1", "task_log", "task-1")
        result2 = formatter.format_message("test2", "task_log", "task-1")
        
        json1 = json.loads(result1[6:-2])
        json2 = json.loads(result2[6:-2])
        
        assert json1["id"] != json2["id"]
        assert json1["id"] == "msg-1"
        assert json2["id"] == "msg-2"


class TestStreamingModels:
    """Test cases for streaming Pydantic models"""
    
    def test_task_execution_request_valid(self):
        """Test valid task execution request"""
        request = TaskExecutionRequest(
            command="echo hello",
            metadata={"source": "test"},
            working_directory="/tmp"
        )
        
        assert request.command == "echo hello"
        assert request.metadata == {"source": "test"}
        assert request.working_directory == "/tmp"
    
    def test_task_execution_request_defaults(self):
        """Test default values for task execution request"""
        request = TaskExecutionRequest(command="ls")
        
        assert request.command == "ls"
        assert request.metadata is None
        assert request.working_directory == "/home/<USER>/workspace/repository"
    
    def test_sse_message_valid(self):
        """Test valid SSE message model"""
        message = SSEMessage(
            id="msg-1",
            content="test content",
            metadata={"type": "task_log"}
        )
        
        assert message.id == "msg-1"
        assert message.role == "assistant"  # Default value
        assert message.content == "test content"
        assert message.metadata == {"type": "task_log"}
    
    def test_sse_message_serialization(self):
        """Test SSE message JSON serialization"""
        message = SSEMessage(
            id="msg-test",
            content="serialization test",
            metadata={"timestamp": "2025-01-31T10:00:00Z"}
        )
        
        json_data = message.model_dump_json()
        parsed = json.loads(json_data)
        
        assert parsed["id"] == "msg-test"
        assert parsed["role"] == "assistant"
        assert parsed["content"] == "serialization test"
        assert parsed["metadata"]["timestamp"] == "2025-01-31T10:00:00Z"


class TestStreamingEndpoint:
    """Test cases for streaming API endpoint"""
    
    @patch('routers.streaming.get_current_user')
    @patch('routers.streaming.get_task_service')
    @patch('routers.streaming.get_daytona_service')
    @patch('routers.streaming.redis_client')
    @patch('routers.streaming.limiter')
    def test_stream_task_execution_invalid_sandbox_id(
        self, mock_limiter, mock_redis, mock_daytona, mock_task_service_dep, 
        mock_auth, client
    ):
        """Test streaming with invalid sandbox ID"""
        mock_auth.return_value = {"user": {"user": {"id": 123}}}
        mock_limiter.limit.return_value = lambda f: f  # Bypass rate limiting
        
        response = client.post(
            "/api/sandbox/invalid-uuid/tasks/stream",
            json={"command": "echo test"}
        )
        
        assert response.status_code == 400
        assert "Invalid sandbox ID format" in response.json()["detail"]
    
    @patch('routers.streaming.get_current_user')
    @patch('routers.streaming.get_task_service')
    @patch('routers.streaming.get_daytona_service')
    @patch('routers.streaming.redis_client')
    @patch('routers.streaming.limiter')
    def test_stream_task_execution_sandbox_not_found(
        self, mock_limiter, mock_redis, mock_daytona, mock_task_service_dep,
        mock_auth, client
    ):
        """Test streaming with non-existent sandbox"""
        mock_auth.return_value = {"user": {"user": {"id": 123}}}
        mock_limiter.limit.return_value = lambda f: f
        mock_redis.get.return_value = None  # Sandbox not found
        
        sandbox_id = str(uuid4())
        response = client.post(
            f"/api/sandbox/{sandbox_id}/tasks/stream",
            json={"command": "echo test"}
        )
        
        assert response.status_code == 404
        assert "Sandbox not found" in response.json()["detail"]
    
    @patch('routers.streaming.get_current_user')
    @patch('routers.streaming.get_task_service')
    @patch('routers.streaming.get_daytona_service')
    @patch('routers.streaming.redis_client')
    @patch('routers.streaming.limiter')
    def test_stream_task_execution_unauthorized(
        self, mock_limiter, mock_redis, mock_daytona, mock_task_service_dep,
        mock_auth, client
    ):
        """Test streaming with unauthorized access"""
        mock_auth.return_value = {"user": {"user": {"id": 123}}}
        mock_limiter.limit.return_value = lambda f: f
        
        # Mock sandbox owned by different user
        sandbox_data = json.dumps({
            "sandbox_id": str(uuid4()),
            "user_id": 456  # Different user
        })
        mock_redis.get.return_value = sandbox_data
        
        sandbox_id = str(uuid4())
        response = client.post(
            f"/api/sandbox/{sandbox_id}/tasks/stream",
            json={"command": "echo test"}
        )
        
        assert response.status_code == 403
        assert "Not authorized to access this sandbox" in response.json()["detail"]
    
    @patch('routers.streaming.get_current_user')
    @patch('routers.streaming.get_task_service')
    @patch('routers.streaming.get_daytona_service')
    @patch('routers.streaming.redis_client')
    @patch('routers.streaming.limiter')
    async def test_stream_task_execution_existing_task(
        self, mock_limiter, mock_redis, mock_daytona, mock_task_service_dep,
        mock_auth, client, mock_task_service, mock_task
    ):
        """Test streaming when task already running"""
        mock_auth.return_value = {"user": {"user": {"id": 123}}}
        mock_limiter.limit.return_value = lambda f: f
        mock_task_service_dep.return_value = mock_task_service
        
        # Mock authorized sandbox
        sandbox_id = str(uuid4())
        sandbox_data = json.dumps({
            "sandbox_id": sandbox_id,
            "user_id": 123
        })
        mock_redis.get.return_value = sandbox_data
        
        # Mock existing active task
        mock_task_service.get_current_task.return_value = mock_task
        
        response = client.post(
            f"/api/sandbox/{sandbox_id}/tasks/stream",
            json={"command": "echo test"}
        )
        
        assert response.status_code == 409
        assert "Task already running" in response.json()["detail"]


class TestStreamingUtilities:
    """Test cases for streaming utility functions"""
    
    def test_create_sse_generator_empty_stream(self):
        """Test SSE generator with empty log stream"""
        def empty_stream():
            return
            yield  # Never reached
        
        formatter = SSEFormatter()
        generator = create_sse_generator(empty_stream(), "task-123", formatter)
        
        messages = list(generator)
        assert len(messages) == 1
        assert messages[0] == "data: [DONE]\n\n"
    
    def test_create_sse_generator_with_error(self):
        """Test SSE generator when stream raises exception"""
        def error_stream():
            yield Mock(content="start")
            raise ValueError("Stream error")
        
        formatter = SSEFormatter()
        generator = create_sse_generator(error_stream(), "task-456", formatter)
        
        messages = list(generator)
        # Should have at least error message and [DONE]
        assert len(messages) >= 2
        assert messages[-1] == "data: [DONE]\n\n"
        
        # Check that error message is included
        error_found = any("Stream error" in msg for msg in messages)
        assert error_found


class TestTaskServiceIntegration:
    """Test cases for TaskService integration with streaming"""
    
    @pytest.mark.asyncio
    async def test_task_lifecycle_in_streaming(self, mock_task_service, mock_task):
        """Test complete task lifecycle during streaming"""
        # Setup mock task service
        mock_task_service.create_task.return_value = mock_task
        mock_task_service.get_current_task.return_value = None  # No existing task
        mock_task_service.start_task.return_value = mock_task
        mock_task_service.complete_task.return_value = mock_task
        
        # Simulate task creation
        task = await mock_task_service.create_task(
            sandbox_id=mock_task.sandbox_id,
            user_id=mock_task.user_id,
            command="echo test",
            metadata={}
        )
        
        assert task == mock_task
        mock_task_service.create_task.assert_called_once()
        
        # Simulate task start
        await mock_task_service.start_task(task.id)
        mock_task_service.start_task.assert_called_once_with(task.id)
        
        # Simulate task completion
        await mock_task_service.complete_task(task.id, 0, [])
        mock_task_service.complete_task.assert_called_once_with(task.id, 0, [])


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])