"""Unit tests for DaytonaService streaming functionality"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import AsyncGenerator

from services.daytona_service import (
    DaytonaService,
    SessionPool,
    DaytonaStreamingError,
    StreamingConnectionError,
    SessionTimeoutError,
    SessionManagerError
)
from models.log_models import LogEntry, LogType, StreamingSession
from daytona import DaytonaConfig


@pytest.fixture
def mock_daytona_config():
    """Mock Daytona configuration"""
    return DaytonaConfig(
        api_key="test_key",
        api_url="http://test.daytona.com",
        target="test",
        organization_id="test_org"
    )


@pytest.fixture
def mock_daytona():
    """Mock Daytona SDK"""
    mock = Mock()
    mock_sandbox = Mock()
    mock_sandbox.process.exec.return_value = Mock(exit_code=0, result="test output\nsecond line")
    mock.get.return_value = mock_sandbox
    return mock


@pytest_asyncio.fixture
async def daytona_service(mock_daytona_config):
    """Create DaytonaService instance for testing"""
    with patch('services.daytona_service.Daytona') as mock_daytona_class:
        mock_daytona_class.return_value = Mock()
        service = DaytonaService(mock_daytona_config)
        await service.initialize()
        yield service
        await service.cleanup()


class TestSessionPool:
    """Test SessionPool functionality"""
    
    @pytest.fixture
    def session_pool(self):
        """Create SessionPool for testing"""
        return SessionPool(max_sessions=3, session_timeout=60)
    
    def test_init(self, session_pool):
        """Test SessionPool initialization"""
        assert session_pool.max_sessions == 3
        assert session_pool.session_timeout == 60
        assert len(session_pool.active_sessions) == 0
    
    @pytest.mark.asyncio
    async def test_get_session_new(self, session_pool):
        """Test creating a new session"""
        session_id = await session_pool.get_session("sandbox123")
        
        assert session_id in session_pool.active_sessions
        session = session_pool.active_sessions[session_id]
        assert session.sandbox_id == "sandbox123"
        assert session.status == "active"
    
    @pytest.mark.asyncio
    async def test_get_session_reuse(self, session_pool):
        """Test reusing existing session"""
        # Create first session
        session_id1 = await session_pool.get_session("sandbox123")
        
        # Should reuse the same session
        session_id2 = await session_pool.get_session("sandbox123")
        assert session_id1 == session_id2
        assert len(session_pool.active_sessions) == 1
    
    @pytest.mark.asyncio
    async def test_session_limit(self, session_pool):
        """Test session limit enforcement"""
        # Fill up the session pool
        session_ids = []
        for i in range(3):
            session_id = await session_pool.get_session(f"sandbox{i}")
            session_ids.append(session_id)
        
        assert len(session_pool.active_sessions) == 3
        
        # Adding one more should remove the oldest
        new_session_id = await session_pool.get_session("sandbox_new")
        assert len(session_pool.active_sessions) == 3
        assert session_ids[0] not in session_pool.active_sessions
        assert new_session_id in session_pool.active_sessions
    
    @pytest.mark.asyncio
    async def test_release_session(self, session_pool):
        """Test releasing a session"""
        session_id = await session_pool.get_session("sandbox123")
        await session_pool.release_session(session_id)
        
        session = session_pool.active_sessions[session_id]
        assert session.status == "completed"
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_sessions(self, session_pool):
        """Test cleanup of expired sessions"""
        # Create a session and make it appear expired
        session_id = await session_pool.get_session("sandbox123")
        session = session_pool.active_sessions[session_id]
        session.created_at = datetime.now() - timedelta(seconds=session_pool.session_timeout + 1)
        
        # Run cleanup
        await session_pool._remove_expired_sessions()
        
        assert session_id not in session_pool.active_sessions


class TestDaytonaService:
    """Test DaytonaService functionality"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, mock_daytona_config):
        """Test service initialization"""
        with patch('services.daytona_service.Daytona'):
            service = DaytonaService(mock_daytona_config)
            assert not service._initialized
            
            await service.initialize()
            assert service._initialized
            
            await service.cleanup()
            assert not service._initialized
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_daytona_config):
        """Test async context manager"""
        with patch('services.daytona_service.Daytona'):
            async with DaytonaService(mock_daytona_config) as service:
                assert service._initialized
            # Should be cleaned up after exiting context
    
    def test_backward_compatibility_methods(self, mock_daytona_config):
        """Test backward compatibility methods"""
        mock_daytona = Mock()
        mock_sandbox = Mock()
        mock_sandbox.process.exec.return_value = Mock(exit_code=0, result="test")
        mock_daytona.get.return_value = mock_sandbox
        
        with patch('services.daytona_service.Daytona', return_value=mock_daytona):
            service = DaytonaService(mock_daytona_config)
            
            # Test synchronous methods
            service.create_sandbox("test")
            mock_daytona.create.assert_called_once()
            
            service.get_sandbox("sandbox123")
            mock_daytona.get.assert_called_once()
            
            result = service.execute_command("sandbox123", "ls")
            assert result.exit_code == 0
    
    @pytest.mark.asyncio
    async def test_execute_command_stream_success(self, daytona_service):
        """Test successful command streaming"""
        # Mock the synchronous execution for fallback
        mock_response = Mock(exit_code=0, result="line1\nline2\nline3")
        daytona_service.daytona.get.return_value.process.exec.return_value = mock_response
        
        log_entries = []
        async for entry in daytona_service.execute_command_stream("sandbox123", "ls"):
            log_entries.append(entry)
        
        # Should have start, content lines, and end entries
        assert len(log_entries) >= 5  # start + 3 content lines + end
        assert log_entries[0].log_type == LogType.START
        assert log_entries[-1].log_type == LogType.END
        
        # Check content entries
        content_entries = [e for e in log_entries if e.log_type == LogType.STDOUT]
        assert len(content_entries) == 3
        assert content_entries[0].content == "line1"
        assert content_entries[1].content == "line2"
        assert content_entries[2].content == "line3"
    
    @pytest.mark.asyncio
    async def test_execute_command_stream_error(self, daytona_service):
        """Test command streaming with error"""
        # Mock execution failure
        daytona_service.daytona.get.side_effect = Exception("Sandbox not found")
        
        log_entries = []
        async for entry in daytona_service.execute_command_stream("sandbox123", "ls"):
            log_entries.append(entry)
        
        # Should have start and error entries
        assert len(log_entries) >= 2
        assert log_entries[0].log_type == LogType.START
        
        # Should have error entry
        error_entries = [e for e in log_entries if e.log_type == LogType.ERROR]
        assert len(error_entries) >= 1
        assert "Sandbox not found" in error_entries[0].content
    
    @pytest.mark.asyncio
    async def test_stream_command_sync_fallback(self, daytona_service):
        """Test synchronous fallback when async streaming fails"""
        mock_response = Mock(exit_code=0, result="fallback output")
        daytona_service.execute_command = Mock(return_value=mock_response)
        
        log_entries = []
        async for entry in daytona_service._stream_command_sync_fallback("sandbox123", "ls", "/"):
            log_entries.append(entry)
        
        assert len(log_entries) == 1
        assert log_entries[0].content == "fallback output"
        assert log_entries[0].metadata.get("fallback") is True
    
    @pytest.mark.asyncio
    async def test_stream_command_sync_fallback_error(self, daytona_service):
        """Test synchronous fallback with command error"""
        mock_response = Mock(exit_code=1, result="error output")
        daytona_service.execute_command = Mock(return_value=mock_response)
        
        log_entries = []
        async for entry in daytona_service._stream_command_sync_fallback("sandbox123", "ls", "/"):
            log_entries.append(entry)
        
        # Should have output and error entries
        assert len(log_entries) == 2
        assert log_entries[0].content == "error output"
        assert log_entries[1].log_type == LogType.ERROR
        assert "exit code: 1" in log_entries[1].content
    
    @pytest.mark.asyncio
    async def test_get_session_logs_stream(self, daytona_service):
        """Test streaming logs from existing session"""
        # Create a session first
        session_id = await daytona_service.session_pool.get_session("sandbox123")
        
        log_entries = []
        async for entry in daytona_service.get_session_logs_stream(session_id):
            log_entries.append(entry)
        
        assert len(log_entries) == 1
        assert session_id in log_entries[0].content
    
    @pytest.mark.asyncio
    async def test_get_session_logs_stream_not_found(self, daytona_service):
        """Test streaming logs from non-existent session"""
        with pytest.raises(SessionManagerError):
            async for entry in daytona_service.get_session_logs_stream("nonexistent"):
                pass
    
    @pytest.mark.asyncio
    async def test_list_active_sessions(self, daytona_service):
        """Test listing active sessions"""
        # Create some sessions
        session_id1 = await daytona_service.session_pool.get_session("sandbox1")
        session_id2 = await daytona_service.session_pool.get_session("sandbox2")
        
        sessions = await daytona_service.list_active_sessions()
        assert len(sessions) == 2
        
        session_ids = [s["session_id"] for s in sessions]
        assert session_id1 in session_ids
        assert session_id2 in session_ids
    
    @pytest.mark.asyncio
    async def test_get_session_info(self, daytona_service):
        """Test getting session info"""
        session_id = await daytona_service.session_pool.get_session("sandbox123", "ls")
        
        info = await daytona_service.get_session_info(session_id)
        assert info is not None
        assert info["session_id"] == session_id
        assert info["sandbox_id"] == "sandbox123"
        assert info["command"] == "ls"
        
        # Test non-existent session
        info = await daytona_service.get_session_info("nonexistent")
        assert info is None
    
    @pytest.mark.asyncio
    async def test_close_session(self, daytona_service):
        """Test manually closing a session"""
        session_id = await daytona_service.session_pool.get_session("sandbox123")
        assert session_id in daytona_service.session_pool.active_sessions
        
        await daytona_service.close_session(session_id)
        assert session_id not in daytona_service.session_pool.active_sessions


class TestLogModels:
    """Test log models functionality"""
    
    def test_log_entry_creation(self):
        """Test LogEntry creation"""
        timestamp = datetime.now()
        entry = LogEntry(
            timestamp=timestamp,
            log_type=LogType.STDOUT,
            content="test content",
            metadata={"key": "value"}
        )
        
        assert entry.timestamp == timestamp
        assert entry.log_type == LogType.STDOUT
        assert entry.content == "test content"
        assert entry.metadata == {"key": "value"}
    
    def test_log_entry_to_dict(self):
        """Test LogEntry to_dict conversion"""
        timestamp = datetime.now()
        entry = LogEntry(
            timestamp=timestamp,
            log_type=LogType.STDERR,
            content="error message"
        )
        
        result = entry.to_dict()
        assert result["timestamp"] == timestamp.isoformat()
        assert result["log_type"] == "stderr"
        assert result["content"] == "error message"
        assert result["metadata"] == {}
    
    def test_log_entry_from_daytona_log(self):
        """Test creating LogEntry from Daytona log format"""
        daytona_log = {
            "type": "stdout",
            "content": "test output",
            "timestamp": "2023-01-01T12:00:00Z",
            "metadata": {"source": "daytona"}
        }
        
        entry = LogEntry.from_daytona_log(daytona_log)
        assert entry.log_type == LogType.STDOUT
        assert entry.content == "test output"
        assert entry.metadata == {"source": "daytona"}
    
    def test_streaming_session_creation(self):
        """Test StreamingSession creation"""
        session = StreamingSession(
            session_id="test_session",
            sandbox_id="test_sandbox",
            command="ls"
        )
        
        assert session.session_id == "test_session"
        assert session.sandbox_id == "test_sandbox"
        assert session.command == "ls"
        assert session.status == "active"
        assert isinstance(session.created_at, datetime)
    
    def test_streaming_session_to_dict(self):
        """Test StreamingSession to_dict conversion"""
        session = StreamingSession(
            session_id="test_session",
            sandbox_id="test_sandbox"
        )
        
        result = session.to_dict()
        assert result["session_id"] == "test_session"
        assert result["sandbox_id"] == "test_sandbox"
        assert result["status"] == "active"
        assert "created_at" in result


@pytest.mark.asyncio
async def test_concurrent_sessions(mock_daytona_config):
    """Test handling multiple concurrent streaming sessions"""
    with patch('services.daytona_service.Daytona'):
        async with DaytonaService(mock_daytona_config, max_sessions=2) as service:
            # Mock successful execution
            mock_response = Mock(exit_code=0, result="output")
            service.daytona.get.return_value.process.exec.return_value = mock_response
            
            # Start multiple concurrent streams
            tasks = []
            for i in range(3):
                task = asyncio.create_task(
                    collect_stream_entries(service.execute_command_stream(f"sandbox{i}", "ls"))
                )
                tasks.append(task)
            
            # Wait for all to complete
            results = await asyncio.gather(*tasks)
            
            # All should complete successfully
            assert len(results) == 3
            for result in results:
                assert len(result) >= 2  # At least start and end entries


async def collect_stream_entries(stream: AsyncGenerator[LogEntry, None]) -> list:
    """Helper to collect all entries from a stream"""
    entries = []
    async for entry in stream:
        entries.append(entry)
    return entries


if __name__ == "__main__":
    pytest.main([__file__, "-v"])