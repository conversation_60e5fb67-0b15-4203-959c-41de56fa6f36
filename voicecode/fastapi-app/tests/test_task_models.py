"""Unit tests for Task models"""

import pytest
import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from database import Base, Task, TaskStatus, ChatMessage
from models.task_models import TaskCreate, TaskUpdate, TaskResponse, LogEntry


# Test database setup
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///:memory:"

@pytest.fixture
def test_engine():
    """Create test database engine"""
    engine = create_engine(
        SQLALCHEMY_TEST_DATABASE_URL, 
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(bind=engine)
    return engine

@pytest.fixture
def test_session(test_engine):
    """Create test database session"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()

@pytest.fixture
def sample_task_data():
    """Sample task data for testing"""
    return {
        "id": uuid.uuid4(),
        "sandbox_id": uuid.uuid4(),
        "user_id": uuid.uuid4(),
        "command": "npm run test",
        "status": TaskStatus.PENDING,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }

@pytest.fixture
def sample_chat_message(test_session):
    """Create a sample chat message for testing relationships"""
    chat_message = ChatMessage(
        sandbox_id=uuid.uuid4(),
        user_id=uuid.uuid4(),
        message_type="user",
        content="Run tests please"
    )
    test_session.add(chat_message)
    test_session.commit()
    return chat_message


class TestTaskSQLAlchemyModel:
    """Test SQLAlchemy Task model"""
    
    def test_task_creation_with_required_fields(self, test_session, sample_task_data):
        """Test creating a task with required fields only"""
        task = Task(**sample_task_data)
        test_session.add(task)
        test_session.commit()
        
        assert task.id is not None
        assert task.sandbox_id == sample_task_data["sandbox_id"]
        assert task.user_id == sample_task_data["user_id"]
        assert task.command == sample_task_data["command"]
        assert task.status == TaskStatus.PENDING
        assert task.created_at is not None
        assert task.updated_at is not None
    
    def test_task_creation_with_all_fields(self, test_session, sample_chat_message):
        """Test creating a task with all fields"""
        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(seconds=10)
        
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="npm run build",
            status=TaskStatus.COMPLETED,
            started_at=start_time,
            completed_at=end_time,
            exit_code=0,
            execution_time=10.0,
            logs=[{"level": "info", "message": "Build started"}],
            task_metadata={"priority": "high"},
            chat_message_id=sample_chat_message.id
        )
        test_session.add(task)
        test_session.commit()
        
        assert task.status == TaskStatus.COMPLETED
        assert task.started_at == start_time
        assert task.completed_at == end_time
        assert task.exit_code == 0
        assert task.execution_time == 10.0
        assert task.logs == [{"level": "info", "message": "Build started"}]
        assert task.task_metadata == {"priority": "high"}
        assert task.chat_message_id == sample_chat_message.id
    
    def test_task_default_values(self, test_session):
        """Test task default values"""
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command"
        )
        test_session.add(task)
        test_session.commit()
        
        assert task.status == TaskStatus.PENDING
        assert task.logs == []
        assert task.task_metadata == {}
        assert task.started_at is None
        assert task.completed_at is None
        assert task.exit_code is None
        assert task.execution_time is None
    
    def test_calculate_execution_time_completed(self, test_session):
        """Test calculate_execution_time for completed task"""
        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(seconds=30)
        
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            started_at=start_time,
            completed_at=end_time
        )
        
        execution_time = task.calculate_execution_time()
        assert execution_time == 30.0
    
    def test_calculate_execution_time_running(self, test_session):
        """Test calculate_execution_time for running task"""
        start_time = datetime.now(timezone.utc) - timedelta(seconds=15)
        
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.RUNNING,
            started_at=start_time
        )
        
        execution_time = task.calculate_execution_time()
        assert execution_time is not None
        assert execution_time >= 14  # Allow some variance for test execution time
    
    def test_calculate_execution_time_no_start(self, test_session):
        """Test calculate_execution_time for task with no start time"""
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command"
        )
        
        execution_time = task.calculate_execution_time()
        assert execution_time is None
    
    def test_is_active_property(self, test_session):
        """Test is_active property for different statuses"""
        task_pending = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.PENDING
        )
        assert task_pending.is_active is True
        
        task_running = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.RUNNING
        )
        assert task_running.is_active is True
        
        task_completed = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.COMPLETED
        )
        assert task_completed.is_active is False
        
        task_failed = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.FAILED
        )
        assert task_failed.is_active is False
    
    def test_add_log_entry(self, test_session):
        """Test adding log entries"""
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command"
        )
        
        # Add first log entry
        task.add_log_entry({"level": "info", "message": "Starting"})
        assert len(task.logs) == 1
        assert task.logs[0]["level"] == "info"
        assert task.logs[0]["message"] == "Starting"
        assert "timestamp" in task.logs[0]
        
        # Add second log entry
        task.add_log_entry({"level": "success", "message": "Completed"})
        assert len(task.logs) == 2
        assert task.logs[1]["level"] == "success"
        assert task.logs[1]["message"] == "Completed"
    
    def test_to_dict_method(self, test_session, sample_chat_message):
        """Test to_dict method"""
        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(seconds=5)
        
        task = Task(
            sandbox_id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            command="test command",
            status=TaskStatus.COMPLETED,
            started_at=start_time,
            completed_at=end_time,
            exit_code=0,
            logs=[{"level": "info", "message": "Test log"}],
            task_metadata={"test": "data"},
            chat_message_id=sample_chat_message.id
        )
        test_session.add(task)
        test_session.commit()
        
        task_dict = task.to_dict()
        
        assert "id" in task_dict
        assert "sandbox_id" in task_dict
        assert "user_id" in task_dict
        assert task_dict["command"] == "test command"
        assert task_dict["status"] == "completed"
        assert task_dict["exit_code"] == 0
        assert task_dict["execution_time"] == 5.0
        assert task_dict["logs"] == [{"level": "info", "message": "Test log"}]
        assert task_dict["metadata"] == {"test": "data"}
        assert task_dict["chat_message_id"] == str(sample_chat_message.id)


class TestTaskPydanticModels:
    """Test Pydantic Task models"""
    
    def test_task_create_validation(self):
        """Test TaskCreate model validation"""
        valid_data = {
            "sandbox_id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "command": "npm test"
        }
        
        task_create = TaskCreate(**valid_data)
        assert task_create.sandbox_id == valid_data["sandbox_id"]
        assert task_create.user_id == valid_data["user_id"]
        assert task_create.command == valid_data["command"]
        assert task_create.task_metadata == {}
    
    def test_task_create_with_metadata(self):
        """Test TaskCreate with metadata"""
        valid_data = {
            "sandbox_id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "command": "npm test",
            "task_metadata": {"priority": "high", "timeout": 300}
        }
        
        task_create = TaskCreate(**valid_data)
        assert task_create.task_metadata == {"priority": "high", "timeout": 300}
    
    def test_task_create_invalid_command(self):
        """Test TaskCreate with invalid command"""
        with pytest.raises(ValueError):
            TaskCreate(
                sandbox_id=uuid.uuid4(),
                user_id=uuid.uuid4(),
                command=""  # Empty command should fail
            )
    
    def test_task_update_validation(self):
        """Test TaskUpdate model validation"""
        update_data = {
            "status": TaskStatus.RUNNING,
            "started_at": datetime.now(timezone.utc),
            "exit_code": 0
        }
        
        task_update = TaskUpdate(**update_data)
        assert task_update.status == TaskStatus.RUNNING
        assert task_update.started_at == update_data["started_at"]
        assert task_update.exit_code == 0
    
    def test_task_update_invalid_exit_code(self):
        """Test TaskUpdate with invalid exit code"""
        with pytest.raises(ValueError):
            TaskUpdate(exit_code=300)  # Exit code > 255 should fail
        
        with pytest.raises(ValueError):
            TaskUpdate(exit_code=-1)   # Negative exit code should fail
    
    def test_task_update_negative_execution_time(self):
        """Test TaskUpdate with negative execution time"""
        with pytest.raises(ValueError):
            TaskUpdate(execution_time=-1.0)  # Negative execution time should fail
    
    def test_task_response_model(self):
        """Test TaskResponse model"""
        now = datetime.now(timezone.utc)
        response_data = {
            "id": uuid.uuid4(),
            "sandbox_id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "command": "npm test",
            "status": TaskStatus.COMPLETED,
            "created_at": now,
            "updated_at": now,
            "execution_time": 15.5,
            "logs": [{"level": "info", "message": "Test completed"}],
            "metadata": {"tests_passed": 10}
        }
        
        task_response = TaskResponse(**response_data)
        assert task_response.id == response_data["id"]
        assert task_response.command == response_data["command"]
        assert task_response.status == TaskStatus.COMPLETED
        assert task_response.execution_time == 15.5
        assert task_response.logs == [{"level": "info", "message": "Test completed"}]
        assert task_response.metadata == {"tests_passed": 10}
    
    def test_log_entry_model(self):
        """Test LogEntry model"""
        log_data = {
            "level": "error",
            "message": "Test failed",
            "timestamp": datetime.now(timezone.utc),
            "metadata": {"error_code": 1}
        }
        
        log_entry = LogEntry(**log_data)
        assert log_entry.level == "error"
        assert log_entry.message == "Test failed"
        assert log_entry.timestamp == log_data["timestamp"]
        assert log_entry.metadata == {"error_code": 1}


class TestTaskStatusEnum:
    """Test TaskStatus enum"""
    
    def test_task_status_values(self):
        """Test TaskStatus enum values"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.RUNNING.value == "running"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"
        assert TaskStatus.CANCELLED.value == "cancelled"
    
    def test_task_status_comparison(self):
        """Test TaskStatus comparison"""
        assert TaskStatus.PENDING == TaskStatus.PENDING
        assert TaskStatus.PENDING != TaskStatus.RUNNING
    
    def test_task_status_in_list(self):
        """Test TaskStatus in active status check"""
        active_statuses = [TaskStatus.PENDING, TaskStatus.RUNNING]
        
        assert TaskStatus.PENDING in active_statuses
        assert TaskStatus.RUNNING in active_statuses
        assert TaskStatus.COMPLETED not in active_statuses
        assert TaskStatus.FAILED not in active_statuses
        assert TaskStatus.CANCELLED not in active_statuses


if __name__ == "__main__":
    pytest.main([__file__, "-v"])