"""Backward compatibility tests for DaytonaService"""

import pytest
from unittest.mock import Mock, patch
import time

from services.daytona_service import DaytonaService
from daytona import DaytonaConfig


@pytest.fixture
def mock_daytona_config():
    """Mock Daytona configuration"""
    return DaytonaConfig(
        api_key="test_key",
        api_url="http://test.daytona.com",
        target="test",
        organization_id="test_org"
    )


@pytest.fixture
def mock_daytona():
    """Mock Daytona SDK with backward compatibility interface"""
    mock = Mock()
    
    # Mock sandbox creation
    mock_sandbox = Mock()
    mock_sandbox.id = "test_sandbox_123"
    mock_sandbox.state = "running"
    mock.create.return_value = mock_sandbox
    
    # Mock sandbox retrieval
    mock.get.return_value = mock_sandbox
    
    # Mock command execution
    mock_response = Mock()
    mock_response.exit_code = 0
    mock_response.result = "test command output"
    mock_sandbox.process.exec.return_value = mock_response
    
    return mock


@pytest.fixture
def daytona_service(mock_daytona_config, mock_daytona):
    """DaytonaService with mocked Daytona SDK"""
    with patch('services.daytona_service.Daytona', return_value=mock_daytona):
        service = DaytonaService(mock_daytona_config)
        return service


class TestBackwardCompatibility:
    """Test that enhanced DaytonaService maintains backward compatibility"""
    
    def test_create_sandbox_interface(self, daytona_service, mock_daytona):
        """Test that create_sandbox method works as before"""
        # Call the method as it would be called in existing code
        result = daytona_service.create_sandbox("test_params")
        
        # Verify it called the underlying Daytona SDK
        mock_daytona.create.assert_called_once_with("test_params")
        assert result.id == "test_sandbox_123"
    
    def test_get_sandbox_interface(self, daytona_service, mock_daytona):
        """Test that get_sandbox method works as before"""
        result = daytona_service.get_sandbox("sandbox_123")
        
        mock_daytona.get.assert_called_once_with("sandbox_123")
        assert result.id == "test_sandbox_123"
    
    def test_delete_sandbox_interface(self, daytona_service, mock_daytona):
        """Test that delete_sandbox method works as before"""
        daytona_service.delete_sandbox("sandbox_123")
        
        mock_daytona.delete.assert_called_once_with("sandbox_123")
    
    def test_execute_command_interface(self, daytona_service, mock_daytona):
        """Test that execute_command method works as before"""
        result = daytona_service.execute_command(
            "sandbox_123", 
            "ls -la", 
            "/home/<USER>"
        )
        
        # Verify the command was executed on the sandbox
        mock_sandbox = mock_daytona.get.return_value
        mock_sandbox.process.exec.assert_called_once_with("ls -la", "/home/<USER>")
        
        # Verify response format
        assert result.exit_code == 0
        assert result.result == "test command output"
    
    def test_execute_command_default_directory(self, daytona_service, mock_daytona):
        """Test that execute_command uses default working directory"""
        daytona_service.execute_command("sandbox_123", "pwd")
        
        mock_sandbox = mock_daytona.get.return_value
        mock_sandbox.process.exec.assert_called_once_with(
            "pwd", 
            "/home/<USER>/workspace/repository"
        )
    
    def test_synchronous_behavior_preserved(self, daytona_service):
        """Test that all backward compatibility methods are synchronous"""
        # These should all be synchronous calls (no async/await)
        start_time = time.time()
        
        daytona_service.create_sandbox("test")
        daytona_service.get_sandbox("test_123")
        daytona_service.execute_command("test_123", "echo test")
        daytona_service.delete_sandbox("test_123")
        
        # Should complete very quickly (mocked operations)
        execution_time = time.time() - start_time
        assert execution_time < 0.1  # Should be nearly instantaneous
    
    def test_error_handling_preserved(self, daytona_service, mock_daytona):
        """Test that error handling works as before"""
        # Mock an error in sandbox retrieval
        mock_daytona.get.side_effect = Exception("Sandbox not found")
        
        # Should propagate the error as before
        with pytest.raises(Exception, match="Sandbox not found"):
            daytona_service.execute_command("nonexistent", "ls")
    
    def test_response_format_unchanged(self, daytona_service, mock_daytona):
        """Test that command response format is unchanged"""
        # Set up specific response
        mock_response = Mock()
        mock_response.exit_code = 127
        mock_response.result = "command not found"
        mock_daytona.get.return_value.process.exec.return_value = mock_response
        
        result = daytona_service.execute_command("test", "nonexistent_command")
        
        # Response should have the same interface as before
        assert hasattr(result, 'exit_code')
        assert hasattr(result, 'result')
        assert result.exit_code == 127
        assert result.result == "command not found"
    
    def test_no_async_context_required(self, daytona_service):
        """Test that backward compatibility methods don't require async context"""
        # These should work without any async context managers or initialization
        
        # Should work immediately without calling initialize()
        assert not daytona_service._initialized
        
        # All methods should work
        daytona_service.create_sandbox("test")
        daytona_service.get_sandbox("test")
        daytona_service.execute_command("test", "echo hello")
        daytona_service.delete_sandbox("test")
        
        # Still should not be initialized (streaming features not used)
        assert not daytona_service._initialized


class TestExistingCodeCompatibility:
    """Test compatibility with existing code patterns"""
    
    def test_main_py_integration_pattern(self, mock_daytona_config):
        """Test the integration pattern used in main.py"""
        with patch('services.daytona_service.Daytona') as mock_daytona_class:
            mock_daytona = Mock()
            mock_sandbox = Mock()
            mock_sandbox.id = "test_id"
            mock_daytona.create.return_value = mock_sandbox
            mock_daytona_class.return_value = mock_daytona
            
            # This is how it's used in main.py
            service = DaytonaService(mock_daytona_config)
            
            # Should work without any changes to existing code
            sandbox = service.create_sandbox("params")
            assert sandbox.id == "test_id"
    
    def test_command_execution_pattern(self, daytona_service, mock_daytona):
        """Test the command execution pattern used in existing endpoints"""
        # This mimics the pattern in /api/sandbox/{sandbox_id}/execute endpoint
        
        # Mock response as used in main.py
        mock_response = Mock()
        mock_response.exit_code = 0
        mock_response.result = "total 4\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 test"
        mock_daytona.get.return_value.process.exec.return_value = mock_response
        
        # Execute as done in main.py
        sandbox_id = "test_sandbox"
        command = "ls -la"
        working_directory = "/home/<USER>/workspace/repository"
        
        response = daytona_service.execute_command(sandbox_id, command, working_directory)
        
        # Verify it works as expected in main.py
        assert response.exit_code == 0
        assert "test" in response.result
        
        # Verify the call was made correctly
        mock_daytona.get.assert_called_once_with(sandbox_id)
        mock_daytona.get.return_value.process.exec.assert_called_once_with(
            command, working_directory
        )
    
    def test_sandbox_management_pattern(self, daytona_service, mock_daytona):
        """Test sandbox management patterns used in existing code"""
        # Pattern used in sandbox creation endpoint
        mock_sandbox = Mock()
        mock_sandbox.id = "new_sandbox_123"
        mock_sandbox.state = "running"
        mock_daytona.create.return_value = mock_sandbox
        
        # Create sandbox
        result = daytona_service.create_sandbox("create_params")
        assert result.id == "new_sandbox_123"
        
        # Get sandbox (used in multiple endpoints)
        mock_daytona.get.return_value = mock_sandbox
        retrieved = daytona_service.get_sandbox("new_sandbox_123")
        assert retrieved.id == "new_sandbox_123"
        
        # Delete sandbox
        daytona_service.delete_sandbox("new_sandbox_123")
        mock_daytona.delete.assert_called_once_with("new_sandbox_123")
    
    def test_error_scenarios_unchanged(self, daytona_service, mock_daytona):
        """Test that error scenarios behave the same way"""
        # Test sandbox not found error
        mock_daytona.get.side_effect = Exception("Sandbox not found")
        
        with pytest.raises(Exception, match="Sandbox not found"):
            daytona_service.execute_command("nonexistent", "ls")
        
        # Test command execution error
        mock_daytona.get.side_effect = None
        mock_daytona.get.return_value.process.exec.side_effect = Exception("Command failed")
        
        with pytest.raises(Exception, match="Command failed"):
            daytona_service.execute_command("test", "failing_command")


class TestPerformanceCompatibility:
    """Test that performance characteristics are maintained"""
    
    def test_no_performance_regression(self, daytona_service, mock_daytona):
        """Test that synchronous operations have no performance regression"""
        # Mock very fast responses
        mock_response = Mock()
        mock_response.exit_code = 0
        mock_response.result = "fast response"
        mock_daytona.get.return_value.process.exec.return_value = mock_response
        
        # Time multiple operations
        start_time = time.time()
        
        for i in range(10):
            daytona_service.execute_command("test", f"command_{i}")
        
        execution_time = time.time() - start_time
        
        # Should be very fast with mocked operations (< 10ms per operation)
        assert execution_time < 0.1
        
        # Verify all calls were made
        assert mock_daytona.get.call_count == 10
    
    def test_memory_usage_unchanged(self, daytona_service):
        """Test that memory usage patterns are unchanged for sync operations"""
        import gc
        import sys
        
        # Get initial reference count
        initial_refs = sys.getrefcount(daytona_service)
        
        # Perform operations that should not leak references
        for i in range(5):
            daytona_service.create_sandbox(f"test_{i}")
            daytona_service.get_sandbox(f"test_{i}")
            daytona_service.delete_sandbox(f"test_{i}")
        
        # Force garbage collection
        gc.collect()
        
        # Reference count should be unchanged
        final_refs = sys.getrefcount(daytona_service)
        assert final_refs == initial_refs


class TestInterfaceStability:
    """Test that the interface remains stable"""
    
    def test_method_signatures_unchanged(self, daytona_service):
        """Test that method signatures are unchanged"""
        import inspect
        
        # Check create_sandbox signature
        sig = inspect.signature(daytona_service.create_sandbox)
        assert len(sig.parameters) >= 1  # Should accept *args, **kwargs
        
        # Check get_sandbox signature
        sig = inspect.signature(daytona_service.get_sandbox)
        params = list(sig.parameters.keys())
        assert 'sandbox_id' in params
        
        # Check execute_command signature
        sig = inspect.signature(daytona_service.execute_command)
        params = list(sig.parameters.keys())
        assert 'sandbox_id' in params
        assert 'command' in params
        assert 'working_directory' in params
        
        # Check delete_sandbox signature
        sig = inspect.signature(daytona_service.delete_sandbox)
        params = list(sig.parameters.keys())
        assert 'sandbox_id' in params
    
    def test_return_types_unchanged(self, daytona_service, mock_daytona):
        """Test that return types are unchanged"""
        # Mock return values
        mock_sandbox = Mock()
        mock_response = Mock()
        mock_response.exit_code = 0
        mock_response.result = "output"
        
        mock_daytona.create.return_value = mock_sandbox
        mock_daytona.get.return_value = mock_sandbox
        mock_sandbox.process.exec.return_value = mock_response
        
        # Test return types
        create_result = daytona_service.create_sandbox("test")
        assert create_result is mock_sandbox
        
        get_result = daytona_service.get_sandbox("test")
        assert get_result is mock_sandbox
        
        exec_result = daytona_service.execute_command("test", "cmd")
        assert exec_result is mock_response
        assert hasattr(exec_result, 'exit_code')
        assert hasattr(exec_result, 'result')
        
        # delete_sandbox should return None (or whatever Daytona SDK returns)
        delete_result = daytona_service.delete_sandbox("test")
        # Just ensure it doesn't raise an exception


if __name__ == "__main__":
    pytest.main([__file__, "-v"])