"""Integration tests for TaskService with real database and Redis"""

import pytest
import asyncio
import redis
from uuid import uuid4
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from database import Base, Task, TaskStatus, test_database_connection
from services.task_service import TaskService
from services.exceptions import (
    TaskAlreadyRunningError,
    TaskNotFoundError,
    UnauthorizedTaskAccessError
)


@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine"""
    # Use test database URL or in-memory SQLite for testing
    test_db_url = "sqlite:///:memory:"
    engine = create_engine(test_db_url, echo=False)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def test_db_session(test_engine):
    """Create test database session"""
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestSessionLocal()
    
    yield session
    
    # Cleanup: remove all data after each test
    session.rollback()
    for table in reversed(Base.metadata.sorted_tables):
        session.execute(table.delete())
    session.commit()
    session.close()


@pytest.fixture
def test_redis_client():
    """Create test Redis client (or mock if Redis not available)"""
    try:
        # Try to connect to Redis
        redis_client = redis.Redis(host='localhost', port=6379, db=15, decode_responses=True)
        redis_client.ping()  # Test connection
        
        yield redis_client
        
        # Cleanup: flush test database
        redis_client.flushdb()
        
    except (redis.ConnectionError, redis.TimeoutError):
        # Redis not available, use None (TaskService should handle this gracefully)
        yield None


@pytest.fixture
def task_service(test_db_session, test_redis_client):
    """TaskService instance with real database and Redis"""
    return TaskService(
        db_session=test_db_session,
        redis_client=test_redis_client,
        daytona_service=None  # Not needed for these tests
    )


class TestTaskServiceIntegration:
    """Integration tests for TaskService"""
    
    @pytest.mark.asyncio
    async def test_create_and_get_task(self, task_service):
        """Test creating and retrieving a task"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "echo 'integration test'"
        metadata = {"integration": True, "test_id": "test_001"}
        
        # Create task
        created_task = await task_service.create_task(
            sandbox_id, user_id, command, metadata
        )
        
        assert created_task.id is not None
        assert created_task.sandbox_id == sandbox_id
        assert created_task.user_id == user_id
        assert created_task.command == command
        assert created_task.status == TaskStatus.PENDING
        assert created_task.task_metadata == metadata
        assert created_task.created_at is not None
        
        # Get task back
        retrieved_task = await task_service.get_task(created_task.id, user_id)
        
        assert retrieved_task.id == created_task.id
        assert retrieved_task.command == command
        assert retrieved_task.status == TaskStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_task_lifecycle(self, task_service):
        """Test complete task lifecycle from creation to completion"""
        sandbox_id = uuid4()
        user_id = uuid4()
        command = "ls -la"
        
        # 1. Create task
        task = await task_service.create_task(sandbox_id, user_id, command)
        assert task.status == TaskStatus.PENDING
        assert task.started_at is None
        assert task.completed_at is None
        
        # 2. Start task
        started_task = await task_service.start_task(task.id)
        assert started_task.status == TaskStatus.RUNNING
        assert started_task.started_at is not None
        assert started_task.completed_at is None
        
        # 3. Complete task
        exit_code = 0
        final_logs = ["Directory listing completed", "Found 5 files"]
        completed_task = await task_service.complete_task(
            task.id, exit_code, final_logs
        )
        
        assert completed_task.status == TaskStatus.COMPLETED
        assert completed_task.exit_code == exit_code
        assert completed_task.completed_at is not None
        assert completed_task.execution_time is not None
        assert completed_task.execution_time > 0
        
        # Verify logs were added
        assert len(completed_task.logs) >= len(final_logs)
    
    @pytest.mark.asyncio
    async def test_concurrent_task_creation(self, task_service):
        """Test one-task-at-a-time enforcement"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Create first task
        task1 = await task_service.create_task(
            sandbox_id, user_id, "first command"
        )
        assert task1.status == TaskStatus.PENDING
        
        # Try to create second task for same sandbox - should fail
        with pytest.raises(TaskAlreadyRunningError) as exc_info:
            await task_service.create_task(
                sandbox_id, user_id, "second command"
            )
        
        assert str(sandbox_id) in str(exc_info.value)
        assert str(task1.id) in str(exc_info.value)
        
        # Complete first task
        await task_service.complete_task(task1.id, 0)
        
        # Now should be able to create second task
        task2 = await task_service.create_task(
            sandbox_id, user_id, "second command"
        )
        assert task2.status == TaskStatus.PENDING
        assert task2.id != task1.id
    
    @pytest.mark.asyncio
    async def test_get_current_task(self, task_service):
        """Test getting current active task"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # No active task initially
        current_task = await task_service.get_current_task(sandbox_id, user_id)
        assert current_task is None
        
        # Create pending task
        task = await task_service.create_task(
            sandbox_id, user_id, "test command"
        )
        
        # Should find the pending task
        current_task = await task_service.get_current_task(sandbox_id, user_id)
        assert current_task is not None
        assert current_task.id == task.id
        assert current_task.status == TaskStatus.PENDING
        
        # Start the task
        await task_service.start_task(task.id)
        
        # Should still find the running task
        current_task = await task_service.get_current_task(sandbox_id, user_id)
        assert current_task is not None
        assert current_task.id == task.id
        assert current_task.status == TaskStatus.RUNNING
        
        # Complete the task
        await task_service.complete_task(task.id, 0)
        
        # Should not find any active task
        current_task = await task_service.get_current_task(sandbox_id, user_id)
        assert current_task is None
    
    @pytest.mark.asyncio
    async def test_cancel_task(self, task_service):
        """Test task cancellation"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Create and start task
        task = await task_service.create_task(
            sandbox_id, user_id, "long running command"
        )
        await task_service.start_task(task.id)
        
        # Cancel the task
        result = await task_service.cancel_task(task.id, user_id)
        assert result is True
        
        # Verify task was cancelled
        cancelled_task = await task_service.get_task(task.id, user_id)
        assert cancelled_task.status == TaskStatus.CANCELLED
        assert cancelled_task.completed_at is not None
        assert cancelled_task.execution_time is not None
        
        # Should be able to create new task now
        new_task = await task_service.create_task(
            sandbox_id, user_id, "new command"
        )
        assert new_task.status == TaskStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_unauthorized_access(self, task_service):
        """Test unauthorized task access"""
        sandbox_id = uuid4()
        user_id = uuid4()
        other_user_id = uuid4()
        
        # Create task as first user
        task = await task_service.create_task(
            sandbox_id, user_id, "private command"
        )
        
        # Try to access as different user - should fail
        with pytest.raises(UnauthorizedTaskAccessError):
            await task_service.get_task(task.id, other_user_id)
        
        with pytest.raises(UnauthorizedTaskAccessError):
            await task_service.cancel_task(task.id, other_user_id)
    
    @pytest.mark.asyncio
    async def test_list_tasks(self, task_service):
        """Test listing tasks with filters"""
        sandbox_id1 = uuid4()
        sandbox_id2 = uuid4()
        user_id = uuid4()
        
        # Create multiple tasks
        task1 = await task_service.create_task(
            sandbox_id1, user_id, "command 1"
        )
        task2 = await task_service.create_task(
            sandbox_id2, user_id, "command 2"
        )
        
        # Complete one task
        await task_service.start_task(task1.id)
        await task_service.complete_task(task1.id, 0)
        
        # List all tasks for user
        all_tasks = await task_service.list_tasks(user_id=user_id)
        assert len(all_tasks) == 2
        
        # List tasks for specific sandbox
        sandbox1_tasks = await task_service.list_tasks(sandbox_id=sandbox_id1)
        assert len(sandbox1_tasks) == 1
        assert sandbox1_tasks[0].id == task1.id
        
        # List pending tasks only
        pending_tasks = await task_service.list_tasks(
            user_id=user_id, status=TaskStatus.PENDING
        )
        assert len(pending_tasks) == 1
        assert pending_tasks[0].id == task2.id
        
        # List completed tasks only
        completed_tasks = await task_service.list_tasks(
            user_id=user_id, status=TaskStatus.COMPLETED
        )
        assert len(completed_tasks) == 1
        assert completed_tasks[0].id == task1.id
    
    @pytest.mark.asyncio
    async def test_cleanup_old_tasks(self, task_service, test_db_session):
        """Test cleanup of old tasks"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Create tasks
        old_task = await task_service.create_task(
            sandbox_id, user_id, "old command"
        )
        await task_service.complete_task(old_task.id, 0)
        
        recent_task = await task_service.create_task(
            sandbox_id, user_id, "recent command"
        )
        await task_service.complete_task(recent_task.id, 0)
        
        # Manually set old task creation time to simulate old task
        old_date = datetime.now(timezone.utc) - timedelta(days=10)
        test_db_session.query(Task).filter(Task.id == old_task.id).update({
            'created_at': old_date
        })
        test_db_session.commit()
        
        # Cleanup tasks older than 7 days
        deleted_count = await task_service.cleanup_old_tasks(days_old=7)
        assert deleted_count == 1
        
        # Verify old task was deleted
        with pytest.raises(TaskNotFoundError):
            await task_service.get_task(old_task.id, user_id)
        
        # Verify recent task still exists
        existing_task = await task_service.get_task(recent_task.id, user_id)
        assert existing_task.id == recent_task.id
    
    @pytest.mark.asyncio
    async def test_task_stats(self, task_service):
        """Test task statistics generation"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Create tasks with different statuses
        pending_task = await task_service.create_task(
            sandbox_id, user_id, "pending command"
        )
        
        running_task = await task_service.create_task(
            uuid4(), user_id, "running command"  # Different sandbox
        )
        await task_service.start_task(running_task.id)
        
        completed_task = await task_service.create_task(
            uuid4(), user_id, "completed command"  # Different sandbox
        )
        await task_service.start_task(completed_task.id)
        await task_service.complete_task(completed_task.id, 0)
        
        failed_task = await task_service.create_task(
            uuid4(), user_id, "failed command"  # Different sandbox
        )
        await task_service.start_task(failed_task.id)
        await task_service.complete_task(failed_task.id, 1)  # Exit code 1 = failed
        
        # Get overall stats
        stats = await task_service.get_task_stats()
        
        assert stats['total_count'] == 4
        assert stats['pending_count'] == 1
        assert stats['running_count'] == 1
        assert stats['completed_count'] == 1
        assert stats['failed_count'] == 1
        assert stats['cancelled_count'] == 0
        assert stats['avg_execution_time'] is not None
        
        # Get stats for specific sandbox
        sandbox_stats = await task_service.get_task_stats(sandbox_id)
        assert sandbox_stats['total_count'] == 1
        assert sandbox_stats['pending_count'] == 1
    
    @pytest.mark.asyncio
    async def test_task_with_logs(self, task_service):
        """Test task log management"""
        sandbox_id = uuid4()
        user_id = uuid4()
        
        # Create and start task
        task = await task_service.create_task(
            sandbox_id, user_id, "command with logs"
        )
        await task_service.start_task(task.id)
        
        # Get the task and add some log entries
        db_task = await task_service.get_task(task.id, user_id)
        
        # Add custom log entries
        db_task.add_log_entry({
            "level": "info",
            "message": "Starting process"
        })
        db_task.add_log_entry({
            "level": "debug", 
            "message": "Processing file 1"
        })
        db_task.add_log_entry({
            "level": "warning",
            "message": "File not found, skipping"
        })
        
        # Commit the log changes
        task_service.db.commit()
        
        # Complete task with final logs
        final_logs = ["Process completed", "Results saved"]
        completed_task = await task_service.complete_task(
            task.id, 0, final_logs
        )
        
        # Verify all logs are present
        assert len(completed_task.logs) >= 6  # 3 custom + 2 final + start/complete logs
        
        # Check log structure
        for log_entry in completed_task.logs:
            assert "timestamp" in log_entry
            assert "message" in log_entry
    
    @pytest.mark.asyncio
    async def test_redis_locking_with_real_redis(self, task_service, test_redis_client):
        """Test Redis locking functionality (only if Redis is available)"""
        if test_redis_client is None:
            pytest.skip("Redis not available for testing")
        
        sandbox_id = uuid4()
        lock_key = f"sandbox:{sandbox_id}"
        
        # Test successful lock acquisition
        with task_service._redis_lock(lock_key) as acquired:
            assert acquired is True
            
            # Lock should be present in Redis
            lock_name = f"{task_service._lock_prefix}:{lock_key}"
            assert test_redis_client.exists(lock_name) == 1
        
        # Lock should be released after context exits
        assert test_redis_client.exists(lock_name) == 0
    
    @pytest.mark.asyncio
    async def test_multiple_users_same_sandbox(self, task_service):
        """Test multiple users can't create tasks for same sandbox simultaneously"""
        sandbox_id = uuid4()
        user_id1 = uuid4()
        user_id2 = uuid4()
        
        # User 1 creates task
        task1 = await task_service.create_task(
            sandbox_id, user_id1, "user 1 command"
        )
        
        # User 2 tries to create task for same sandbox - should fail
        with pytest.raises(TaskAlreadyRunningError):
            await task_service.create_task(
                sandbox_id, user_id2, "user 2 command"
            )
        
        # User 1 completes their task
        await task_service.complete_task(task1.id, 0)
        
        # Now User 2 can create task
        task2 = await task_service.create_task(
            sandbox_id, user_id2, "user 2 command"
        )
        assert task2.user_id == user_id2


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])