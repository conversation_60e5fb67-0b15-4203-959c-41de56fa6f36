"""Integration tests for DaytonaService streaming functionality"""

import pytest
import asyncio
import os
import time
from typing import List

from services.daytona_service import DaytonaService, DaytonaStreamingError
from models.log_models import LogEntry, LogType
from daytona import DaytonaConfig


# Skip integration tests if Daytona credentials are not available
pytestmark = pytest.mark.skipif(
    not all([
        os.getenv("DAYTONA_API_KEY"),
        os.getenv("DAYTONA_API_URL")
    ]),
    reason="Daytona credentials not available for integration tests"
)


@pytest.fixture(scope="module")
def daytona_config():
    """Real Daytona configuration for integration tests"""
    return DaytonaConfig(
        api_key=os.getenv("DAYTONA_API_KEY"),
        api_url=os.getenv("DAYTONA_API_URL"),
        target=os.getenv("DAYTONA_TARGET", "us"),
        organization_id=os.getenv("DAYTONA_ORG_ID")
    )


@pytest.fixture(scope="module")
async def daytona_service(daytona_config):
    """DaytonaService instance for integration tests"""
    async with DaytonaService(daytona_config) as service:
        yield service


@pytest.fixture(scope="module")
def test_sandbox_id():
    """
    Test sandbox ID - should be set in environment or created for testing
    This should be an existing sandbox with basic tools available
    """
    sandbox_id = os.getenv("TEST_SANDBOX_ID")
    if not sandbox_id:
        pytest.skip("TEST_SANDBOX_ID not set - cannot run integration tests")
    return sandbox_id


class TestDaytonaServiceIntegration:
    """Integration tests with real Daytona sandbox"""
    
    @pytest.mark.asyncio
    async def test_simple_command_streaming(self, daytona_service, test_sandbox_id):
        """Test streaming a simple command like 'echo'"""
        log_entries = []
        
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, "echo 'Hello World'"):
            log_entries.append(entry)
        
        # Verify we got expected entries
        assert len(log_entries) >= 3  # start, content, end
        
        # Check start entry
        start_entries = [e for e in log_entries if e.log_type == LogType.START]
        assert len(start_entries) == 1
        assert "echo 'Hello World'" in start_entries[0].content
        
        # Check end entry
        end_entries = [e for e in log_entries if e.log_type == LogType.END]
        assert len(end_entries) == 1
        
        # Check output content
        stdout_entries = [e for e in log_entries if e.log_type == LogType.STDOUT]
        assert len(stdout_entries) >= 1
        assert any("Hello World" in entry.content for entry in stdout_entries)
    
    @pytest.mark.asyncio
    async def test_multi_line_output_streaming(self, daytona_service, test_sandbox_id):
        """Test streaming command with multi-line output"""
        command = "printf 'Line 1\\nLine 2\\nLine 3\\n'"
        log_entries = []
        
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            log_entries.append(entry)
        
        # Check that we got multiple content lines
        stdout_entries = [e for e in log_entries if e.log_type == LogType.STDOUT]
        assert len(stdout_entries) >= 3
        
        # Verify content
        content_lines = [entry.content for entry in stdout_entries]
        assert "Line 1" in " ".join(content_lines)
        assert "Line 2" in " ".join(content_lines)
        assert "Line 3" in " ".join(content_lines)
    
    @pytest.mark.asyncio
    async def test_long_running_command_streaming(self, daytona_service, test_sandbox_id):
        """Test streaming a command that takes some time"""
        # Command that outputs something every second for 3 seconds
        command = "for i in 1 2 3; do echo \"Output $i\"; sleep 1; done"
        log_entries = []
        start_time = time.time()
        
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            log_entries.append(entry)
            entry.received_at = time.time()  # Track when we received it
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should take at least 3 seconds
        assert execution_time >= 2.5  # Allow some tolerance
        
        # Should have progressive output
        stdout_entries = [e for e in log_entries if e.log_type == LogType.STDOUT]
        assert len(stdout_entries) >= 3
        
        # Verify progressive timing (entries should be received over time)
        if len(stdout_entries) >= 2:
            time_diff = stdout_entries[-1].received_at - stdout_entries[0].received_at
            assert time_diff >= 1.0  # Should span at least 1 second
    
    @pytest.mark.asyncio
    async def test_command_with_error_streaming(self, daytona_service, test_sandbox_id):
        """Test streaming a command that produces an error"""
        command = "ls /nonexistent/directory 2>&1 || echo 'Command failed'"
        log_entries = []
        
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            log_entries.append(entry)
        
        # Should have output indicating error or failure
        content_entries = [e for e in log_entries if e.log_type in [LogType.STDOUT, LogType.STDERR]]
        assert len(content_entries) >= 1
        
        all_content = " ".join(entry.content for entry in content_entries)
        assert any(word in all_content.lower() for word in ["error", "no such", "failed", "cannot"])
    
    @pytest.mark.asyncio 
    async def test_concurrent_streaming_sessions(self, daytona_service, test_sandbox_id):
        """Test multiple concurrent streaming sessions"""
        
        async def run_command(cmd_suffix):
            entries = []
            async for entry in daytona_service.execute_command_stream(
                test_sandbox_id, 
                f"echo 'Command {cmd_suffix}' && sleep 1 && echo 'Done {cmd_suffix}'"
            ):
                entries.append(entry)
            return entries
        
        # Run 3 concurrent commands
        tasks = [
            asyncio.create_task(run_command(i))
            for i in range(1, 4)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Should complete concurrently (not sequentially)
        # If sequential, would take ~6 seconds, concurrent should be ~2-3 seconds
        execution_time = end_time - start_time
        assert execution_time < 5  # Should be much faster than sequential
        
        # All commands should complete successfully
        assert len(results) == 3
        for result in results:
            assert len(result) >= 3  # start, content, end
            stdout_entries = [e for e in result if e.log_type == LogType.STDOUT]
            assert len(stdout_entries) >= 2  # Should have "Command X" and "Done X"
    
    @pytest.mark.asyncio
    async def test_session_management(self, daytona_service, test_sandbox_id):
        """Test session creation and management"""
        initial_sessions = await daytona_service.list_active_sessions()
        initial_count = len(initial_sessions)
        
        # Start a streaming command
        stream = daytona_service.execute_command_stream(test_sandbox_id, "echo 'test' && sleep 2")
        
        # Should create a new session
        sessions_during = await daytona_service.list_active_sessions()
        assert len(sessions_during) == initial_count + 1
        
        # Complete the stream
        entries = []
        async for entry in stream:
            entries.append(entry)
        
        # Allow some time for cleanup
        await asyncio.sleep(0.5)
        
        # Session should still exist but marked as completed
        sessions_after = await daytona_service.list_active_sessions()
        # Note: Completed sessions might still be in the list temporarily
        # The actual cleanup happens in background task
    
    @pytest.mark.asyncio
    async def test_session_info_retrieval(self, daytona_service, test_sandbox_id):
        """Test retrieving session information"""
        # Start streaming to create a session
        stream = daytona_service.execute_command_stream(test_sandbox_id, "echo 'session test'")
        
        # Get first entry to ensure session is created
        first_entry = await stream.__anext__()
        assert first_entry.log_type == LogType.START
        
        session_id = first_entry.metadata.get("session_id")
        assert session_id is not None
        
        # Get session info
        session_info = await daytona_service.get_session_info(session_id)
        assert session_info is not None
        assert session_info["session_id"] == session_id
        assert session_info["sandbox_id"] == test_sandbox_id
        assert session_info["command"] == "echo 'session test'"
        
        # Complete the stream
        async for entry in stream:
            pass
    
    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, daytona_service, test_sandbox_id):
        """Test performance benchmarks for streaming"""
        # Test streaming overhead
        command = "echo 'performance test'"
        
        # Time streaming execution
        start_time = time.time()
        entries = []
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            entries.append(entry)
        streaming_time = time.time() - start_time
        
        # Time synchronous execution
        start_time = time.time()
        sync_result = daytona_service.execute_command(test_sandbox_id, command)
        sync_time = time.time() - start_time
        
        # Streaming should not have excessive overhead (allow 2x overhead max)
        assert streaming_time < sync_time * 3
        
        # Verify we got the expected content
        stdout_entries = [e for e in entries if e.log_type == LogType.STDOUT]
        assert len(stdout_entries) >= 1
        assert "performance test" in stdout_entries[0].content
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, daytona_service, test_sandbox_id):
        """Test memory usage remains stable during extended streaming"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Run multiple streaming operations
        for i in range(10):
            entries = []
            async for entry in daytona_service.execute_command_stream(
                test_sandbox_id, 
                f"echo 'Memory test {i}' && echo 'Line 2' && echo 'Line 3'"
            ):
                entries.append(entry)
            
            # Force garbage collection
            gc.collect()
            
            # Check memory usage
            current_memory = process.memory_info().rss
            memory_increase = current_memory - initial_memory
            
            # Memory increase should be reasonable (less than 50MB)
            assert memory_increase < 50 * 1024 * 1024, f"Memory increase too large: {memory_increase / 1024 / 1024:.2f}MB"
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, daytona_service, test_sandbox_id):
        """Test error recovery and fallback mechanisms"""
        # Test with a command that might trigger async streaming errors
        command = "echo 'Before error'; false; echo 'After error'"
        
        log_entries = []
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            log_entries.append(entry)
        
        # Should handle the error gracefully and complete
        assert len(log_entries) >= 2  # At least start and end
        
        # Should have start and end events
        start_entries = [e for e in log_entries if e.log_type == LogType.START]
        end_entries = [e for e in log_entries if e.log_type == LogType.END]
        assert len(start_entries) == 1
        assert len(end_entries) >= 1  # Could have error end or normal end


@pytest.mark.asyncio
async def test_real_development_workflow(daytona_service, test_sandbox_id):
    """Test a realistic development workflow with streaming"""
    # Simulate a typical development command sequence
    commands = [
        "pwd",
        "ls -la", 
        "echo 'Hello from Daytona streaming!'",
        "date",
        "whoami"
    ]
    
    all_results = []
    
    for command in commands:
        entries = []
        async for entry in daytona_service.execute_command_stream(test_sandbox_id, command):
            entries.append(entry)
        all_results.append((command, entries))
        
        # Small delay between commands
        await asyncio.sleep(0.1)
    
    # Verify all commands completed
    assert len(all_results) == len(commands)
    
    for command, entries in all_results:
        # Each command should have start and end
        start_entries = [e for e in entries if e.log_type == LogType.START]
        end_entries = [e for e in entries if e.log_type == LogType.END]
        assert len(start_entries) == 1
        assert len(end_entries) >= 1
        
        # Should have some output (unless command produces no output)
        if command != "pwd":  # pwd might not produce visible output in all environments
            content_entries = [e for e in entries if e.log_type == LogType.STDOUT]
            assert len(content_entries) >= 0  # At least should not error


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])