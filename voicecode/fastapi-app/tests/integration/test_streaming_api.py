"""Integration tests for streaming API endpoints"""

import pytest
import asyncio
import json
import time
from uuid import uuid4
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from main import app, get_current_user, get_task_service, settings
from database import Base, Task, TaskStatus
from services.task_service import TaskService
from models.streaming_models import TaskExecutionRequest


# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_streaming.db"
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="function")
def test_db():
    """Create test database for each test"""
    Base.metadata.create_all(bind=test_engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_auth_user():
    """Mock authenticated user for integration tests"""
    return {
        "user": {
            "user": {"id": 12345, "login": "testuser"},
            "github_token": "ghp_test_token",
            "auth_type": "oauth"
        }
    }


@pytest.fixture
def mock_sandbox_data():
    """Mock sandbox data in Redis"""
    return {
        "sandbox_id": str(uuid4()),
        "user_id": 12345,
        "repo_url": "https://github.com/test/repo",
        "status": "running"
    }


class TestStreamingAPIIntegration:
    """Integration tests for streaming API with real dependencies"""
    
    @patch('main.redis_client')
    @patch.object(app, 'dependency_overrides', {})
    def test_streaming_endpoint_registration(self, mock_redis, client):
        """Test that streaming endpoints are properly registered"""
        # Check if streaming router is registered by testing route availability
        sandbox_id = str(uuid4())
        
        # Should get 401 without auth, not 404 (which would mean route not found)
        response = client.post(f"/api/sandbox/{sandbox_id}/tasks/stream")
        assert response.status_code == 401  # Unauthorized, not Not Found
    
    @patch('main.redis_client')
    @patch('main.daytona')
    def test_streaming_with_auth_and_valid_sandbox(
        self, mock_daytona, mock_redis, client, mock_auth_user, mock_sandbox_data
    ):
        """Test streaming endpoint with authentication and valid sandbox"""
        
        # Override authentication dependency
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        # Mock Redis sandbox data
        mock_redis.get.return_value = json.dumps(mock_sandbox_data)
        
        # Mock Daytona command execution
        mock_command_response = Mock()
        mock_command_response.exit_code = 0
        mock_command_response.result = "Hello, World!\nTest output line 2"
        mock_daytona.execute_command.return_value = mock_command_response
        
        sandbox_id = mock_sandbox_data["sandbox_id"]
        
        try:
            # Make streaming request
            with client.stream(
                "POST", 
                f"/api/sandbox/{sandbox_id}/tasks/stream",
                json={"command": "echo 'Hello, World!'"}
            ) as response:
                
                assert response.status_code == 200
                assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
                
                # Read streaming content
                content_lines = []
                for line in response.iter_lines():
                    if line:
                        content_lines.append(line)
                        # Stop after reasonable amount of content
                        if len(content_lines) > 10:
                            break
                
                # Verify SSE format
                sse_messages = []
                for line in content_lines:
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix
                        if data != "[DONE]":
                            try:
                                message = json.loads(data)
                                sse_messages.append(message)
                            except json.JSONDecodeError:
                                pass  # Skip invalid JSON
                
                # Should have at least task_start and task_complete messages
                assert len(sse_messages) >= 2
                
                # Check message structure
                for message in sse_messages:
                    assert "id" in message
                    assert "role" in message
                    assert "content" in message
                    assert "metadata" in message
                    assert message["role"] == "assistant"
                
                # Check for specific message types
                message_types = [msg["metadata"].get("type") for msg in sse_messages]
                assert "task_start" in message_types
                assert "task_complete" in message_types or "task_log" in message_types
        
        finally:
            # Clean up dependency override
            app.dependency_overrides.clear()
    
    @patch('main.redis_client')
    def test_streaming_unauthorized_sandbox_access(
        self, mock_redis, client, mock_auth_user
    ):
        """Test streaming with unauthorized sandbox access"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        # Mock sandbox owned by different user
        unauthorized_sandbox = {
            "sandbox_id": str(uuid4()),
            "user_id": 99999,  # Different user ID
            "status": "running"
        }
        mock_redis.get.return_value = json.dumps(unauthorized_sandbox)
        
        try:
            response = client.post(
                f"/api/sandbox/{unauthorized_sandbox['sandbox_id']}/tasks/stream",
                json={"command": "echo test"}
            )
            
            assert response.status_code == 403
            assert "Not authorized to access this sandbox" in response.json()["detail"]
        
        finally:
            app.dependency_overrides.clear()
    
    @patch('main.redis_client')
    def test_streaming_sandbox_not_found(self, mock_redis, client, mock_auth_user):
        """Test streaming with non-existent sandbox"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        mock_redis.get.return_value = None  # Sandbox not found
        
        try:
            sandbox_id = str(uuid4())
            response = client.post(
                f"/api/sandbox/{sandbox_id}/tasks/stream",
                json={"command": "echo test"}
            )
            
            assert response.status_code == 404
            assert "Sandbox not found" in response.json()["detail"]
        
        finally:
            app.dependency_overrides.clear()
    
    def test_current_task_status_endpoint(self, client, mock_auth_user):
        """Test getting current task status"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        try:
            sandbox_id = str(uuid4())
            response = client.get(f"/api/sandbox/{sandbox_id}/tasks/current")
            
            # Should return 200 with no active task
            assert response.status_code == 200
            data = response.json()
            assert "data" in data
            assert data["data"]["task"] is None
            assert "No active task" in data["message"]
        
        finally:
            app.dependency_overrides.clear()
    
    def test_cancel_task_endpoint(self, client, mock_auth_user):
        """Test task cancellation endpoint"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        try:
            sandbox_id = str(uuid4())
            task_id = str(uuid4())
            
            response = client.delete(f"/api/sandbox/{sandbox_id}/tasks/{task_id}")
            
            # Should handle non-existent task gracefully
            assert response.status_code in [404, 500]  # Task not found or service error
        
        finally:
            app.dependency_overrides.clear()


class TestStreamingRateLimiting:
    """Test rate limiting for streaming endpoints"""
    
    @patch('main.redis_client')
    @patch('main.limiter')
    def test_streaming_rate_limit(self, mock_limiter, mock_redis, client, mock_auth_user):
        """Test that rate limiting is applied to streaming endpoint"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        # Mock rate limiter to raise exception
        from slowapi.errors import RateLimitExceeded
        mock_limiter.limit.side_effect = RateLimitExceeded("Rate limit exceeded")
        
        try:
            sandbox_id = str(uuid4())
            response = client.post(
                f"/api/sandbox/{sandbox_id}/tasks/stream",
                json={"command": "echo test"}
            )
            
            # Should be rate limited (429) or error handled differently
            assert response.status_code in [429, 500]
        
        finally:
            app.dependency_overrides.clear()


class TestStreamingTaskIntegration:
    """Test integration with Task management system"""
    
    @patch('main.redis_client')
    @patch('main.daytona')
    def test_task_creation_and_tracking(
        self, mock_daytona, mock_redis, client, mock_auth_user, mock_sandbox_data, test_db
    ):
        """Test that tasks are properly created and tracked during streaming"""
        
        # Override dependencies with test database
        def get_test_task_service():
            return TaskService(db_session=test_db, redis_client=None, daytona_service=mock_daytona)
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        app.dependency_overrides[get_task_service] = get_test_task_service
        
        # Mock Redis and Daytona
        mock_redis.get.return_value = json.dumps(mock_sandbox_data)
        mock_command_response = Mock()
        mock_command_response.exit_code = 0
        mock_command_response.result = "Task completed successfully"
        mock_daytona.execute_command.return_value = mock_command_response
        
        sandbox_id = mock_sandbox_data["sandbox_id"]
        
        try:
            # Make streaming request
            with client.stream(
                "POST",
                f"/api/sandbox/{sandbox_id}/tasks/stream", 
                json={"command": "echo 'integration test'", "metadata": {"test": "true"}}
            ) as response:
                
                assert response.status_code == 200
                
                # Consume some of the stream
                lines_read = 0
                for line in response.iter_lines():
                    lines_read += 1
                    if lines_read > 5:  # Read a few lines then break
                        break
            
            # Verify task was created in database
            tasks = test_db.query(Task).all()
            assert len(tasks) >= 1
            
            created_task = tasks[0]
            assert created_task.command == "echo 'integration test'"
            assert created_task.task_metadata == {"test": "true"}
            assert created_task.status in [TaskStatus.COMPLETED, TaskStatus.RUNNING, TaskStatus.PENDING]
        
        finally:
            app.dependency_overrides.clear()


class TestStreamingErrorHandling:
    """Test error handling in streaming scenarios"""
    
    @patch('main.redis_client')
    @patch('main.daytona')
    def test_streaming_with_command_failure(
        self, mock_daytona, mock_redis, client, mock_auth_user, mock_sandbox_data
    ):
        """Test streaming when command execution fails"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        # Mock Redis
        mock_redis.get.return_value = json.dumps(mock_sandbox_data)
        
        # Mock Daytona to return error
        mock_command_response = Mock()
        mock_command_response.exit_code = 1
        mock_command_response.result = "Command failed with error"
        mock_daytona.execute_command.return_value = mock_command_response
        
        sandbox_id = mock_sandbox_data["sandbox_id"]
        
        try:
            with client.stream(
                "POST",
                f"/api/sandbox/{sandbox_id}/tasks/stream",
                json={"command": "invalid-command"}
            ) as response:
                
                assert response.status_code == 200  # Stream starts successfully
                
                # Read stream content
                content_lines = []
                for line in response.iter_lines():
                    if line:
                        content_lines.append(line)
                        if len(content_lines) > 10:
                            break
                
                # Should contain error information in the stream
                full_content = "\n".join(content_lines)
                assert "data: " in full_content
                assert "[DONE]" in full_content
        
        finally:
            app.dependency_overrides.clear()
    
    @patch('main.redis_client')
    @patch('main.daytona')
    def test_streaming_with_daytona_exception(
        self, mock_daytona, mock_redis, client, mock_auth_user, mock_sandbox_data
    ):
        """Test streaming when Daytona service raises exception"""
        
        app.dependency_overrides[get_current_user] = lambda: mock_auth_user
        
        # Mock Redis
        mock_redis.get.return_value = json.dumps(mock_sandbox_data)
        
        # Mock Daytona to raise exception
        mock_daytona.execute_command.side_effect = Exception("Daytona service error")
        
        sandbox_id = mock_sandbox_data["sandbox_id"]
        
        try:
            with client.stream(
                "POST",
                f"/api/sandbox/{sandbox_id}/tasks/stream",
                json={"command": "echo test"}
            ) as response:
                
                assert response.status_code == 200  # Stream starts
                
                # Should handle error gracefully in stream
                content_lines = []
                for line in response.iter_lines():
                    if line:
                        content_lines.append(line)
                        if len(content_lines) > 15:  # Give more lines for error handling
                            break
                
                # Should contain error and termination
                full_content = "\n".join(content_lines)
                assert "[DONE]" in full_content
                
                # Should contain error information
                error_found = any("error" in line.lower() for line in content_lines)
                assert error_found
        
        finally:
            app.dependency_overrides.clear()


# Run integration tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])