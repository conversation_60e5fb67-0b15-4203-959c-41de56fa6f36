# Multi-stage Dockerfile for FastAPI application

# Base stage with common dependencies
FROM python:3.11-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Install UV
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/

# Copy Python project files for dependency resolution
COPY pyproject.toml uv.lock ./

# Install Python dependencies using UV
RUN uv sync --frozen --no-cache

# Development stage
FROM base as development

# Copy application code
COPY . .

EXPOSE 9100

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9100/health || exit 1

# Use UV to run the application in development mode
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9100", "--reload"]

# Production stage
FROM base as production

# Copy application code
COPY . .

# Make scripts executable and set proper permissions
RUN chmod +x run-migrations.py docker-entrypoint.sh && \
    chmod +r alembic.ini

# Create app user and set ownership
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app

# Switch to app user
USER app

EXPOSE 9100

# Healthcheck with better error handling
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:9100/health || exit 1

# Use entrypoint for initialization
ENTRYPOINT ["./docker-entrypoint.sh"]

# Default command with proper error handling using UV
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9100", "--workers", "2", "--log-level", "info"]