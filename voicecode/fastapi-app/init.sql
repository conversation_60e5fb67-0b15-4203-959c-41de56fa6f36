-- =============================================================================
-- PostgreSQL Initialization Script for VoiceCode
-- This script runs automatically when the PostgreSQL container starts
-- =============================================================================

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable additional useful extensions for performance and functionality
CREATE EXTENSION IF NOT EXISTS "pg_trgm";     -- For text search and similarity
CREATE EXTENSION IF NOT EXISTS "btree_gin";  -- For better indexing performance
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"; -- For query performance monitoring

-- Create performance indexes for chat_messages table (if it exists)
-- These will be created by Alembic migrations, but we prepare for them
-- Note: These CREATE INDEX statements use IF NOT EXISTS to avoid conflicts

-- Function to create indexes only if table exists
CREATE OR REPLACE FUNCTION create_chat_indexes() RETURNS void AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'chat_messages') THEN
        -- Composite index for querying messages by sandbox and creation time (DESC for recent first)
        CREATE INDEX IF NOT EXISTS idx_chat_messages_sandbox_created 
        ON chat_messages(sandbox_id, created_at DESC);
        
        -- Composite index for querying messages by user and creation time (DESC for recent first)
        CREATE INDEX IF NOT EXISTS idx_chat_messages_user_created 
        ON chat_messages(user_id, created_at DESC);
        
        -- GIN index for full-text search on message content
        CREATE INDEX IF NOT EXISTS idx_chat_messages_content_search 
        ON chat_messages USING gin(to_tsvector('english', content));
        
        -- Index for message type filtering (useful for filtering by user/system/error messages)
        CREATE INDEX IF NOT EXISTS idx_chat_messages_type 
        ON chat_messages(message_type);
        
        -- Composite index for command tracking
        CREATE INDEX IF NOT EXISTS idx_chat_messages_command_created
        ON chat_messages(command_id, created_at DESC) WHERE command_id IS NOT NULL;
        
        -- Partial index for recent messages (last 30 days) - frequently accessed
        CREATE INDEX IF NOT EXISTS idx_chat_messages_recent
        ON chat_messages(created_at DESC, sandbox_id) 
        WHERE created_at > (CURRENT_TIMESTAMP - INTERVAL '30 days');
        
        RAISE NOTICE 'Chat message indexes created successfully';
    ELSE
        RAISE NOTICE 'chat_messages table does not exist yet - indexes will be created after migration';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Execute the index creation function
SELECT create_chat_indexes();

-- Set up database configuration for optimal performance in development
-- These settings are appropriate for development environments
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Enable logging for development (helps with debugging)
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log queries taking more than 1 second

-- Reload configuration
SELECT pg_reload_conf();

-- Create a function to check database health
CREATE OR REPLACE FUNCTION check_database_health()
RETURNS TABLE(
    status TEXT,
    database_name TEXT,
    connection_count INTEGER,
    active_connections INTEGER,
    extensions_enabled TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'healthy'::TEXT as status,
        current_database()::TEXT as database_name,
        (SELECT count(*) FROM pg_stat_activity)::INTEGER as connection_count,
        (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::INTEGER as active_connections,
        ARRAY(SELECT extname FROM pg_extension ORDER BY extname)::TEXT[] as extensions_enabled;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get table statistics
CREATE OR REPLACE FUNCTION get_table_stats()
RETURNS TABLE(
    table_name TEXT,
    row_count BIGINT,
    table_size TEXT,
    index_size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::TEXT,
        COALESCE(s.n_tup_ins - s.n_tup_del, 0) as row_count,
        pg_size_pretty(pg_total_relation_size(c.oid)) as table_size,
        pg_size_pretty(pg_indexes_size(c.oid)) as index_size
    FROM information_schema.tables t
    LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
    LEFT JOIN pg_class c ON c.relname = t.table_name
    WHERE t.table_schema = 'public'
    ORDER BY t.table_name;
END;
$$ LANGUAGE plpgsql;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'VoiceCode PostgreSQL database initialized successfully';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'Version: %', version();
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pg_trgm, btree_gin, pg_stat_statements';
    RAISE NOTICE 'Performance indexes and monitoring functions created';
END
$$;