"""Shared dependencies for the FastAPI application"""

import json
import jwt
import redis
import uuid
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import HTTPException, Depends
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session
from slowapi import Limiter
from slowapi.util import get_remote_address

from database import get_db
from services import TaskService

# Initialize shared components
security = HTTPBearer()

# These will be set by main.py during startup
_redis_client = None
_daytona_service = None
_limiter = None
_settings = None


def set_shared_dependencies(redis_client, daytona_service, limiter, settings):
    """Set shared dependencies from main.py"""
    global _redis_client, _daytona_service, _limiter, _settings
    _redis_client = redis_client
    _daytona_service = daytona_service
    _limiter = limiter
    _settings = settings


def get_redis_client():
    """Get Redis client"""
    return _redis_client


def get_daytona_service():
    """Get Daytona service"""
    return _daytona_service


def get_limiter():
    """Get rate limiter"""
    return _limiter


def get_settings():
    """Get application settings"""
    return _settings


def verify_jwt_token(token: str) -> Dict[str, Any]:
    """Verify JWT token and return user data"""
    settings = get_settings()
    if not settings:
        raise HTTPException(status_code=500, detail="Settings not available")
    
    try:
        payload = jwt.decode(token, settings.jwt_secret, algorithms=[settings.jwt_algorithm])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    return verify_jwt_token(token)


def get_task_service(db: Session = Depends(get_db)) -> TaskService:
    """Get TaskService instance with dependencies"""
    return TaskService(
        db_session=db,
        redis_client=get_redis_client(),
        daytona_service=get_daytona_service()
    )