# VoiceCode FastAPI Application

Unified FastAPI application for VoiceCode that handles GitHub authentication and Daytona sandbox management.

## Features

- ✅ GitHub OAuth authentication flow
- ✅ Repository and branch selection
- ✅ Daytona sandbox creation and management
- ✅ Claude CLI command execution in sandboxes
- ✅ JWT token-based authentication
- ✅ Redis caching for sessions and data

## Prerequisites

- Python 3.9+
- Redis server
- GitHub OAuth app
- Daytona account and API key

## Installation

1. **<PERSON><PERSON> and navigate to the directory:**
   ```bash
   cd voicecode/fastapi-app
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

## Configuration

### GitHub OAuth Setup

1. Create a GitHub OAuth app at https://github.com/settings/developers
2. Set the authorization callback URL to: `http://localhost:8000/api/auth/github/callback`
3. Add your client ID and secret to `.env`

### Daytona Setup

1. Get your Daytona API key from the dashboard
2. Ensure you have the `voicecode-claude:1.0.1` image available
3. Add your Daytona credentials to `.env`

### Redis Setup

1. Install and start Redis server
2. Update `REDIS_URL` in `.env` if needed

## Running the Application

```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## API Endpoints

### Authentication

- `GET /api/auth/github` - Initialize GitHub OAuth flow
- `GET /api/auth/github/callback` - Handle GitHub OAuth callback
- `GET /api/auth/me` - Get current user information

### Repositories

- `GET /api/repositories` - List accessible repositories
- `GET /api/repositories/{owner}/{repo}/branches` - List repository branches

### Sandbox Management

- `POST /api/sandbox/create` - Create a new sandbox with repository
- `GET /api/sandbox/list` - List user's sandboxes
- `DELETE /api/sandbox/{sandbox_id}` - Delete a sandbox

### Command Execution

- `POST /api/sandbox/{sandbox_id}/execute` - Execute Claude CLI commands in sandbox

### Health Check

- `GET /health` - Health check endpoint
- `GET /` - Root endpoint with API info

## API Documentation

Once running, visit:
- Interactive docs: http://localhost:8000/docs
- OpenAPI spec: http://localhost:8000/redoc

## Authentication Flow

1. **User initiates OAuth:** `GET /api/auth/github`
2. **GitHub redirects back:** `GET /api/auth/github/callback`
3. **User gets JWT token:** Use in `Authorization: Bearer <token>` header
4. **Access protected endpoints:** All sandbox and repo endpoints require authentication

## Sandbox Workflow

1. **Authenticate with GitHub**
2. **List repositories:** `GET /api/repositories`
3. **Select repository and branch:** `GET /api/repositories/{owner}/{repo}/branches`
4. **Create sandbox:** `POST /api/sandbox/create`
5. **Execute commands:** `POST /api/sandbox/{sandbox_id}/execute`
6. **Clean up:** `DELETE /api/sandbox/{sandbox_id}`

## Example Usage

```python
import httpx

# 1. Start OAuth flow
response = httpx.get("http://localhost:8000/api/auth/github")
auth_url = response.json()["auth_url"]
# User visits auth_url and completes OAuth

# 2. Create sandbox
headers = {"Authorization": "Bearer <jwt_token>"}
sandbox_data = {
    "repo_url": "https://github.com/user/repo.git",
    "branch": "main"
}
response = httpx.post(
    "http://localhost:8000/api/sandbox/create",
    json=sandbox_data,
    headers=headers
)
sandbox = response.json()

# 3. Execute Claude CLI command
command_data = {
    "command": "claude --help",
    "working_directory": "workspace/repository"
}
response = httpx.post(
    f"http://localhost:8000/api/sandbox/{sandbox['sandbox_id']}/execute",
    json=command_data,
    headers=headers
)
result = response.json()
print(result["stdout"])
```

## Security Features

- JWT token-based authentication
- GitHub OAuth integration
- Token validation and expiration
- User-specific resource access control
- CORS middleware for browser compatibility
- Secure token storage in Redis

## Error Handling

The API provides detailed error responses with appropriate HTTP status codes:
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (invalid/expired token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error (server-side issues)

## Logging

The application uses structured logging with emoji prefixes:
- 🔑 Authentication events
- 🚀 Sandbox creation
- 🔄 Command execution
- ✅ Success operations
- ❌ Error conditions

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

### Code Style

```bash
# Install formatting tools
pip install black isort

# Format code
black .
isort .
```

## Migration from Node.js Services

This FastAPI application replaces the previous Node.js microservices:
- `voicecode/services/github-auth` → OAuth endpoints in FastAPI
- `voicecode/services/sandbox-repo` → Sandbox management endpoints in FastAPI
- `voicecode/scripts/sandbox-manager.js` → Integrated Python sandbox management

## Environment Variables

See `.env.example` for all required environment variables and their descriptions.
