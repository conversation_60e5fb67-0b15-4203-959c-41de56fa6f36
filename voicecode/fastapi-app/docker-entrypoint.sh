#!/bin/bash
set -e

echo "🚀 Starting VoiceCode FastAPI application..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check if we should run migrations (default: no, use main.py init instead)
RUN_MIGRATIONS=${RUN_MIGRATIONS:-"false"}

if [ "$RUN_MIGRATIONS" = "true" ]; then
    log "🔄 Running database migrations..."
    uv run python run-migrations.py
    
    if [ $? -eq 0 ]; then
        log "✅ Database migrations completed successfully"
    else
        log "❌ Database migrations failed"
        exit 1
    fi
else
    log "⏭️ Skipping database migrations - using FastAPI app initialization instead"
fi

# Start the FastAPI application
log "🌟 Starting FastAPI application..."
exec "$@"