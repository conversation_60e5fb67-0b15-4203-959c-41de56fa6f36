#!/usr/bin/env python3
"""
Database Migration Runner for VoiceCode
This script runs Alembic migrations automatically during container startup
"""

import os
import sys
import time
import logging
from pathlib import Path
from alembic.config import Config
from alembic import command
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import psycopg2
from psycopg2 import OperationalError as PsycopgOperationalError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def wait_for_database(database_url: str, max_retries: int = 30, retry_interval: int = 2):
    """Wait for database to be available"""
    logger.info("Waiting for database to be available...")
    
    for attempt in range(max_retries):
        try:
            # Try to connect using psycopg2 first (lighter weight)
            conn_params = {
                'host': os.getenv('POSTGRES_HOST', 'postgres'),
                'port': int(os.getenv('POSTGRES_PORT', '5432')),
                'user': os.getenv('POSTGRES_USER', 'voicecode'),
                'password': os.getenv('POSTGRES_PASSWORD', 'voicecode'),
                'database': os.getenv('POSTGRES_DB', 'voicecode'),
                'connect_timeout': 5
            }
            
            conn = psycopg2.connect(**conn_params)
            conn.close()
            logger.info("✅ Database is available!")
            return True
            
        except (PsycopgOperationalError, Exception) as e:
            logger.warning(f"Database not ready (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
            else:
                logger.error("❌ Database failed to become available")
                return False
    
    return False

def check_database_health(database_url: str):
    """Check database health and extensions"""
    try:
        engine = create_engine(database_url)
        with engine.connect() as conn:
            # First check if the health function exists
            try:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_proc 
                        WHERE proname = 'check_database_health'
                    )
                """))
                function_exists = result.fetchone()[0]
                
                if function_exists:
                    # Call the health function
                    result = conn.execute(text("SELECT check_database_health()"))
                    health_data = result.fetchone()
                    if health_data:
                        logger.info(f"✅ Database health check passed: {health_data[0]}")
                else:
                    logger.warning("⚠️ Health check function not yet created, skipping...")
            except Exception as e:
                logger.warning(f"⚠️ Could not check health function: {e}")
            
            # Check extensions (this should always work)
            result = conn.execute(text("SELECT extname FROM pg_extension ORDER BY extname"))
            extensions = [row[0] for row in result.fetchall()]
            logger.info(f"📦 Installed extensions: {', '.join(extensions)}")
            
            # Basic connectivity check
            result = conn.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1
            logger.info("✅ Basic database connectivity confirmed")
            
            return True
    except Exception as e:
        logger.error(f"❌ Database health check failed: {e}")
        return False

def run_migrations(alembic_cfg_path: str, database_url: str):
    """Run Alembic migrations with better error handling"""
    try:
        logger.info("🔄 Starting database migrations...")
        
        # Create Alembic configuration
        alembic_cfg = Config(alembic_cfg_path)
        alembic_cfg.set_main_option("sqlalchemy.url", database_url)
        
        # Check current revision
        try:
            current_rev = command.current(alembic_cfg)
            logger.info(f"📍 Current database revision: {current_rev if current_rev else 'None (empty database)'}")
        except Exception as e:
            logger.warning(f"Could not determine current revision: {e}")
            logger.info("Database may be empty, attempting to create initial schema...")
        
        # Run migrations to head
        logger.info("⬆️ Upgrading database to latest revision...")
        command.upgrade(alembic_cfg, "head")
        
        # Check final revision
        final_rev = command.current(alembic_cfg)
        logger.info(f"✅ Database migration completed. Final revision: {final_rev}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed with error: {e}")
        logger.info("Attempting fallback migration...")
        return run_fallback_migration(database_url)

def run_fallback_migration(database_url: str):
    """Fallback migration using direct table creation"""
    try:
        logger.info("🔄 Running fallback migration with direct table creation...")
        
        # Import database models
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from database import Base, engine
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Fallback migration completed - all tables created successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Fallback migration failed: {e}")
        return False

def create_indexes_post_migration(database_url: str):
    """Create additional indexes after migration"""
    try:
        logger.info("🔧 Creating additional performance indexes...")
        
        engine = create_engine(database_url)
        with engine.connect() as conn:
            # Check if the function exists before calling it
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_proc 
                    WHERE proname = 'create_chat_indexes'
                )
            """))
            function_exists = result.fetchone()[0]
            
            if function_exists:
                # Call the index creation function from init.sql
                conn.execute(text("SELECT create_chat_indexes()"))
                conn.commit()
                logger.info("✅ Additional indexes created successfully")
            else:
                logger.warning("⚠️ Index creation function not found, skipping...")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to create additional indexes: {e}")
        return False

def verify_schema(database_url: str):
    """Verify database schema after migration"""
    try:
        logger.info("🔍 Verifying database schema...")
        
        engine = create_engine(database_url)
        with engine.connect() as conn:
            # Check if expected tables exist
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            logger.info(f"📋 Tables found: {', '.join(tables) if tables else 'None'}")
            
            # Check indexes on chat_messages table if it exists
            if 'chat_messages' in tables:
                result = conn.execute(text("""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE tablename = 'chat_messages' 
                    ORDER BY indexname
                """))
                indexes = [row[0] for row in result.fetchall()]
                logger.info(f"🗂️ Indexes on chat_messages: {', '.join(indexes) if indexes else 'None'}")
            
            # Get table statistics if function exists
            try:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_proc 
                        WHERE proname = 'get_table_stats'
                    )
                """))
                function_exists = result.fetchone()[0]
                
                if function_exists:
                    result = conn.execute(text("SELECT * FROM get_table_stats()"))
                    stats = result.fetchall()
                    if stats:
                        logger.info("📊 Table statistics:")
                        for stat in stats:
                            logger.info(f"  - {stat[0]}: {stat[1]} rows, {stat[2]} table size, {stat[3]} index size")
                else:
                    logger.warning("⚠️ Table statistics function not found, skipping...")
            except Exception as e:
                logger.warning(f"Could not get table statistics: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Schema verification failed: {e}")
        return False

def main():
    """Main migration runner"""
    logger.info("🚀 Starting VoiceCode database migration runner")
    
    # Get configuration from environment
    database_url = os.getenv('DATABASE_URL', '**********************************************/voicecode')
    alembic_cfg_path = os.getenv('ALEMBIC_CONFIG', 'alembic.ini')
    
    logger.info(f"📝 Database URL: {database_url.replace(database_url.split('@')[0].split('://')[-1], '***')}")
    logger.info(f"📝 Alembic config: {alembic_cfg_path}")
    
    # Check if alembic.ini exists
    if not Path(alembic_cfg_path).exists():
        logger.error(f"❌ Alembic configuration file not found: {alembic_cfg_path}")
        sys.exit(1)
    
    # Wait for database to be available
    if not wait_for_database(database_url):
        logger.error("❌ Database is not available. Exiting.")
        sys.exit(1)
    
    # Check database health
    if not check_database_health(database_url):
        logger.error("❌ Database health check failed. Exiting.")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations(alembic_cfg_path, database_url):
        logger.error("❌ Migration failed. Exiting.")
        sys.exit(1)
    
    # Create additional indexes
    if not create_indexes_post_migration(database_url):
        logger.warning("⚠️ Failed to create additional indexes, but continuing...")
    
    # Verify schema
    if not verify_schema(database_url):
        logger.warning("⚠️ Schema verification failed, but continuing...")
    
    logger.info("🎉 Database migration completed successfully!")

if __name__ == "__main__":
    main()