"""Task management service for VoiceCode API"""

import redis
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timezone, timedelta
import logging
import json
from contextlib import contextmanager

from database import Task, TaskStatus
from .exceptions import (
    TaskServiceError,
    TaskAlreadyRunningError,
    TaskNotFoundError,
    UnauthorizedTaskAccessError
)

logger = logging.getLogger(__name__)


class TaskService:
    """Service for managing task lifecycle and state"""
    
    def __init__(self, db_session: Session, redis_client: Optional[redis.Redis] = None, daytona_service = None):
        """Initialize TaskService with dependencies
        
        Args:
            db_session: SQLAlchemy database session
            redis_client: Redis client for locking and caching
            daytona_service: Daytona service for sandbox operations
        """
        self.db = db_session
        self.redis = redis_client
        self.daytona = daytona_service
        self._lock_timeout = 30  # seconds
        self._lock_prefix = "task_lock"
    
    @contextmanager
    def _redis_lock(self, lock_key: str, timeout: int = None):
        """Context manager for Redis-based distributed locking"""
        if not self.redis:
            # No Redis available, use database-level constraints
            yield True
            return
        
        timeout = timeout or self._lock_timeout
        lock_name = f"{self._lock_prefix}:{lock_key}"
        
        try:
            # Try to acquire lock
            if self.redis.set(lock_name, "locked", nx=True, ex=timeout):
                logger.debug(f"Acquired lock: {lock_name}")
                yield True
            else:
                logger.warning(f"Failed to acquire lock: {lock_name}")
                yield False
        finally:
            # Release lock
            try:
                self.redis.delete(lock_name)
                logger.debug(f"Released lock: {lock_name}")
            except Exception as e:
                logger.warning(f"Failed to release lock {lock_name}: {e}")
    
    async def create_task(
        self, 
        sandbox_id: UUID, 
        user_id: UUID, 
        command: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> Task:
        """Create new task in pending state with concurrency control
        
        Args:
            sandbox_id: UUID of the sandbox
            user_id: UUID of the user
            command: Command to execute
            metadata: Optional task metadata
            
        Returns:
            Created Task object
            
        Raises:
            TaskAlreadyRunningError: If active task already exists for sandbox
            TaskServiceError: For other task creation errors
        """
        lock_key = f"sandbox:{sandbox_id}"
        
        with self._redis_lock(lock_key) as lock_acquired:
            if not lock_acquired:
                raise TaskServiceError(f"Failed to acquire lock for sandbox {sandbox_id}")
            
            try:
                # Check for existing active task
                existing_task = self.db.query(Task).filter(
                    and_(
                        Task.sandbox_id == sandbox_id,
                        Task.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING])
                    )
                ).first()
                
                if existing_task:
                    raise TaskAlreadyRunningError(
                        str(sandbox_id), 
                        str(existing_task.id)
                    )
                
                # Validate user permissions for sandbox (simplified for now)
                # In a real implementation, you'd check sandbox ownership
                
                # Create new task
                task = Task(
                    sandbox_id=sandbox_id,
                    user_id=user_id,
                    command=command,
                    status=TaskStatus.PENDING,
                    task_metadata=metadata or {}
                )
                
                self.db.add(task)
                self.db.commit()
                self.db.refresh(task)
                
                logger.info(f"Created task {task.id} for sandbox {sandbox_id}")
                return task
                
            except TaskAlreadyRunningError:
                raise
            except Exception as e:
                self.db.rollback()
                logger.error(f"Failed to create task: {e}")
                raise TaskServiceError(f"Task creation failed: {str(e)}")
    
    async def get_current_task(self, sandbox_id: UUID, user_id: UUID) -> Optional[Task]:
        """Get active task for sandbox/user combination
        
        Args:
            sandbox_id: UUID of the sandbox
            user_id: UUID of the user
            
        Returns:
            Active Task object or None if no active task
        """
        try:
            task = self.db.query(Task).filter(
                and_(
                    Task.sandbox_id == sandbox_id,
                    Task.user_id == user_id,
                    Task.status.in_([TaskStatus.PENDING, TaskStatus.RUNNING])
                )
            ).first()
            
            if task:
                logger.debug(f"Found active task {task.id} for sandbox {sandbox_id}")
            else:
                logger.debug(f"No active task found for sandbox {sandbox_id}")
            
            return task
            
        except Exception as e:
            logger.error(f"Failed to get current task: {e}")
            raise TaskServiceError(f"Failed to get current task: {str(e)}")
    
    async def cancel_task(self, task_id: UUID, user_id: UUID) -> bool:
        """Cancel running task with proper authorization
        
        Args:
            task_id: UUID of the task to cancel
            user_id: UUID of the user requesting cancellation
            
        Returns:
            True if task was cancelled successfully
            
        Raises:
            TaskNotFoundError: If task doesn't exist
            UnauthorizedTaskAccessError: If user not authorized
            TaskServiceError: For other cancellation errors
        """
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            
            if not task:
                raise TaskNotFoundError(str(task_id))
            
            # Check authorization
            if task.user_id != user_id:
                raise UnauthorizedTaskAccessError(str(task_id), str(user_id))
            
            # Can only cancel pending or running tasks
            if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                logger.warning(f"Cannot cancel task {task_id} with status {task.status}")
                return False
            
            # Update task status
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now(timezone.utc)
            task.execution_time = task.calculate_execution_time()
            
            # Add cancellation log entry
            task.add_log_entry({
                "level": "info",
                "message": f"Task cancelled by user {user_id}"
            })
            
            self.db.commit()
            
            logger.info(f"Cancelled task {task_id}")
            return True
            
        except (TaskNotFoundError, UnauthorizedTaskAccessError):
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to cancel task {task_id}: {e}")
            raise TaskServiceError(f"Task cancellation failed: {str(e)}")
    
    async def complete_task(
        self, 
        task_id: UUID, 
        exit_code: int, 
        final_logs: Optional[List[str]] = None
    ) -> Task:
        """Mark task as completed with final state
        
        Args:
            task_id: UUID of the task to complete
            exit_code: Command exit code
            final_logs: Optional final log entries
            
        Returns:
            Updated Task object
            
        Raises:
            TaskNotFoundError: If task doesn't exist
            TaskServiceError: For other completion errors
        """
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            
            if not task:
                raise TaskNotFoundError(str(task_id))
            
            # Update task status based on exit code
            task.status = TaskStatus.COMPLETED if exit_code == 0 else TaskStatus.FAILED
            task.exit_code = exit_code
            task.completed_at = datetime.now(timezone.utc)
            task.execution_time = task.calculate_execution_time()
            
            # Add final logs if provided
            if final_logs:
                for log_entry in final_logs:
                    task.add_log_entry({
                        "level": "info",
                        "message": log_entry
                    })
            
            # Add completion log entry
            status_text = "completed successfully" if exit_code == 0 else "failed"
            task.add_log_entry({
                "level": "info",
                "message": f"Task {status_text} with exit code {exit_code}"
            })
            
            self.db.commit()
            self.db.refresh(task)
            
            logger.info(f"Completed task {task_id} with exit code {exit_code}")
            return task
            
        except TaskNotFoundError:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to complete task {task_id}: {e}")
            raise TaskServiceError(f"Task completion failed: {str(e)}")
    
    async def start_task(self, task_id: UUID) -> Task:
        """Mark task as started (move from pending to running)
        
        Args:
            task_id: UUID of the task to start
            
        Returns:
            Updated Task object
            
        Raises:
            TaskNotFoundError: If task doesn't exist
            TaskServiceError: For other start errors
        """
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            
            if not task:
                raise TaskNotFoundError(str(task_id))
            
            # Can only start pending tasks
            if task.status != TaskStatus.PENDING:
                raise TaskServiceError(f"Cannot start task with status {task.status}")
            
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now(timezone.utc)
            
            # Add start log entry
            task.add_log_entry({
                "level": "info",
                "message": "Task execution started"
            })
            
            self.db.commit()
            self.db.refresh(task)
            
            logger.info(f"Started task {task_id}")
            return task
            
        except TaskNotFoundError:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to start task {task_id}: {e}")
            raise TaskServiceError(f"Task start failed: {str(e)}")
    
    async def get_task(self, task_id: UUID, user_id: UUID) -> Task:
        """Get task by ID with authorization check
        
        Args:
            task_id: UUID of the task
            user_id: UUID of the user requesting access
            
        Returns:
            Task object
            
        Raises:
            TaskNotFoundError: If task doesn't exist
            UnauthorizedTaskAccessError: If user not authorized
        """
        try:
            task = self.db.query(Task).filter(Task.id == task_id).first()
            
            if not task:
                raise TaskNotFoundError(str(task_id))
            
            # Check authorization
            if task.user_id != user_id:
                raise UnauthorizedTaskAccessError(str(task_id), str(user_id))
            
            return task
            
        except (TaskNotFoundError, UnauthorizedTaskAccessError):
            raise
        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            raise TaskServiceError(f"Failed to get task: {str(e)}")
    
    async def list_tasks(
        self, 
        sandbox_id: Optional[UUID] = None, 
        user_id: Optional[UUID] = None,
        status: Optional[TaskStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Task]:
        """List tasks with optional filtering
        
        Args:
            sandbox_id: Optional sandbox filter
            user_id: Optional user filter
            status: Optional status filter
            limit: Maximum number of tasks to return
            offset: Offset for pagination
            
        Returns:
            List of Task objects
        """
        try:
            query = self.db.query(Task)
            
            if sandbox_id:
                query = query.filter(Task.sandbox_id == sandbox_id)
            
            if user_id:
                query = query.filter(Task.user_id == user_id)
            
            if status:
                query = query.filter(Task.status == status)
            
            tasks = query.order_by(Task.created_at.desc()).offset(offset).limit(limit).all()
            
            logger.debug(f"Listed {len(tasks)} tasks")
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            raise TaskServiceError(f"Failed to list tasks: {str(e)}")
    
    async def cleanup_old_tasks(self, days_old: int = 7) -> int:
        """Remove old completed tasks for cleanup
        
        Args:
            days_old: Remove tasks older than this many days
            
        Returns:
            Number of tasks deleted
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
            
            # Delete old completed, failed, or cancelled tasks
            deleted_count = self.db.query(Task).filter(
                and_(
                    Task.created_at < cutoff_date,
                    Task.status.in_([
                        TaskStatus.COMPLETED, 
                        TaskStatus.FAILED, 
                        TaskStatus.CANCELLED
                    ])
                )
            ).delete()
            
            self.db.commit()
            
            logger.info(f"Cleaned up {deleted_count} old tasks (older than {days_old} days)")
            return deleted_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to cleanup old tasks: {e}")
            raise TaskServiceError(f"Task cleanup failed: {str(e)}")
    
    async def get_task_stats(self, sandbox_id: Optional[UUID] = None) -> Dict[str, Any]:
        """Get task statistics
        
        Args:
            sandbox_id: Optional sandbox filter
            
        Returns:
            Dictionary with task statistics
        """
        try:
            query = self.db.query(Task)
            
            if sandbox_id:
                query = query.filter(Task.sandbox_id == sandbox_id)
            
            # Get counts by status
            stats = {}
            for status in TaskStatus:
                count = query.filter(Task.status == status).count()
                stats[f"{status.value}_count"] = count
            
            # Get total count
            stats["total_count"] = query.count()
            
            # Get average execution time for completed tasks
            avg_time = query.filter(
                and_(
                    Task.status == TaskStatus.COMPLETED,
                    Task.execution_time.isnot(None)
                )
            ).with_entities(func.avg(Task.execution_time)).scalar()
            
            stats["avg_execution_time"] = float(avg_time) if avg_time else None
            
            logger.debug(f"Generated task stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get task stats: {e}")
            raise TaskServiceError(f"Failed to get task stats: {str(e)}")