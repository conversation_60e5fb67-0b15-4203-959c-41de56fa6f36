"""Enhanced Daytona service with async streaming capabilities"""

import asyncio
import logging
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Async<PERSON>enerator, Dict, Optional, Any, List
import json
import uuid
from dataclasses import dataclass

from daytona import Daytona, DaytonaConfig
from models.log_models import LogEntry, LogType, StreamingSession


logger = logging.getLogger(__name__)


class DaytonaStreamingError(Exception):
    """Base exception for Daytona streaming operations"""
    pass


class StreamingConnectionError(DaytonaStreamingError):
    """Raised when streaming connection fails"""
    pass


class SessionTimeoutError(DaytonaStreamingError):
    """Raised when session times out"""
    pass


class SessionManagerError(DaytonaStreamingError):
    """Raised when session management fails"""
    pass


@dataclass
class SessionPool:
    """Connection pool for Daytona sessions"""
    max_sessions: int = 10
    session_timeout: int = 300  # 5 minutes
    cleanup_interval: int = 60  # 1 minute
    
    def __post_init__(self):
        self.active_sessions: Dict[str, StreamingSession] = {}
        self.session_clients: Dict[str, Any] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def start_cleanup_task(self):
        """Start the background cleanup task"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop_cleanup_task(self):
        """Stop the background cleanup task"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    async def _cleanup_expired_sessions(self):
        """Background task to cleanup expired sessions"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._remove_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
    
    async def _remove_expired_sessions(self):
        """Remove expired sessions from the pool"""
        async with self._lock:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, session in self.active_sessions.items():
                if (current_time - session.created_at).total_seconds() > self.session_timeout:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                try:
                    await self._cleanup_session(session_id)
                except Exception as e:
                    logger.error(f"Error cleaning up session {session_id}: {e}")
    
    async def _cleanup_session(self, session_id: str):
        """Cleanup a specific session"""
        if session_id in self.active_sessions:
            logger.info(f"Cleaning up expired session: {session_id}")
            
            # Close client if exists
            if session_id in self.session_clients:
                client = self.session_clients[session_id]
                try:
                    if hasattr(client, 'close'):
                        await client.close()
                except Exception as e:
                    logger.warning(f"Error closing client for session {session_id}: {e}")
                del self.session_clients[session_id]
            
            # Remove session
            del self.active_sessions[session_id]
            logger.info(f"Session {session_id} cleaned up successfully")
    
    async def get_session(self, sandbox_id: str, command: Optional[str] = None) -> str:
        """Get or create a session for streaming"""
        async with self._lock:
            # Check if we can reuse an existing session for this sandbox
            for session_id, session in self.active_sessions.items():
                if (session.sandbox_id == sandbox_id and 
                    session.status == "active" and
                    (datetime.now() - session.created_at).total_seconds() < self.session_timeout):
                    logger.info(f"Reusing existing session: {session_id}")
                    return session_id
            
            # Create new session if under limit
            if len(self.active_sessions) >= self.max_sessions:
                # Remove oldest session to make room
                oldest_session_id = min(
                    self.active_sessions.keys(),
                    key=lambda k: self.active_sessions[k].created_at
                )
                await self._cleanup_session(oldest_session_id)
                logger.info(f"Removed oldest session {oldest_session_id} to make room")
            
            # Create new session
            session_id = str(uuid.uuid4())
            session = StreamingSession(
                session_id=session_id,
                sandbox_id=sandbox_id,
                command=command
            )
            
            self.active_sessions[session_id] = session
            logger.info(f"Created new session: {session_id} for sandbox: {sandbox_id}")
            return session_id
    
    async def release_session(self, session_id: str):
        """Release a session from the pool"""
        async with self._lock:
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = "completed"
                # Don't immediately remove - let cleanup task handle it
                logger.info(f"Released session: {session_id}")


class DaytonaService:
    """Enhanced Daytona service with async streaming capabilities"""
    
    def __init__(
        self,
        daytona_config: DaytonaConfig,
        max_sessions: int = 10,
        session_timeout: int = 300,
        retry_attempts: int = 3,
        retry_delay: int = 1
    ):
        self.daytona = Daytona(daytona_config)
        self.config = daytona_config
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # Initialize session pool
        self.session_pool = SessionPool(
            max_sessions=max_sessions,
            session_timeout=session_timeout
        )
        
        # Start cleanup task
        self._initialized = False
    
    async def initialize(self):
        """Initialize the service and start background tasks"""
        if not self._initialized:
            await self.session_pool.start_cleanup_task()
            self._initialized = True
            logger.info("DaytonaService initialized with streaming capabilities")
    
    async def cleanup(self):
        """Cleanup resources and stop background tasks"""
        if self._initialized:
            await self.session_pool.stop_cleanup_task()
            # Cleanup all active sessions
            for session_id in list(self.session_pool.active_sessions.keys()):
                await self.session_pool._cleanup_session(session_id)
            self._initialized = False
            logger.info("DaytonaService cleanup completed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
    
    # Backward compatibility methods (synchronous)
    def create_sandbox(self, *args, **kwargs):
        """Create sandbox (synchronous - backward compatibility)"""
        return self.daytona.create(*args, **kwargs)
    
    def get_sandbox(self, sandbox_id: str):
        """Get sandbox (synchronous - backward compatibility)"""
        return self.daytona.get(sandbox_id)
    
    def delete_sandbox(self, sandbox_id: str):
        """Delete sandbox (synchronous - backward compatibility)"""
        return self.daytona.delete(sandbox_id)
    
    def execute_command(self, sandbox_id: str, command: str, working_directory: str = "/home/<USER>/workspace/repository"):
        """Execute command synchronously (backward compatibility)"""
        sandbox = self.daytona.get(sandbox_id)
        return sandbox.process.exec(command, working_directory)
    
    # New async streaming methods
    async def execute_command_stream(
        self, 
        sandbox_id: str, 
        command: str,
        working_directory: str = "/home/<USER>/workspace/repository"
    ) -> AsyncGenerator[LogEntry, None]:
        """
        Execute command and stream logs in real-time.
        
        Args:
            sandbox_id: ID of the sandbox
            command: Command to execute
            working_directory: Working directory for command execution
            
        Yields:
            LogEntry: Individual log entries with timestamp, type, and content
        """
        session_id = None
        try:
            # Get or create session
            session_id = await self.session_pool.get_session(sandbox_id, command)
            
            # Yield start event
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.START,
                content=f"Starting command execution: {command}",
                metadata={"session_id": session_id, "sandbox_id": sandbox_id}
            )
            
            # Try async streaming first
            try:
                async for log_entry in self._stream_command_async(sandbox_id, command, working_directory, session_id):
                    yield log_entry
                
            except (StreamingConnectionError, NotImplementedError) as e:
                logger.warning(f"Async streaming failed, falling back to sync: {e}")
                
                # Fallback to synchronous execution with polling
                async for log_entry in self._stream_command_sync_fallback(sandbox_id, command, working_directory):
                    yield log_entry
            
            # Yield end event
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.END,
                content="Command execution completed",
                metadata={"session_id": session_id, "sandbox_id": sandbox_id}
            )
            
        except Exception as e:
            logger.error(f"Error in execute_command_stream: {e}")
            
            # Yield error event
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.ERROR,
                content=f"Command execution failed: {str(e)}",
                metadata={"session_id": session_id, "sandbox_id": sandbox_id, "error": str(e)}
            )
            
        finally:
            # Release session
            if session_id:
                await self.session_pool.release_session(session_id)
    
    async def _stream_command_async(
        self, 
        sandbox_id: str, 
        command: str, 
        working_directory: str,
        session_id: str
    ) -> AsyncGenerator[LogEntry, None]:
        """Stream command using Daytona SDK async methods"""
        
        # This is a placeholder for the actual Daytona SDK async streaming implementation
        # In the real implementation, this would use Daytona SDK's async methods
        # For now, we'll simulate async streaming behavior
        
        retry_count = 0
        while retry_count < self.retry_attempts:
            try:
                # Get sandbox
                sandbox = self.daytona.get(sandbox_id)
                
                # Simulate creating an async session (this would be actual Daytona SDK calls)
                # session = await daytona_client.create_session_async(sandbox_id)
                # command_id = await session.execute_command_async(command, working_directory)
                
                # For now, we'll execute synchronously and yield formatted logs
                response = sandbox.process.exec(command, working_directory)
                
                # Parse and yield the response as streaming logs
                if response.result:
                    lines = response.result.split('\n')
                    for line in lines:
                        if line.strip():
                            yield LogEntry(
                                timestamp=datetime.now(),
                                log_type=LogType.STDOUT,
                                content=line,
                                metadata={"session_id": session_id, "exit_code": response.exit_code}
                            )
                            # Add small delay to simulate streaming
                            await asyncio.sleep(0.1)
                
                # Return after successful execution
                return
                
            except Exception as e:
                retry_count += 1
                if retry_count >= self.retry_attempts:
                    raise StreamingConnectionError(f"Failed to stream command after {self.retry_attempts} attempts: {e}")
                
                # Wait before retry
                await asyncio.sleep(self.retry_delay * retry_count)
                logger.warning(f"Retry {retry_count}/{self.retry_attempts} for command streaming: {e}")
    
    async def _stream_command_sync_fallback(
        self, 
        sandbox_id: str, 
        command: str, 
        working_directory: str
    ) -> AsyncGenerator[LogEntry, None]:
        """Fallback to synchronous execution when streaming fails"""
        
        try:
            # Execute synchronously
            response = self.execute_command(sandbox_id, command, working_directory)
            
            # Convert result to streaming format
            if response.result:
                lines = response.result.split('\n')
                for line in lines:
                    if line.strip():
                        yield LogEntry(
                            timestamp=datetime.now(),
                            log_type=LogType.STDOUT,
                            content=line,
                            metadata={"fallback": True, "exit_code": response.exit_code}
                        )
                        # Add small delay to simulate streaming
                        await asyncio.sleep(0.05)
            
            # Yield final status
            if response.exit_code != 0:
                yield LogEntry(
                    timestamp=datetime.now(),
                    log_type=LogType.ERROR,
                    content=f"Command failed with exit code: {response.exit_code}",
                    metadata={"exit_code": response.exit_code}
                )
        
        except Exception as e:
            logger.error(f"Synchronous fallback failed: {e}")
            yield LogEntry(
                timestamp=datetime.now(),
                log_type=LogType.ERROR,
                content=f"Fallback execution failed: {str(e)}",
                metadata={"error": str(e)}
            )
    
    async def get_session_logs_stream(
        self, 
        session_id: str
    ) -> AsyncGenerator[LogEntry, None]:
        """
        Stream logs from existing session.
        
        Args:
            session_id: ID of the streaming session
            
        Yields:
            LogEntry: Log entries from the session
        """
        
        if session_id not in self.session_pool.active_sessions:
            raise SessionManagerError(f"Session {session_id} not found")
        
        session = self.session_pool.active_sessions[session_id]
        
        # This would be the actual implementation using Daytona SDK
        # For now, we'll return a placeholder implementation
        yield LogEntry(
            timestamp=datetime.now(),
            log_type=LogType.SYSTEM,
            content=f"Streaming logs for session: {session_id}",
            metadata={"session_id": session_id, "sandbox_id": session.sandbox_id}
        )
    
    # Session management methods
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active streaming sessions"""
        return [session.to_dict() for session in self.session_pool.active_sessions.values()]
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific session"""
        if session_id in self.session_pool.active_sessions:
            return self.session_pool.active_sessions[session_id].to_dict()
        return None
    
    async def close_session(self, session_id: str):
        """Manually close a specific session"""
        await self.session_pool._cleanup_session(session_id)