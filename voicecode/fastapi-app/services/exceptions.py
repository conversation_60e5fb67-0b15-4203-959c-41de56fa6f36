"""Service-specific exceptions for VoiceCode API"""


class TaskServiceError(Exception):
    """Base exception for task service operations"""
    pass


class TaskAlreadyRunningError(TaskServiceError):
    """Raised when attempting to create task while one is active"""
    
    def __init__(self, sandbox_id: str, existing_task_id: str = None):
        self.sandbox_id = sandbox_id
        self.existing_task_id = existing_task_id
        message = f"Task already running for sandbox {sandbox_id}"
        if existing_task_id:
            message += f" (existing task: {existing_task_id})"
        super().__init__(message)


class TaskNotFoundError(TaskServiceError):
    """Raised when task cannot be found"""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
        super().__init__(f"Task {task_id} not found")


class UnauthorizedTaskAccessError(TaskServiceError):
    """Raised when user lacks permission for task operation"""
    
    def __init__(self, task_id: str, user_id: str):
        self.task_id = task_id
        self.user_id = user_id
        super().__init__(f"User {user_id} not authorized to access task {task_id}")