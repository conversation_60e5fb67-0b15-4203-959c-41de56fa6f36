#!/usr/bin/env python3
"""
Database Setup Test Suite for VoiceCode
Tests database initialization, migrations, and schema creation
"""

import os
import sys
import time
import logging
from pathlib import Path
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import OperationalError
import psycopg2
from psycopg2 import OperationalError as PsycopgOperationalError
from alembic.config import Config
from alembic import command
from alembic.runtime.migration import MigrationContext
from alembic.script import ScriptDirectory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseTestSuite:
    """Test suite for database setup and migrations"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or os.getenv(
            'DATABASE_URL', 
            'postgresql://voicecode:voicecode@localhost:9432/voicecode'
        )
        self.engine = None
        self.alembic_cfg = None
    
    def setup(self):
        """Setup test environment"""
        logger.info("🔧 Setting up database test environment...")
        
        try:
            self.engine = create_engine(self.database_url)
            
            # Setup Alembic configuration
            alembic_cfg_path = 'alembic.ini'
            if Path(alembic_cfg_path).exists():
                self.alembic_cfg = Config(alembic_cfg_path)
                self.alembic_cfg.set_main_option("sqlalchemy.url", self.database_url)
            
            logger.info("✅ Test environment setup completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup test environment: {e}")
            return False
    
    def test_database_connection(self):
        """Test basic database connectivity"""
        logger.info("🔍 Testing database connection...")
        
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                assert result.fetchone()[0] == 1
            
            logger.info("✅ Database connection test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database connection test failed: {e}")
            return False
    
    def test_extensions_installed(self):
        """Test that required PostgreSQL extensions are installed"""
        logger.info("🔍 Testing PostgreSQL extensions...")
        
        required_extensions = ['uuid-ossp', 'pg_trgm', 'btree_gin']
        
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT extname FROM pg_extension ORDER BY extname"))
                installed_extensions = [row[0] for row in result.fetchall()]
                
                logger.info(f"📦 Installed extensions: {', '.join(installed_extensions)}")
                
                missing_extensions = []
                for ext in required_extensions:
                    if ext not in installed_extensions:
                        missing_extensions.append(ext)
                
                if missing_extensions:
                    logger.error(f"❌ Missing required extensions: {', '.join(missing_extensions)}")
                    return False
                
                logger.info("✅ All required extensions are installed")
                return True
                
        except Exception as e:
            logger.error(f"❌ Extension test failed: {e}")
            return False
    
    def test_health_functions(self):
        """Test that health check functions are available"""
        logger.info("🔍 Testing health check functions...")
        
        try:
            with self.engine.connect() as conn:
                # Test check_database_health function
                result = conn.execute(text("SELECT check_database_health()"))
                health_data = result.fetchone()
                assert health_data is not None
                logger.info(f"✅ Database health function works: {health_data[0]}")
                
                # Test get_table_stats function
                result = conn.execute(text("SELECT * FROM get_table_stats()"))
                stats = result.fetchall()
                logger.info(f"✅ Table stats function works, found {len(stats)} tables")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Health functions test failed: {e}")
            return False
    
    def test_migration_system(self):
        """Test Alembic migration system"""
        logger.info("🔍 Testing migration system...")
        
        if not self.alembic_cfg:
            logger.error("❌ Alembic configuration not available")
            return False
        
        try:
            # Check if we can get current revision
            with self.engine.connect() as conn:
                context = MigrationContext.configure(conn)
                current_rev = context.get_current_revision()
                logger.info(f"📍 Current migration revision: {current_rev or 'None'}")
            
            # Check migration scripts
            script_dir = ScriptDirectory.from_config(self.alembic_cfg)
            revisions = list(script_dir.walk_revisions())
            logger.info(f"📜 Found {len(revisions)} migration scripts")
            
            for rev in revisions:
                logger.info(f"  - {rev.revision}: {rev.doc}")
            
            logger.info("✅ Migration system test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration system test failed: {e}")
            return False
    
    def test_schema_creation(self):
        """Test database schema creation"""
        logger.info("🔍 Testing schema creation...")
        
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            
            logger.info(f"📋 Found tables: {', '.join(tables) if tables else 'None'}")
            
            # Check if chat_messages table exists (should be created by migration)
            if 'chat_messages' in tables:
                # Check table structure
                columns = inspector.get_columns('chat_messages')
                column_names = [col['name'] for col in columns]
                
                expected_columns = [
                    'id', 'sandbox_id', 'user_id', 'message_type', 
                    'content', 'command_id', 'created_at', 'message_metadata'
                ]
                
                missing_columns = []
                for col in expected_columns:
                    if col not in column_names:
                        missing_columns.append(col)
                
                if missing_columns:
                    logger.error(f"❌ Missing columns in chat_messages: {', '.join(missing_columns)}")
                    return False
                
                logger.info(f"✅ chat_messages table has all expected columns: {', '.join(column_names)}")
                
                # Check indexes
                indexes = inspector.get_indexes('chat_messages')
                index_names = [idx['name'] for idx in indexes]
                logger.info(f"🗂️ chat_messages indexes: {', '.join(index_names)}")
                
            else:
                logger.warning("⚠️ chat_messages table not found - may need migration")
            
            logger.info("✅ Schema creation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Schema creation test failed: {e}")
            return False
    
    def test_index_creation(self):
        """Test that performance indexes are created"""
        logger.info("🔍 Testing index creation...")
        
        try:
            with self.engine.connect() as conn:
                # Check if chat_messages table exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = 'chat_messages'
                    )
                """))
                
                table_exists = result.fetchone()[0]
                
                if not table_exists:
                    logger.warning("⚠️ chat_messages table doesn't exist yet - skipping index test")
                    return True
                
                # Check for expected indexes
                result = conn.execute(text("""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE tablename = 'chat_messages' 
                    ORDER BY indexname
                """))
                
                indexes = [row[0] for row in result.fetchall()]
                logger.info(f"🗂️ Found indexes on chat_messages: {', '.join(indexes)}")
                
                expected_indexes = [
                    'idx_chat_messages_sandbox_created',
                    'idx_chat_messages_user_created',
                    'idx_chat_messages_type'
                ]
                
                missing_indexes = []
                for idx in expected_indexes:
                    if idx not in indexes:
                        missing_indexes.append(idx)
                
                if missing_indexes:
                    logger.warning(f"⚠️ Some expected indexes are missing: {', '.join(missing_indexes)}")
                    # Try to create them
                    try:
                        conn.execute(text("SELECT create_chat_indexes()"))
                        conn.commit()
                        logger.info("✅ Created missing indexes")
                    except Exception as e:
                        logger.error(f"❌ Failed to create missing indexes: {e}")
                        return False
                
                logger.info("✅ Index creation test passed")
                return True
                
        except Exception as e:
            logger.error(f"❌ Index creation test failed: {e}")
            return False
    
    def test_data_operations(self):
        """Test basic data operations"""
        logger.info("🔍 Testing data operations...")
        
        try:
            with self.engine.connect() as conn:
                # Check if chat_messages table exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = 'chat_messages'
                    )
                """))
                
                table_exists = result.fetchone()[0]
                
                if not table_exists:
                    logger.warning("⚠️ chat_messages table doesn't exist - skipping data operations test")
                    return True
                
                # Test insert operation
                test_data = {
                    'id': 'gen_random_uuid()',
                    'sandbox_id': 'gen_random_uuid()',
                    'user_id': 'gen_random_uuid()',
                    'message_type': 'test',
                    'content': 'Test message for database validation'
                }
                
                insert_sql = text("""
                    INSERT INTO chat_messages (id, sandbox_id, user_id, message_type, content)
                    VALUES (gen_random_uuid(), gen_random_uuid(), gen_random_uuid(), :message_type, :content)
                    RETURNING id
                """)
                
                result = conn.execute(insert_sql, {
                    'message_type': test_data['message_type'],
                    'content': test_data['content']
                })
                
                inserted_id = result.fetchone()[0]
                logger.info(f"✅ Successfully inserted test record with ID: {inserted_id}")
                
                # Test select operation
                select_sql = text("SELECT * FROM chat_messages WHERE id = :id")
                result = conn.execute(select_sql, {'id': inserted_id})
                record = result.fetchone()
                
                assert record is not None
                assert record[4] == test_data['content']  # content column
                logger.info("✅ Successfully retrieved test record")
                
                # Clean up test data
                delete_sql = text("DELETE FROM chat_messages WHERE id = :id")
                conn.execute(delete_sql, {'id': inserted_id})
                conn.commit()
                logger.info("✅ Successfully cleaned up test data")
                
                logger.info("✅ Data operations test passed")
                return True
                
        except Exception as e:
            logger.error(f"❌ Data operations test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all database tests"""
        logger.info("🚀 Starting comprehensive database test suite...")
        
        if not self.setup():
            return False
        
        tests = [
            ('Database Connection', self.test_database_connection),
            ('PostgreSQL Extensions', self.test_extensions_installed),
            ('Health Functions', self.test_health_functions),
            ('Migration System', self.test_migration_system),
            ('Schema Creation', self.test_schema_creation),
            ('Index Creation', self.test_index_creation),
            ('Data Operations', self.test_data_operations),
        ]
        
        passed_tests = 0
        failed_tests = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name} - PASSED")
                else:
                    failed_tests += 1
                    logger.error(f"❌ {test_name} - FAILED")
            except Exception as e:
                failed_tests += 1
                logger.error(f"❌ {test_name} - ERROR: {e}")
        
        # Summary
        logger.info(f"\n{'='*50}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*50}")
        logger.info(f"Total tests: {len(tests)}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        
        if failed_tests == 0:
            logger.info("🎉 All tests passed!")
            return True
        else:
            logger.error(f"❌ {failed_tests} test(s) failed")
            return False

def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description='VoiceCode Database Test Suite')
    parser.add_argument('--database-url', help='Database URL to test against')
    parser.add_argument('--wait-for-db', action='store_true', help='Wait for database to be available')
    args = parser.parse_args()
    
    database_url = args.database_url or os.getenv(
        'DATABASE_URL', 
        'postgresql://voicecode:voicecode@localhost:9432/voicecode'
    )
    
    # Wait for database if requested
    if args.wait_for_db:
        logger.info("⏳ Waiting for database to be available...")
        max_retries = 30
        for attempt in range(max_retries):
            try:
                engine = create_engine(database_url)
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                logger.info("✅ Database is available!")
                break
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.info(f"Database not ready (attempt {attempt + 1}/{max_retries}), waiting...")
                    time.sleep(2)
                else:
                    logger.error("❌ Database failed to become available")
                    sys.exit(1)
    
    # Run tests
    test_suite = DatabaseTestSuite(database_url)
    success = test_suite.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()