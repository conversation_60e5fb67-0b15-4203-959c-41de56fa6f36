# Long-Running Tasks Implementation Plan

## Overview

This document outlines the implementation plan for handling long-running tasks in the VoiceCode FastAPI application, specifically for `claude -p` commands that may run for 10-30 minutes. The solution leverages Daytona's session-based execution and log streaming capabilities.

## Current Problem

The existing `/api/sandbox/{sandbox_id}/execute` endpoint uses synchronous execution (`sandbox.process.exec`), which will timeout for long-running tasks and blocks the API server during execution.

## Solution Architecture

### 1. Session-Based Execution with Log Streaming

Replace synchronous execution with Daytona's session-based approach:

- **Session management**: Use `sandbox.process.create_session()` and `sandbox.process.execute_session_command()`
- **Asynchronous execution**: Execute commands with `var_async=True`
- **Real-time monitoring**: Stream logs using `sandbox.process.get_session_command_logs_async()`

### 2. New API Endpoints

```
POST /api/sandbox/{sandbox_id}/execute/async              # Start long-running task
GET  /api/sandbox/{sandbox_id}/execute/{task_id}/status   # Check task status
GET  /api/sandbox/{sandbox_id}/execute/{task_id}/logs     # Stream logs (SSE)
POST /api/sandbox/{sandbox_id}/execute/{task_id}/cancel   # Cancel task
GET  /api/sandbox/{sandbox_id}/execute/tasks              # List active tasks
```

### 3. Task Management System

#### Redis-Based Task Tracking

Store task information in Redis with the following structure:

```python
task_info = {
    "task_id": str(uuid.uuid4()),
    "sandbox_id": sandbox_id,
    "user_id": user_id,
    "command": processed_command,
    "original_command": request.command,
    "session_id": session_id,
    "cmd_id": cmd_id,
    "status": "running|completed|failed|cancelled",
    "started_at": datetime.now().isoformat(),
    "completed_at": None,
    "exit_code": None,
    "result": None,
    "logs": [],  # Store recent logs for quick access
    "working_directory": request.working_directory
}
```

#### Task Status States

- `pending`: Task created but not yet started
- `running`: Task is currently executing
- `completed`: Task finished successfully
- `failed`: Task finished with error
- `cancelled`: Task was cancelled by user
- `timeout`: Task exceeded maximum execution time

## Implementation Details

### 1. Async Task Execution Endpoint

```python
@app.post("/api/sandbox/{sandbox_id}/execute/async")
async def start_async_task(
    sandbox_id: str,
    request: CommandRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Start a long-running task asynchronously"""
    
    # Generate unique task ID
    task_id = str(uuid.uuid4())
    
    # Process command (wrap with claude -p if needed)
    processed_command = process_command(request.command)
    
    # Create session
    session_id = f"claude-session-{task_id}"
    sandbox.process.create_session(session_id)
    
    # Execute async command
    command = sandbox.process.execute_session_command(
        session_id,
        SessionExecuteRequest(
            command=processed_command,
            var_async=True
        )
    )
    
    # Store task info in Redis
    task_info = {
        "task_id": task_id,
        "sandbox_id": sandbox_id,
        "user_id": current_user["user"]["user"]["id"],
        "command": processed_command,
        "original_command": request.command,
        "session_id": session_id,
        "cmd_id": command.cmd_id,
        "status": "running",
        "started_at": datetime.now().isoformat(),
        "working_directory": request.working_directory
    }
    
    store_data(f"task:{task_id}", json.dumps(task_info), 7200)  # 2 hours TTL
    
    # Start background log streaming
    background_tasks.add_task(
        stream_logs_to_redis, 
        sandbox, 
        session_id, 
        command.cmd_id, 
        task_id
    )
    
    return {
        "data": {
            "task_id": task_id,
            "status": "running",
            "message": "Task started successfully"
        }
    }
```

### 2. Background Log Processing

```python
async def stream_logs_to_redis(sandbox, session_id: str, cmd_id: str, task_id: str):
    """Stream logs from Daytona session to Redis"""
    try:
        def handle_log_chunk(chunk: str):
            # Store log chunk in Redis
            current_time = datetime.now().isoformat()
            log_entry = {
                "timestamp": current_time,
                "content": chunk.replace("\x00", "")  # Clean null bytes
            }
            
            # Append to task logs
            task_key = f"task:{task_id}"
            task_data = get_data(task_key)
            if task_data:
                task_info = json.loads(task_data)
                if "logs" not in task_info:
                    task_info["logs"] = []
                
                task_info["logs"].append(log_entry)
                
                # Keep only last 1000 log entries to prevent memory issues
                if len(task_info["logs"]) > 1000:
                    task_info["logs"] = task_info["logs"][-1000:]
                
                # Update task in Redis
                store_data(task_key, json.dumps(task_info), 7200)
            
            # Also store in separate log stream for SSE
            store_data(f"task_log:{task_id}:{current_time}", chunk, 3600)
        
        # Stream logs asynchronously
        await sandbox.process.get_session_command_logs_async(
            session_id, 
            cmd_id, 
            handle_log_chunk
        )
        
        # Task completed - update status
        task_key = f"task:{task_id}"
        task_data = get_data(task_key)
        if task_data:
            task_info = json.loads(task_data)
            task_info["status"] = "completed"
            task_info["completed_at"] = datetime.now().isoformat()
            store_data(task_key, json.dumps(task_info), 7200)
            
    except Exception as e:
        logger.error(f"Error streaming logs for task {task_id}: {e}")
        # Mark task as failed
        task_key = f"task:{task_id}"
        task_data = get_data(task_key)
        if task_data:
            task_info = json.loads(task_data)
            task_info["status"] = "failed"
            task_info["completed_at"] = datetime.now().isoformat()
            task_info["error"] = str(e)
            store_data(task_key, json.dumps(task_info), 7200)
```

### 3. Task Status Endpoint

```python
@app.get("/api/sandbox/{sandbox_id}/execute/{task_id}/status")
async def get_task_status(
    sandbox_id: str,
    task_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Get status of a long-running task"""
    
    task_data = get_data(f"task:{task_id}")
    if not task_data:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = json.loads(task_data)
    
    # Verify ownership
    if task_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # If task is still running, check actual status from Daytona
    if task_info["status"] == "running":
        try:
            sandbox = daytona.get(sandbox_id)
            # Could check session status here if needed
        except Exception as e:
            logger.warning(f"Could not verify task status: {e}")
    
    return {
        "data": {
            "task_id": task_id,
            "status": task_info["status"],
            "started_at": task_info["started_at"],
            "completed_at": task_info.get("completed_at"),
            "command": task_info["original_command"],
            "exit_code": task_info.get("exit_code"),
            "result": task_info.get("result"),
            "recent_logs": task_info.get("logs", [])[-10:]  # Last 10 log entries
        }
    }
```

### 4. Server-Sent Events for Real-time Logs

```python
from fastapi.responses import StreamingResponse
import asyncio

@app.get("/api/sandbox/{sandbox_id}/execute/{task_id}/logs")
async def stream_task_logs(
    sandbox_id: str,
    task_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Stream task logs in real-time using Server-Sent Events"""
    
    # Verify task ownership
    task_data = get_data(f"task:{task_id}")
    if not task_data:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = json.loads(task_data)
    if task_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    async def event_stream():
        last_timestamp = None
        
        while True:
            # Get current task status
            current_task_data = get_data(f"task:{task_id}")
            if not current_task_data:
                break
                
            current_task_info = json.loads(current_task_data)
            
            # Send new log entries
            logs = current_task_info.get("logs", [])
            for log_entry in logs:
                if last_timestamp is None or log_entry["timestamp"] > last_timestamp:
                    yield f"data: {json.dumps(log_entry)}\n\n"
                    last_timestamp = log_entry["timestamp"]
            
            # Send status updates
            yield f"event: status\ndata: {json.dumps({'status': current_task_info['status']})}\n\n"
            
            # Break if task is completed
            if current_task_info["status"] in ["completed", "failed", "cancelled"]:
                break
                
            await asyncio.sleep(1)  # Poll every second
    
    return StreamingResponse(
        event_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
```

### 5. Task Cancellation

```python
@app.post("/api/sandbox/{sandbox_id}/execute/{task_id}/cancel")
async def cancel_task(
    sandbox_id: str,
    task_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """Cancel a running task"""
    
    task_data = get_data(f"task:{task_id}")
    if not task_data:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = json.loads(task_data)
    if task_info.get("user_id") != current_user["user"]["user"]["id"]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    if task_info["status"] != "running":
        raise HTTPException(status_code=400, detail="Task is not running")
    
    try:
        sandbox = daytona.get(sandbox_id)
        
        # Kill the session (this will terminate the running command)
        session_id = task_info["session_id"]
        # Note: Daytona SDK might not have direct session termination
        # Alternative: execute a kill command for the process
        
        # Update task status
        task_info["status"] = "cancelled"
        task_info["completed_at"] = datetime.now().isoformat()
        store_data(f"task:{task_id}", json.dumps(task_info), 7200)
        
        return {
            "data": {"status": "cancelled"},
            "message": "Task cancelled successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to cancel task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")
```

## Configuration and Settings

### Environment Variables

Add to `.env`:

```bash
# Long-running task settings
MAX_TASK_DURATION_MINUTES=30
MAX_CONCURRENT_TASKS_PER_USER=3
TASK_CLEANUP_INTERVAL_HOURS=24
LOG_RETENTION_HOURS=24
```

### Task Limits and Cleanup

```python
# In main.py settings
class Settings(BaseSettings):
    # ... existing settings ...
    
    # Task management
    max_task_duration_minutes: int = Field(30, env="MAX_TASK_DURATION_MINUTES")
    max_concurrent_tasks_per_user: int = Field(3, env="MAX_CONCURRENT_TASKS_PER_USER")
    task_cleanup_interval_hours: int = Field(24, env="TASK_CLEANUP_INTERVAL_HOURS")
    log_retention_hours: int = Field(24, env="LOG_RETENTION_HOURS")
```

### Background Cleanup Task

```python
def cleanup_expired_tasks():
    """Background task to clean up expired tasks"""
    while True:
        try:
            # Get all task keys
            task_keys = get_keys("task:*")
            current_time = datetime.now()
            
            for key in task_keys:
                task_data = get_data(key)
                if task_data:
                    task_info = json.loads(task_data)
                    started_at = datetime.fromisoformat(task_info["started_at"])
                    
                    # Check if task has exceeded max duration
                    if (current_time - started_at).total_seconds() > settings.max_task_duration_minutes * 60:
                        if task_info["status"] == "running":
                            # Mark as timeout
                            task_info["status"] = "timeout"
                            task_info["completed_at"] = current_time.isoformat()
                            store_data(key, json.dumps(task_info), 3600)  # Keep for 1 hour
                    
                    # Clean up old completed tasks
                    if task_info["status"] in ["completed", "failed", "cancelled", "timeout"]:
                        completed_at = task_info.get("completed_at")
                        if completed_at:
                            completed_time = datetime.fromisoformat(completed_at)
                            if (current_time - completed_time).total_seconds() > settings.task_cleanup_interval_hours * 3600:
                                delete_data(key)
                                # Also clean up associated log entries
                                log_keys = get_keys(f"task_log:{task_info['task_id']}:*")
                                for log_key in log_keys:
                                    delete_data(log_key)
                                    
        except Exception as e:
            logger.error(f"Error in cleanup_expired_tasks: {e}")
        
        time.sleep(3600)  # Run every hour

# Start cleanup task
cleanup_thread = threading.Thread(target=cleanup_expired_tasks, daemon=True)
cleanup_thread.start()
```

## Frontend Integration

### Mobile App Changes

1. **Task Initiation**: Replace direct command execution with async task creation
2. **Progress Monitoring**: Use SSE to show real-time progress
3. **Status Updates**: Poll task status endpoint for updates
4. **User Experience**: Show progress bars, allow cancellation, handle network interruptions

### Example Frontend Flow

```typescript
// Start long-running task
const response = await fetch(`/api/sandbox/${sandboxId}/execute/async`, {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: JSON.stringify({ command: 'create a new feature for user authentication' })
});

const { task_id } = await response.json();

// Monitor progress with SSE
const eventSource = new EventSource(`/api/sandbox/${sandboxId}/execute/${task_id}/logs`);

eventSource.onmessage = (event) => {
  const logEntry = JSON.parse(event.data);
  appendToConsole(logEntry.content);
};

eventSource.addEventListener('status', (event) => {
  const statusUpdate = JSON.parse(event.data);
  updateTaskStatus(statusUpdate.status);
});

// Check final status
const statusResponse = await fetch(`/api/sandbox/${sandboxId}/execute/${task_id}/status`);
const taskStatus = await statusResponse.json();
```

## Benefits

1. **Non-blocking**: API server remains responsive during long-running tasks
2. **Real-time feedback**: Users see progress through log streaming
3. **Scalable**: Multiple concurrent tasks per sandbox/user
4. **Resilient**: Tasks survive API server restarts (stored in Redis)
5. **User-friendly**: Progress indicators, cancellation, and status updates
6. **Resource management**: Automatic cleanup and limits prevent resource exhaustion

## Migration Strategy

1. **Phase 1**: Implement new async endpoints alongside existing sync endpoint
2. **Phase 2**: Update frontend to use async endpoints for long commands
3. **Phase 3**: Deprecate sync endpoint for commands that take >30 seconds
4. **Phase 4**: Full migration and cleanup

## Testing Strategy

1. **Unit tests**: Test task creation, status updates, and cleanup
2. **Integration tests**: Test with actual Daytona sandboxes
3. **Load tests**: Verify performance with multiple concurrent tasks
4. **End-to-end tests**: Test complete workflow from frontend to completion

## Monitoring and Observability

1. **Metrics**: Track task duration, success rates, concurrent tasks
2. **Logging**: Comprehensive logging for debugging
3. **Alerts**: Monitor for stuck tasks, high failure rates
4. **Dashboard**: Admin interface to view active tasks and system health