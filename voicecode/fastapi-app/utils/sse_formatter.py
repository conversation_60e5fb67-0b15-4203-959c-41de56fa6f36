"""Server-Sent Events formatter for Vercel AI SDK compatibility"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Generator
from models.streaming_models import SSEMessage
from models.log_models import LogEntry, LogType


class SSEFormatter:
    """Formats messages for Server-Sent Events compatible with Vercel AI SDK"""
    
    def __init__(self):
        self.message_counter = 0
    
    def _generate_message_id(self) -> str:
        """Generate unique message ID"""
        self.message_counter += 1
        return f"msg-{self.message_counter}"
    
    def format_message(
        self, 
        content: str, 
        message_type: str = "task_log",
        task_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Format a message as SSE data.
        
        Args:
            content: Message content
            message_type: Type of message (task_start, task_log, task_complete, task_error)
            task_id: Associated task ID
            **kwargs: Additional metadata
            
        Returns:
            Formatted SSE message string
        """
        metadata = {
            "type": message_type,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        
        if task_id:
            metadata["task_id"] = task_id
        
        message = SSEMessage(
            id=self._generate_message_id(),
            role="assistant",
            content=content,
            metadata=metadata
        )
        
        # Format as SSE data
        return f"data: {message.model_dump_json()}\n\n"
    
    def format_task_start(self, command: str, task_id: str) -> str:
        """Format task start message"""
        return self.format_message(
            content=f"Executing: {command}",
            message_type="task_start",
            task_id=task_id,
            command=command
        )
    
    def format_task_log(self, log_content: str, task_id: str, **metadata) -> str:
        """Format task log message"""
        return self.format_message(
            content=log_content,
            message_type="task_log", 
            task_id=task_id,
            **metadata
        )
    
    def format_task_complete(
        self, 
        task_id: str, 
        exit_code: int, 
        execution_time: Optional[float] = None
    ) -> str:
        """Format task completion message"""
        content = f"Task completed (exit code: {exit_code})"
        metadata = {"exit_code": exit_code}
        
        if execution_time is not None:
            metadata["execution_time"] = execution_time
            content += f" in {execution_time:.2f}s"
        
        return self.format_message(
            content=content,
            message_type="task_complete",
            task_id=task_id,
            **metadata
        )
    
    def format_task_error(self, error_message: str, task_id: Optional[str] = None) -> str:
        """Format task error message"""
        return self.format_message(
            content=f"Error: {error_message}",
            message_type="task_error",
            task_id=task_id,
            error=error_message
        )
    
    def format_log_entry(self, log_entry: LogEntry, task_id: str) -> str:
        """
        Format a LogEntry from DaytonaService as SSE message.
        
        Args:
            log_entry: LogEntry from streaming service
            task_id: Associated task ID
            
        Returns:
            Formatted SSE message string
        """
        # Map LogType to message type
        type_mapping = {
            LogType.START: "task_start",
            LogType.STDOUT: "task_log",
            LogType.STDERR: "task_log", 
            LogType.SYSTEM: "task_log",
            LogType.ERROR: "task_error",
            LogType.END: "task_complete"
        }
        
        message_type = type_mapping.get(log_entry.log_type, "task_log")
        
        # Extract metadata from log entry
        metadata = log_entry.metadata.copy() if log_entry.metadata else {}
        metadata.update({
            "log_type": log_entry.log_type.value,
            "original_timestamp": log_entry.timestamp.isoformat()
        })
        
        return self.format_message(
            content=log_entry.content,
            message_type=message_type,
            task_id=task_id,
            **metadata
        )
    
    def format_done_message(self) -> str:
        """Format final [DONE] message to terminate stream"""
        return "data: [DONE]\n\n"
    
    def format_error_message(self, error: Exception, task_id: Optional[str] = None) -> str:
        """Format error as SSE message"""
        return self.format_task_error(str(error), task_id)


def create_sse_generator(
    log_stream: Generator[LogEntry, None, None], 
    task_id: str,
    formatter: Optional[SSEFormatter] = None
) -> Generator[str, None, None]:
    """
    Convert a log stream to SSE formatted messages.
    
    Args:
        log_stream: Generator of LogEntry objects
        task_id: Task identifier
        formatter: Optional SSEFormatter instance
        
    Yields:
        SSE formatted message strings
    """
    if formatter is None:
        formatter = SSEFormatter()
    
    try:
        for log_entry in log_stream:
            yield formatter.format_log_entry(log_entry, task_id)
        
        # Send completion message
        yield formatter.format_done_message()
        
    except Exception as e:
        # Send error message
        yield formatter.format_error_message(e, task_id)
        yield formatter.format_done_message()