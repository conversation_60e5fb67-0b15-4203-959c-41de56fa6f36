# Custom Daytona image with Claude <PERSON> CLI pre-installed
FROM debian:12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    gnupg \
    lsb-release \
    sudo \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20.x (required for Claude Code CLI)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/*

# Install Claude Code CLI globally
RUN npm install -g @anthropic-ai/claude-code

# Create daytona user with sudo privileges
RUN groupadd -r daytona && \
    useradd -r -g daytona -m -s /bin/bash daytona && \
    echo "daytona ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create workspace directory
RUN mkdir -p /home/<USER>/workspace && \
    chown -R daytona:daytona /home/<USER>

# Switch to daytona user
USER daytona

# Set working directory
WORKDIR /home/<USER>/workspace

# Set environment variables
ENV NODE_ENV=production \
    PATH="/home/<USER>/.npm-global/bin:$PATH" \
    CLAUDE_CODE_ENABLED=true

# Configure npm to use global directory in user home
RUN mkdir -p /home/<USER>/.npm-global && \
    npm config set prefix "/home/<USER>/.npm-global" && \
    chown -R daytona:daytona /home/<USER>/.npm-global

# Pre-create Claude config directory
RUN mkdir -p /home/<USER>/.claude

# Verify Claude Code installation (but don't fail build if it doesn't work)
RUN claude --version || echo "Claude Code CLI installed but may need authentication"

# Default command - keep container running
CMD ["/bin/bash", "-c", "while true; do sleep 30; done"]