/**
 * Simple example showing how to use GitHub token for git clone
 */

const axios = require('axios');
const BASE_URL = 'http://localhost:3000';

async function simpleGitCloneExample() {
  console.log('📱 Simple GitHub App to Git Clone Example');
  
  // Step 1: Get OAuth URL
  console.log('\n1. Getting OAuth URL...');
  const authResponse = await axios.get(`${BASE_URL}/api/auth/github`);
  console.log(`   Open this URL: ${authResponse.data.authUrl}`);
  
  console.log('\n2. Complete OAuth in browser, then come back here with your JWT token');
  console.log('   You can get the JWT token from the browser response');
  
  // Step 3: Use the JWT token to get GitHub token
  const jwtToken = process.argv[2];
  
  if (!jwtToken) {
    console.log('\n❌ Please provide JWT token as argument:');
    console.log('   node simple-git-clone.js YOUR_JWT_TOKEN');
    return;
  }
  
  console.log('\n3. Getting GitHub token...');
  const tokenResponse = await axios.get(`${BASE_URL}/api/auth/token`, {
    headers: { 'Authorization': `Bearer ${jwtToken}` }
  });
  
  const { token: githubToken, user } = tokenResponse.data;
  
  console.log(`✅ GitHub token received: ${githubToken.substring(0, 20)}...`);
  console.log(`   User: ${user}`);
  console.log(`   Token type: ${githubToken.startsWith('ghu_') ? 'User Access Token' : 'Other'}`);
  
  // Step 4: Show git clone format
  console.log('\n4. Git clone format:');
  console.log(`   https://x-access-token:${githubToken}@github.com/owner/repo.git`);
  
  // Step 5: Test with Daytona SDK (example)
  console.log('\n5. Daytona SDK usage:');
  console.log(`   await sandbox.git.clone({`);
  console.log(`     repositoryUrl: 'https://github.com/owner/repo.git',`);
  console.log(`     token: '${githubToken}'`);
  console.log(`   });`);
  
  console.log('\n✅ That\'s it! Super simple.');
}

if (require.main === module) {
  simpleGitCloneExample().catch(console.error);
}