/**
 * Test script for GitHub Apps OAuth integration
 */

const axios = require('axios');
const assert = require('assert');

const BASE_URL = process.env.GITHUB_AUTH_URL || 'http://localhost:3000';

async function testGitHubIntegration() {
  console.log('Testing GitHub Apps OAuth integration...');
  
  try {
    // Test 1: Health check
    console.log('\n1. Testing service health...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    assert(healthResponse.status === 200, 'Health check failed');
    assert(healthResponse.data.status === 'ok', 'Service not healthy');
    console.log('✓ Service is healthy');
    
    // Test 2: OAuth initialization
    console.log('\n2. Testing OAuth initialization...');
    const authResponse = await axios.get(`${BASE_URL}/api/auth/github`);
    assert(authResponse.status === 200, 'OAuth init failed');
    assert(authResponse.data.authUrl, 'No auth URL returned');
    console.log('✓ OAuth initialization working');
    console.log(`   Auth URL: ${authResponse.data.authUrl.substring(0, 100)}...`);
    
    // Test 2b: Device flow initialization
    console.log('\n2b. Testing device flow initialization...');
    const deviceResponse = await axios.post(`${BASE_URL}/api/auth/device`);
    assert(deviceResponse.status === 200, 'Device flow init failed');
    assert(deviceResponse.data.device_code, 'No device code returned');
    assert(deviceResponse.data.user_code, 'No user code returned');
    assert(deviceResponse.data.verification_uri, 'No verification URI returned');
    console.log('✓ Device flow initialization working');
    console.log(`   User code: ${deviceResponse.data.user_code}`);
    console.log(`   Verification URI: ${deviceResponse.data.verification_uri}`);
    
    // Test 2c: Device flow polling (should be pending)
    console.log('\n2c. Testing device flow polling...');
    const pollResponse = await axios.post(`${BASE_URL}/api/auth/device/poll`, {
      device_code: deviceResponse.data.device_code
    });
    assert(pollResponse.status === 200, 'Device flow polling failed');
    assert(pollResponse.data.status === 'pending', 'Device flow should be pending');
    console.log('✓ Device flow polling working (pending state)');
    
    // Test 2d: Device flow status
    console.log('\n2d. Testing device flow status...');
    const statusResponse = await axios.get(`${BASE_URL}/api/auth/device/status`);
    assert(statusResponse.status === 200, 'Device flow status failed');
    assert(Array.isArray(statusResponse.data.active_devices), 'Active devices should be array');
    assert(statusResponse.data.count >= 1, 'Should have at least one active device');
    console.log('✓ Device flow status working');
    console.log(`   Active devices: ${statusResponse.data.count}`);
    
    // Test 3: Protected endpoint without auth (should fail)
    console.log('\n3. Testing protected endpoint without auth...');
    try {
      await axios.get(`${BASE_URL}/api/repositories`);
      throw new Error('Protected endpoint should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✓ Protected endpoint correctly requires authentication');
      } else {
        throw error;
      }
    }
    
    // Test 4: Invalid token (should fail)
    console.log('\n4. Testing invalid token...');
    try {
      await axios.get(`${BASE_URL}/api/repositories`, {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      });
      throw new Error('Invalid token should be rejected');
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log('✓ Invalid token correctly rejected');
      } else {
        throw error;
      }
    }
    
    // Test 5: Rate limiting headers
    console.log('\n5. Testing rate limiting...');
    const rateLimitResponse = await axios.get(`${BASE_URL}/api/auth/github`);
    console.log('✓ Rate limiting headers present');
    
    console.log('\n🎉 All GitHub integration tests passed!');
    console.log('\nNext steps:');
    console.log('1. Create a GitHub App at https://github.com/settings/apps');
    console.log('2. Set the required environment variables');
    console.log('3. Test the complete OAuth flow with a real GitHub App');
    
    return true;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Helper function to test with real GitHub token (for integration testing)
async function testWithRealToken(token) {
  console.log('\nTesting with real GitHub token...');
  
  try {
    // Test token info endpoint
    const meResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✓ Token info retrieved for user: ${meResponse.data.user.username}`);
    console.log(`   Token type: ${meResponse.data.token_type}`);
    console.log(`   Has refresh token: ${meResponse.data.has_refresh_token}`);
    
    if (meResponse.data.expires_at) {
      const expiresAt = new Date(meResponse.data.expires_at);
      console.log(`   Expires at: ${expiresAt.toLocaleString()}`);
    }
    
    // Test repositories endpoint
    const reposResponse = await axios.get(`${BASE_URL}/api/repositories`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✓ Retrieved ${reposResponse.data.repositories.length} repositories`);
    
    if (reposResponse.data.repositories.length > 0) {
      const firstRepo = reposResponse.data.repositories[0];
      const [owner, repo] = firstRepo.fullName.split('/');
      
      // Test branches endpoint
      const branchesResponse = await axios.get(
        `${BASE_URL}/api/repositories/${owner}/${repo}/branches`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      
      console.log(`✓ Retrieved ${branchesResponse.data.branches.length} branches for ${firstRepo.fullName}`);
    }
    
    return true;
  } catch (error) {
    console.error('Real token test failed:', error.message);
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testGitHubIntegration()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testGitHubIntegration, testWithRealToken };