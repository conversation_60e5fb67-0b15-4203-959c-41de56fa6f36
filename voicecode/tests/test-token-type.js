/**
 * Test what type of token your GitHub App is returning
 */

const axios = require('axios');
const BASE_URL = 'http://localhost:3000';

async function testCurrentTokenType() {
  console.log('🔍 Testing current GitHub App token type...');
  
  try {
    // Start OAuth flow
    const authResponse = await axios.get(`${BASE_URL}/api/auth/github`);
    console.log('\n✅ OAuth flow available');
    console.log('🔗 Complete OAuth here:', authResponse.data.authUrl);
    
    console.log('\n📝 Instructions:');
    console.log('1. Open the OAuth URL above in your browser');
    console.log('2. Complete the GitHub authorization');
    console.log('3. Look at the browser/console output for token info');
    console.log('4. Or run: node test-token-type.js YOUR_JWT_TOKEN');
    
    // Test with provided JWT token
    const jwtToken = process.argv[2];
    if (jwtToken) {
      console.log('\n🔍 Testing provided JWT token...');
      
      const meResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
        headers: { 'Authorization': `Bearer ${jwtToken}` }
      });
      
      console.log('\n📊 Token Analysis:');
      console.log(`   Token prefix: ${meResponse.data.token_prefix}`);
      console.log(`   Token length: ${meResponse.data.token_length}`);
      console.log(`   Is User Access Token: ${meResponse.data.is_user_access_token}`);
      
      if (meResponse.data.is_user_access_token) {
        console.log('\n🎉 SUCCESS! You have User Access Tokens');
        console.log('   You can use this token directly in git clone');
      } else {
        console.log('\n⚠️  You have regular OAuth tokens');
        console.log('   Check your GitHub App settings');
      }
      
      console.log('\n🔧 Git clone format:');
      console.log(`   ${meResponse.data.git_clone_format}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

testCurrentTokenType();