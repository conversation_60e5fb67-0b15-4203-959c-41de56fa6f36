/**
 * Simple test to check what type of token we're getting from GitHub
 */

const axios = require('axios');
const BASE_URL = process.env.GITHUB_AUTH_URL || 'http://localhost:3000';

async function checkTokenType() {
  console.log('🔍 Checking what type of tokens we\'re getting...');
  
  try {
    // Test OAuth initialization
    console.log('\n1. Starting OAuth flow...');
    const authResponse = await axios.get(`${BASE_URL}/api/auth/github`);
    console.log('✓ OAuth URL generated');
    console.log(`   Open this URL to complete OAuth: ${authResponse.data.authUrl}`);
    
    console.log('\n2. After completing OAuth in browser, check the token in the response.');
    console.log('   The token should start with:');
    console.log('   - ghu_ = User Access Token (what we want)');
    console.log('   - ghp_ = Personal Access Token');
    console.log('   - gho_ = OAuth Token');
    
    // Also check what tokens are currently stored
    console.log('\n3. If you have an existing token, test with it:');
    console.log('   node check-token-type.js YOUR_TOKEN_HERE');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Test existing token if provided
async function testExistingToken(token) {
  console.log(`\n🔍 Testing token: ${token.substring(0, 20)}...`);
  
  // Check token prefix
  if (token.startsWith('ghu_')) {
    console.log('✅ This is a User Access Token (perfect for git clone)');
  } else if (token.startsWith('ghp_')) {
    console.log('⚠️  This is a Personal Access Token');
  } else if (token.startsWith('gho_')) {
    console.log('⚠️  This is an OAuth Token');
  } else {
    console.log('❓ Unknown token type');
  }
  
  try {
    // Test the token with GitHub API
    const response = await axios.get('https://api.github.com/user', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/vnd.github.v3+json'
      }
    });
    
    console.log(`✅ Token works! User: ${response.data.login}`);
    
    // Test git clone format
    const gitUrl = `https://x-access-token:${token}@github.com/${response.data.login}/REPO_NAME.git`;
    console.log(`\n🔧 For git clone, use: ${gitUrl}`);
    
  } catch (error) {
    console.error('❌ Token test failed:', error.response?.data || error.message);
  }
}

// Run the appropriate test
if (process.argv[2]) {
  testExistingToken(process.argv[2]);
} else {
  checkTokenType();
}