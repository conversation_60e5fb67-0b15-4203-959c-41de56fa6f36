/**
 * Test script for custom Daytona image with Claude Code CLI
 */

const { execSync } = require('child_process');
const assert = require('assert');

async function testClaudeCodeInSandbox() {
  console.log('Testing Claude Code CLI in Daytona sandbox...');
  
  try {
    // Test 1: Check if Claude Code CLI is installed
    console.log('\n1. Testing Claude Code CLI installation...');
    const version = execSync('claude --version', { encoding: 'utf8' });
    console.log(`✓ Claude Code CLI version: ${version.trim()}`);
    
    // Test 2: Check if Node.js is available
    console.log('\n2. Testing Node.js installation...');
    const nodeVersion = execSync('node --version', { encoding: 'utf8' });
    console.log(`✓ Node.js version: ${nodeVersion.trim()}`);
    
    // Test 3: Check npm installation
    console.log('\n3. Testing npm installation...');
    const npmVersion = execSync('npm --version', { encoding: 'utf8' });
    console.log(`✓ npm version: ${npmVersion.trim()}`);
    
    // Test 4: Check workspace directory
    console.log('\n4. Testing workspace directory...');
    const pwd = execSync('pwd', { encoding: 'utf8' });
    assert(pwd.includes('/home/<USER>/workspace'), 'Not in correct workspace directory');
    console.log(`✓ Working directory: ${pwd.trim()}`);
    
    // Test 5: Check user permissions
    console.log('\n5. Testing user permissions...');
    const whoami = execSync('whoami', { encoding: 'utf8' });
    assert(whoami.trim() === 'daytona', 'Not running as daytona user');
    console.log(`✓ Running as user: ${whoami.trim()}`);
    
    // Test 6: Check Claude config directory
    console.log('\n6. Testing Claude config directory...');
    const claudeDir = execSync('ls -la ~/.claude', { encoding: 'utf8' });
    console.log(`✓ Claude config directory exists`);
    
    // Test 7: Test git availability
    console.log('\n7. Testing git installation...');
    const gitVersion = execSync('git --version', { encoding: 'utf8' });
    console.log(`✓ Git version: ${gitVersion.trim()}`);
    
    console.log('\n🎉 All tests passed! Custom Daytona image is working correctly.');
    return true;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testClaudeCodeInSandbox()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testClaudeCodeInSandbox };