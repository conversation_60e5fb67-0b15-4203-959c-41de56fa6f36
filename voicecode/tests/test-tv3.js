#!/usr/bin/env node

const axios = require('axios');
const SandboxManager = require('../scripts/sandbox-manager');

class TV3Validator {
  constructor() {
    this.githubAuthService = process.env.GITHUB_AUTH_SERVICE || 'http://localhost:3000';
    this.sandboxRepoService = process.env.SANDBOX_REPO_SERVICE || 'http://localhost:3001';
    this.sandboxManager = new SandboxManager();
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting TV3 Validation Tests');
    console.log('================================\n');

    try {
      // Test 1: Public repository cloning
      await this.testPublicRepositoryCloning();
      
      // Test 2: Private repository access (requires GitHub token)
      const githubToken = await this.getGithubToken();
      if (githubToken) {
        await this.testPrivateRepositoryAccess(githubToken);
      } else {
        console.log('⏭️  Skipping private repository tests (no GitHub token available)');
      }
      
      // Test 3: Git operations validation
      await this.testGitOperations();
      
      // Test 4: Sandbox repository service API
      await this.testSandboxRepositoryAPI();
      
      // Test 5: Repository information retrieval
      await this.testRepositoryInfoAPI();
      
      this.printSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testPublicRepositoryCloning() {
    console.log('📋 Test 1: Public Repository Cloning');
    console.log('=====================================');
    
    const testRepo = 'https://github.com/octocat/Hello-World.git';
    const testName = `test-public-${Date.now()}`;
    
    try {
      console.log(`🔍 Testing: ${testRepo}`);
      
      const result = await this.sandboxManager.createSandboxWithRepo({
        repoUrl: testRepo,
        branch: 'master', // This repo uses 'master' branch
        sandboxName: testName
      });
      
      this.recordResult('Public Repository Cloning', true, 'Successfully cloned public repository');
      
      // Cleanup
      await this.sandboxManager.deleteSandbox(testName);
      console.log('✅ Test 1 PASSED\n');
      
    } catch (error) {
      this.recordResult('Public Repository Cloning', false, error.message);
      console.log('❌ Test 1 FAILED:', error.message);
      console.log('');
    }
  }

  async testPrivateRepositoryAccess(githubToken) {
    console.log('📋 Test 2: Private Repository Access');
    console.log('====================================');
    
    // Note: This would need to be updated with an actual private repo you have access to
    const testRepo = 'https://github.com/themrb/private-test-repo.git'; // Update this
    const testName = `test-private-${Date.now()}`;
    
    try {
      console.log(`🔍 Testing: ${testRepo} (with token)`);
      
      const result = await this.sandboxManager.createSandboxWithRepo({
        repoUrl: testRepo,
        branch: 'main',
        githubToken: githubToken,
        sandboxName: testName
      });
      
      this.recordResult('Private Repository Access', true, 'Successfully cloned private repository with token');
      
      // Cleanup
      await this.sandboxManager.deleteSandbox(testName);
      console.log('✅ Test 2 PASSED\n');
      
    } catch (error) {
      this.recordResult('Private Repository Access', false, error.message);
      console.log('❌ Test 2 FAILED:', error.message);
      console.log('ℹ️  Note: Make sure to update the private repo URL in the test');
      console.log('');
    }
  }

  async testGitOperations() {
    console.log('📋 Test 3: Git Operations Validation');
    console.log('====================================');
    
    const testRepo = 'https://github.com/octocat/Hello-World.git';
    const testName = `test-git-ops-${Date.now()}`;
    
    try {
      console.log('🔍 Testing git operations in sandbox');
      
      // Create sandbox with repository
      await this.sandboxManager.createSandboxWithRepo({
        repoUrl: testRepo,
        branch: 'master',
        sandboxName: testName
      });
      
      // Test additional git operations
      console.log('🔍 Testing branch listing...');
      await this.sandboxManager.executeInSandbox(testName, [
        'bash', '-c', 'cd repository && git branch -r'
      ]);
      
      console.log('🔍 Testing git log...');
      await this.sandboxManager.executeInSandbox(testName, [
        'bash', '-c', 'cd repository && git log --oneline -5'
      ]);
      
      console.log('🔍 Testing file listing...');
      await this.sandboxManager.executeInSandbox(testName, [
        'bash', '-c', 'cd repository && find . -name "*.md" -o -name "*.txt"'
      ]);
      
      this.recordResult('Git Operations Validation', true, 'All git operations completed successfully');
      
      // Cleanup
      await this.sandboxManager.deleteSandbox(testName);
      console.log('✅ Test 3 PASSED\n');
      
    } catch (error) {
      this.recordResult('Git Operations Validation', false, error.message);
      console.log('❌ Test 3 FAILED:', error.message);
      console.log('');
    }
  }

  async testSandboxRepositoryAPI() {
    console.log('📋 Test 4: Sandbox Repository Service API');
    console.log('==========================================');
    
    try {
      // Test health endpoint
      console.log('🔍 Testing health endpoint...');
      const healthResponse = await axios.get(`${this.sandboxRepoService}/health`);
      
      if (healthResponse.data.status === 'ok') {
        console.log('✅ Health endpoint working');
      } else {
        throw new Error('Health check failed');
      }
      
      // Test sandbox creation via API
      console.log('🔍 Testing sandbox creation API...');
      const createResponse = await axios.post(`${this.sandboxRepoService}/api/sandbox/create`, {
        repoUrl: 'https://github.com/octocat/Hello-World.git',
        branch: 'master'
      });
      
      const operationId = createResponse.data.operationId;
      console.log(`📝 Operation ID: ${operationId}`);
      
      // Poll for completion
      let completed = false;
      let attempts = 0;
      const maxAttempts = 30; // 5 minutes max
      
      while (!completed && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        
        const statusResponse = await axios.get(`${this.sandboxRepoService}/api/sandbox/status/${operationId}`);
        const status = statusResponse.data.status;
        
        console.log(`📊 Status: ${status} - ${statusResponse.data.message || ''}`);
        
        if (status === 'completed') {
          completed = true;
          console.log('✅ Sandbox created via API');
          
          // Cleanup
          const sandboxName = statusResponse.data.result?.sandboxName;
          if (sandboxName) {
            await axios.delete(`${this.sandboxRepoService}/api/sandbox/${sandboxName}`);
            console.log('🗑️  Sandbox cleaned up');
          }
        } else if (status === 'failed') {
          throw new Error(`Sandbox creation failed: ${statusResponse.data.message}`);
        }
        
        attempts++;
      }
      
      if (!completed) {
        throw new Error('Sandbox creation timed out');
      }
      
      this.recordResult('Sandbox Repository API', true, 'API endpoints working correctly');
      console.log('✅ Test 4 PASSED\n');
      
    } catch (error) {
      this.recordResult('Sandbox Repository API', false, error.message);
      console.log('❌ Test 4 FAILED:', error.message);
      console.log('ℹ️  Note: Make sure sandbox repository service is running on port 3001');
      console.log('');
    }
  }

  async testRepositoryInfoAPI() {
    console.log('📋 Test 5: Repository Information API');
    console.log('=====================================');
    
    try {
      console.log('🔍 Testing repository info endpoint...');
      
      const response = await axios.get(`${this.sandboxRepoService}/api/repository/info`, {
        params: {
          repoUrl: 'https://github.com/octocat/Hello-World.git'
        }
      });
      
      const repoInfo = response.data;
      
      console.log('📊 Repository Info:');
      console.log(`  Name: ${repoInfo.name}`);
      console.log(`  Full Name: ${repoInfo.fullName}`);
      console.log(`  Private: ${repoInfo.private}`);
      console.log(`  Default Branch: ${repoInfo.defaultBranch}`);
      console.log(`  Branches: ${repoInfo.branches.length}`);
      
      if (repoInfo.name && repoInfo.branches && repoInfo.branches.length > 0) {
        this.recordResult('Repository Information API', true, 'Repository info retrieved successfully');
        console.log('✅ Test 5 PASSED\n');
      } else {
        throw new Error('Invalid repository information returned');
      }
      
    } catch (error) {
      this.recordResult('Repository Information API', false, error.message);
      console.log('❌ Test 5 FAILED:', error.message);
      console.log('');
    }
  }

  async getGithubToken() {
    try {
      // Check if we have a stored token from previous OAuth
      // This is a simplified approach - in practice you'd have proper token management
      console.log('🔍 Checking for GitHub token...');
      
      // For testing, you can manually set a token here
      const token = process.env.GITHUB_TEST_TOKEN;
      
      if (token) {
        console.log('✅ GitHub token found (from environment)');
        return token;
      }
      
      console.log('⚠️  No GitHub token available for private repository testing');
      console.log('ℹ️  Set GITHUB_TEST_TOKEN environment variable to test private repos');
      return null;
      
    } catch (error) {
      console.log('⚠️  Could not retrieve GitHub token:', error.message);
      return null;
    }
  }

  recordResult(testName, passed, message) {
    this.testResults.push({
      test: testName,
      passed,
      message,
      timestamp: new Date()
    });
  }

  printSummary() {
    console.log('📊 TV3 Validation Summary');
    console.log('=========================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`\n✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${total - passed}/${total}\n`);
    
    for (const result of this.testResults) {
      const icon = result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.test}: ${result.message}`);
    }
    
    console.log('\n🎯 TV3 Status:', passed === total ? 'COMPLETE ✅' : 'INCOMPLETE ❌');
    
    if (passed === total) {
      console.log('\n🎉 All TV3 requirements validated successfully!');
      console.log('   - ✅ Custom Daytona image creation');
      console.log('   - ✅ Repository cloning in sandbox');
      console.log('   - ✅ Git operations functionality');
      console.log('   - ✅ API service integration');
    } else {
      console.log('\n🔧 Some tests failed. Please review and fix issues before proceeding to TV4.');
    }
  }
}

// CLI interface
if (require.main === module) {
  const validator = new TV3Validator();
  
  console.log('VoiceCode TV3 Validation Test Suite');
  console.log('Testing: Repository Pulling in Sandbox\n');
  
  validator.runAllTests().catch(error => {
    console.error('Test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = TV3Validator;