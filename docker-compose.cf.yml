services:
  voicecode-api:
    build:
      context: ./voicecode/fastapi-app
      dockerfile: Dockerfile
    container_name: voicecode-api
    ports:
      - "${API_HOST_PORT:-9100}:${UVICORN_PORT:-9100}"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    volumes:
      - ./voicecode/fastapi-app:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${UVICORN_PORT:-9100}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - voicecode-network

  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: voicecode-cloudflared
    command: tunnel --no-autoupdate run --token ${CLOUDFLARE_TUNNEL_TOKEN}
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    environment:
      - CLOUDFLARE_TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    depends_on:
      - voicecode-api
    restart: unless-stopped
    networks:
      - voicecode-network

  postgres:
    image: postgres:${POSTGRES_VERSION:-15-alpine}
    container_name: voicecode-postgres
    ports:
      - "${POSTGRES_HOST_PORT:-9432}:5432"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    environment:
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./voicecode/fastapi-app/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-voicecode} -d ${POSTGRES_DB:-voicecode}"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - voicecode-network

  redis:
    image: redis:${REDIS_VERSION:-6.2-alpine}
    container_name: voicecode-redis
    ports:
      - "${REDIS_HOST_PORT:-9379}:6379"
    env_file:
      - ./voicecode/fastapi-app/.env.docker
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - voicecode-network

networks:
  voicecode-network:
    driver: bridge
    name: voicecode-network

volumes:
  postgres-data:
  redis-data: