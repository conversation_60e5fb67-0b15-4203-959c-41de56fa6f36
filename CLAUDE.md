# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

VoiceCode is a mobile application that enables developers to interact with their GitHub repositories using voice commands through Claude Code CLI in sandboxed environments. The app bridges voice input with Claude Code CLI execution, allowing developers to perform complex code operations through natural language voice commands.

**Key Components:**
- React Native mobile app (iOS/Android)
- Voice processing pipeline using on-device speech recognition
- Daytona sandbox integration for secure execution
- Claude Code CLI integration with authentication bridge
- GitHub Apps integration for repository access

## Architecture

**Core Technical Architecture:**
- **Mobile App**: React Native with TypeScript for cross-platform development
- **Backend Services**: Node.js/Express API services in monorepo structure
- **Database**: PostgreSQL for user data, Redis for caching
- **Sandbox Environment**: Daytona sandboxes with Claude Code CLI pre-installed
- **Authentication**: OAuth 2.0 with GitHub Apps and Claude Code token bridge
- **Voice Processing**: On-device speech recognition via Capacitor community plugin

**Key Technical Bridges:**
1. **Authentication Bridge**: Uses `claude setup-token` in sandbox with OAuth URL extraction via log streaming
2. **Voice Processing Pipeline**: Speech-to-text → Command server in sandbox → Claude Code CLI execution
3. **Network Architecture**: Fire-and-forget requests with real-time status updates and hook-based notifications

## Development Setup

Since this is a planning-stage repository, implementation will require:

1. **Mobile App Setup**: Initialize React Native project with TypeScript
2. **Backend Services**: Set up Node.js API services with Express
3. **Database**: Configure PostgreSQL and Redis instances
4. **Sandbox Management**: Integrate Daytona SDK for sandbox lifecycle management
5. **Authentication**: Implement GitHub Apps OAuth and Claude Code token bridge
6. **Voice Processing**: Implement on-device speech recognition using Capacitor community plugin

## Common Development Commands

*These will be populated once the codebase is implemented:*
- Build: `npm run build`
- Test: `npm run test`  
- Lint: `npm run lint`
- Start Development: `npm run dev`
- Mobile Build: `npm run ios` / `npm run android`

## Technical Validation Requirements

**TV1: Custom Daytona Image Creation**
- Dockerfile with Claude Code CLI pre-installed
- Image builds successfully in Daytona environment
- Claude Code CLI functional in sandbox

**TV2: GitHub Apps Integration**
- OAuth flow with repository read/write permissions
- Repository listing via GitHub API
- Branch creation and pull request submission capabilities
- Secure token management

**TV3: Repository Pulling in Sandbox**
- Sandbox creation with specific repository and branch
- Git operations functional in sandbox environment

**TV4: Request Mechanism to Sandbox**
- HTTP server in sandbox for command processing
- Command execution endpoint with Claude Code CLI
- Response mechanism back to mobile client

## Key Implementation Notes

- **Monorepo Structure**: Single repository with mobile app, backend services, and shared utilities
- **Microservices Architecture**: Voice processing, GitHub integration, sandbox management, and notification services
- **Real-time Features**: Log streaming via Daytona SDK, Claude Code hooks for completion notifications
- **Security**: OAuth 2.0, encrypted token storage, sandbox isolation, audit logging
- **Performance**: <3 second voice processing, 85%+ command success rate, real-time log streaming

## Project Documentation

Comprehensive project documentation is available in the `docs/` folder:
- `brief.md`: Executive summary and project overview
- `mvp-prd.md`: Technical validation requirements  
- `prd.md`: Complete product requirements document
- `brainstorming-session-results.md`: Technical architecture solutions
- `claude_code_hook.txt`: Claude Code hooks documentation
- `daytona_log_streaming.txt`: Daytona log streaming integration guide

## Current Status

This repository is in the planning and design phase. The project has completed:
- Technical architecture validation through brainstorming
- Complete product requirements documentation
- Technical validation roadmap definition

Next steps involve implementing the technical validation requirements before full application development.

## Claude Code Usage Guidelines

- Only use serena for finding, do not use serena to edit

## Development Environment Notes

- Remember to use uv and our venv for the backend is in @voicecode/fastapi-app/