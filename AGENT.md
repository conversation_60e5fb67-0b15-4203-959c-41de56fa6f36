# AGENT.md

## Project Overview
VoiceCode is a mobile app for developers to interact with GitHub repositories using voice commands through Claude Code CLI in sandboxed environments.

## Architecture & Structure
- **Core Services**: Node.js microservices (`voicecode/services/`)
  - `github-auth`: GitHub Apps OAuth service with JWT/Redis caching
  - `sandbox-repo`: Repository management service
- **Scripts**: Management tools (`voicecode/scripts/`)
  - `sandbox-manager.js`: Creates Daytona sandboxes with repo cloning
- **External Dependencies**: Daytona SDK, Capacitor Speech Recognition, PostgreSQL, Redis

## Build/Test Commands
```bash
# Service development
cd voicecode/services/github-auth && npm run dev
cd voicecode/services/sandbox-repo && npm run dev

# Testing
cd voicecode/services/github-auth && npm test
cd voicecode/services/sandbox-repo && npm test

# Sandbox management
node voicecode/scripts/sandbox-manager.js create <repo-url> [--branch <branch>] [--token <token>]
```

## Code Style & Conventions
- ES6+ JavaScript with destructuring, async/await
- Express.js APIs with security middleware (helmet, cors, rate limiting)
- Environment variables with `dotenv`
- Error handling with try/catch blocks and proper error messages
- Logging: console.log with emoji prefixes (🚀 🔑 ✅ ❌)
- Token validation: GitHub tokens (ghp_, gho_, ghu_, github_pat_ prefixes)
- Naming: camelCase for variables, kebab-case for services
