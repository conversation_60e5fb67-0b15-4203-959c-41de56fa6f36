version: '3.8'

services:
  voicecode-api:
    build:
      context: ./voicecode/fastapi-app
      dockerfile: Dockerfile
      target: production
    image: voicecode-api:production
    container_name: voicecode-api-prod
    ports:
      - "${API_HOST_PORT:-9100}:${UVICORN_PORT:-9100}"
    env_file:
      - ./voicecode/fastapi-app/.env.production
    environment:
      - ENVIRONMENT=production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${UVICORN_PORT:-9100}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - voicecode-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  postgres:
    image: postgres:${POSTGRES_VERSION:-15-alpine}
    container_name: voicecode-postgres-prod
    ports:
      - "${POSTGRES_HOST_PORT:-5432}:5432"
    env_file:
      - ./voicecode/fastapi-app/.env.production
    environment:
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data-prod:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-voicecode} -d ${POSTGRES_DB:-voicecode}"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - voicecode-network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  redis:
    image: redis:${REDIS_VERSION:-6.2-alpine}
    container_name: voicecode-redis-prod
    ports:
      - "${REDIS_HOST_PORT:-6379}:6379"
    env_file:
      - ./voicecode/fastapi-app/.env.production
    volumes:
      - redis-data-prod:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - voicecode-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  voicecode-landing-page:
    build:
      context: ./voicecode-landing-page
      dockerfile: Dockerfile.prod
      target: production
    image: voicecode-landing-page:production
    container_name: voicecode-landing-page-prod
    ports:
      - "${LANDING_PAGE_HOST_PORT:-9101}:${VITE_PORT:-9101}"
    environment:
      - NODE_ENV=production
      - VITE_PORT=9101
    restart: unless-stopped
    networks:
      - voicecode-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

networks:
  voicecode-network:
    driver: bridge
    name: voicecode-network-prod

volumes:
  postgres-data-prod:
    driver: local
  redis-data-prod:
    driver: local