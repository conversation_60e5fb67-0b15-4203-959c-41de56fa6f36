output "instance_id" {
  description = "ID of the EC2 instance"
  value       = aws_instance.docker_instance.id
}

output "instance_public_ip" {
  description = "Public IP address of the EC2 instance"
  value       = aws_instance.docker_instance.public_ip
}

output "instance_public_dns" {
  description = "Public DNS name of the EC2 instance"
  value       = aws_instance.docker_instance.public_dns
}

output "instance_private_ip" {
  description = "Private IP address of the EC2 instance"
  value       = aws_instance.docker_instance.private_ip
}

output "security_group_id" {
  description = "ID of the security group"
  value       = aws_security_group.docker_sg.id
}

output "key_pair_name" {
  description = "Name of the key pair"
  value       = aws_key_pair.docker_instance_key.key_name
}

output "instance_profile_name" {
  description = "Name of the IAM instance profile"
  value       = aws_iam_instance_profile.docker_instance_profile.name
}

output "ssh_connection_command" {
  description = "SSH command to connect to the instance"
  value       = "ssh -i ~/.ssh/your-private-key ec2-user@${aws_instance.docker_instance.public_ip}"
}

output "docker_health_check_url" {
  description = "URL to check if Docker services are running"
  value       = "http://${aws_instance.docker_instance.public_ip}/health"
}

output "instance_tags" {
  description = "Tags applied to the instance"
  value       = aws_instance.docker_instance.tags
}

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.docker_vpc.id
}

output "subnet_id" {
  description = "ID of the public subnet"
  value       = aws_subnet.docker_public_subnet.id
}