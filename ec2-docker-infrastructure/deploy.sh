#!/bin/bash

# Quick deployment script for EC2 Docker infrastructure
# This script handles the complete deployment process

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install Terraform first."
        exit 1
    fi
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI first."
        exit 1
    fi
    
    if [ ! -f "terraform.tfvars" ]; then
        print_error "terraform.tfvars file not found. Please create it from terraform.tfvars.example"
        exit 1
    fi
    
    print_status "Prerequisites check passed!"
}

# Deploy infrastructure
deploy() {
    print_status "Starting infrastructure deployment..."
    
    # Initialize Terraform
    print_status "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    print_status "Planning deployment..."
    terraform plan
    
    # Apply deployment
    print_status "Applying deployment..."
    terraform apply -auto-approve
    
    print_status "Deployment completed successfully!"
    
    # Show outputs
    echo ""
    print_status "Deployment outputs:"
    terraform output
}

# Destroy infrastructure
destroy() {
    print_warning "This will destroy all infrastructure resources!"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" = "yes" ]; then
        print_status "Destroying infrastructure..."
        terraform destroy -auto-approve
        print_status "Infrastructure destroyed successfully!"
    else
        print_status "Destruction cancelled."
    fi
}

# Show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    Deploy the infrastructure (default)"
    echo "  destroy   Destroy the infrastructure"
    echo "  plan      Show deployment plan"
    echo "  output    Show deployment outputs"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 destroy"
    echo "  $0 plan"
}

# Main script logic
main() {
    local command="${1:-deploy}"
    
    case $command in
        deploy)
            check_prerequisites
            deploy
            ;;
        destroy)
            destroy
            ;;
        plan)
            check_prerequisites
            terraform plan
            ;;
        output)
            terraform output
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"