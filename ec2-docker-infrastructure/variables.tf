variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

variable "aws_profile" {
  description = "AWS profile to use for authentication"
  type        = string
  default     = null
}

variable "project_name" {
  description = "Name of the project - used for resource naming"
  type        = string
  default     = "docker-platform"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.medium"
  
  validation {
    condition = contains([
      "t3.micro", "t3.small", "t3.medium", "t3.large", "t3.xlarge",
      "t3a.micro", "t3a.small", "t3a.medium", "t3a.large", "t3a.xlarge",
      "m5.large", "m5.xlarge", "m5.2xlarge",
      "c5.large", "c5.xlarge", "c5.2xlarge"
    ], var.instance_type)
    error_message = "Instance type must be a valid EC2 instance type suitable for Docker workloads."
  }
}

variable "root_volume_size" {
  description = "Size of the root EBS volume in GB"
  type        = number
  default     = 30

  validation {
    condition     = var.root_volume_size >= 20 && var.root_volume_size <= 100
    error_message = "Root volume size must be between 20 and 100 GB."
  }
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
  
  validation {
    condition     = can(regex("^ssh-(rsa|ed25519|ecdsa)", var.ssh_public_key))
    error_message = "SSH public key must be a valid SSH public key starting with ssh-rsa, ssh-ed25519, or ssh-ecdsa."
  }
}

# Note: SSH private key not needed for provisioning
# Use your private key file directly for SSH connections

variable "allowed_ssh_cidr" {
  description = "CIDR block allowed for SSH access"
  type        = string
  default     = "0.0.0.0/0"  # Restrict this in production!
  
  validation {
    condition     = can(cidrhost(var.allowed_ssh_cidr, 0))
    error_message = "Allowed SSH CIDR must be a valid CIDR block."
  }
}

variable "app_port_start" {
  description = "Starting port for Docker application services"
  type        = number
  default     = 3000

  validation {
    condition     = var.app_port_start >= 1024 && var.app_port_start <= 65000
    error_message = "Application port start must be between 1024 and 65000."
  }
}

variable "app_port_end" {
  description = "Ending port for Docker application services"
  type        = number
  default     = 9999

  validation {
    condition     = var.app_port_end >= 1024 && var.app_port_end <= 65535
    error_message = "Application port end must be between 1024 and 65535."
  }
}

variable "enable_cloudwatch_monitoring" {
  description = "Enable CloudWatch monitoring for the instance"
  type        = bool
  default     = false
}

# Optional: Additional security group rules
variable "additional_security_group_rules" {
  description = "Additional security group rules for custom applications"
  type = list(object({
    from_port   = number
    to_port     = number
    protocol    = string
    cidr_blocks = list(string)
    description = string
  }))
  default = []
}