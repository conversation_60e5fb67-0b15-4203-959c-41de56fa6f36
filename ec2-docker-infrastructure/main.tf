terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region  = var.aws_region
  profile = var.aws_profile  # Optional: use profile from variables
}

# Data source for latest Amazon Linux 2023 AMI
data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["al2023-ami-*-x86_64"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# Data source for availability zones
data "aws_availability_zones" "available" {
  state = "available"
}

# VPC
resource "aws_vpc" "docker_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "${var.project_name}-vpc"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "docker_igw" {
  vpc_id = aws_vpc.docker_vpc.id

  tags = {
    Name        = "${var.project_name}-igw"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
  }
}

# Public Subnet
resource "aws_subnet" "docker_public_subnet" {
  vpc_id                  = aws_vpc.docker_vpc.id
  cidr_block              = "********/24"
  availability_zone       = data.aws_availability_zones.available.names[0]
  map_public_ip_on_launch = true

  tags = {
    Name        = "${var.project_name}-public-subnet"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
    Type        = "Public"
  }
}

# Route Table for Public Subnet
resource "aws_route_table" "docker_public_rt" {
  vpc_id = aws_vpc.docker_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.docker_igw.id
  }

  tags = {
    Name        = "${var.project_name}-public-rt"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
  }
}

# Route Table Association
resource "aws_route_table_association" "docker_public_rta" {
  subnet_id      = aws_subnet.docker_public_subnet.id
  route_table_id = aws_route_table.docker_public_rt.id
}

# Security Group for Docker services
resource "aws_security_group" "docker_sg" {
  name_prefix = "docker-instance-sg"
  description = "Security group for Docker-enabled EC2 instance"
  vpc_id      = aws_vpc.docker_vpc.id

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
    description = "SSH access"
  }

  # HTTP
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP access"
  }

  # HTTPS
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS access"
  }

  # Custom application ports (configurable range)
  ingress {
    from_port   = var.app_port_start
    to_port     = var.app_port_end
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Application ports for Docker services"
  }

  # All outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = {
    Name        = "${var.project_name}-docker-sg"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
  }
}

# IAM role for EC2 instance (for potential CloudWatch, ECR access, etc.)
resource "aws_iam_role" "docker_instance_role" {
  name = "${var.project_name}-docker-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-docker-instance-role"
    Environment = var.environment
  }
}

# IAM instance profile
resource "aws_iam_instance_profile" "docker_instance_profile" {
  name = "${var.project_name}-docker-instance-profile"
  role = aws_iam_role.docker_instance_role.name
}

# Optional: CloudWatch agent policy (for monitoring)
resource "aws_iam_role_policy_attachment" "cloudwatch_agent_policy" {
  count      = var.enable_cloudwatch_monitoring ? 1 : 0
  role       = aws_iam_role.docker_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

# Key pair for SSH access
resource "aws_key_pair" "docker_instance_key" {
  key_name   = "${var.project_name}-docker-key"
  public_key = var.ssh_public_key

  tags = {
    Name        = "${var.project_name}-docker-key"
    Environment = var.environment
  }
}

# EC2 Instance with Docker
resource "aws_instance" "docker_instance" {
  ami                    = data.aws_ami.amazon_linux.id
  instance_type          = var.instance_type
  key_name               = aws_key_pair.docker_instance_key.key_name
  vpc_security_group_ids = [aws_security_group.docker_sg.id]
  subnet_id              = aws_subnet.docker_public_subnet.id
  iam_instance_profile   = aws_iam_instance_profile.docker_instance_profile.name

  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.root_volume_size
    delete_on_termination = true
    encrypted             = true

    tags = {
      Name        = "${var.project_name}-docker-root-volume"
      Environment = var.environment
    }
  }

  user_data = base64encode(templatefile("${path.module}/user-data.sh", {
    project_name = var.project_name
    environment  = var.environment
  }))

  tags = {
    Name        = "${var.project_name}-docker-instance"
    Environment = var.environment
    Purpose     = "docker-infrastructure"
    AutoStart   = "true"
  }

  # Note: Docker installation happens via user-data script
  # Instance will be ready once cloud-init completes
}