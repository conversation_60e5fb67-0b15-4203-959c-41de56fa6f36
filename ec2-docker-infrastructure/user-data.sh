#!/bin/bash

# User data script for EC2 instance - Docker installation and configuration
# This script runs with root privileges during instance initialization

set -euo pipefail

# Enhanced error handling
error_exit() {
    echo "ERROR: $1" >&2
    exit 1
}

# Function to retry commands
retry() {
    local retries=$1
    shift
    local count=0
    until "$@"; do
        exit_code=$?
        wait=$((2 ** count))
        count=$((count + 1))
        if [ $count -lt $retries ]; then
            echo "Retry $count/$retries exited $exit_code, retrying in $wait second(s)..."
            sleep $wait
        else
            echo "Retry $count/$retries exited $exit_code, no more retries left."
            return $exit_code
        fi
    done
    return 0
}

# Logging setup
exec > >(tee /var/log/user-data.log)
exec 2>&1

echo "=== Starting Docker installation and configuration ==="
echo "Timestamp: $(date)"
echo "Instance ID: $(curl -s http://***************/latest/meta-data/instance-id)"

# Update system packages
echo "=== Updating system packages ==="
retry 3 dnf update -y

# Install required packages with conflict resolution
echo "=== Installing required packages ==="
retry 3 dnf install -y --allowerasing \
    git \
    curl \
    wget \
    unzip \
    htop \
    tree \
    jq \
    vim \
    tmux \
    docker \
    python3-pip \
    nodejs \
    npm || error_exit "Failed to install required packages"

# Start and enable Docker service
echo "=== Starting and enabling Docker service ==="
systemctl start docker
systemctl enable docker

# Add ec2-user to docker group
echo "=== Adding ec2-user to docker group ==="
usermod -aG docker ec2-user

# Install Docker Compose (stable version)
echo "=== Installing Docker Compose ==="
DOCKER_COMPOSE_VERSION="v2.25.0"
echo "Installing Docker Compose version: $DOCKER_COMPOSE_VERSION"
curl -L "https://github.com/docker/compose/releases/download/$DOCKER_COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verify Docker Compose installation
/usr/local/bin/docker-compose --version || {
    echo "Docker Compose installation failed, trying alternative method..."
    # Fallback installation using pip
    python3 -m pip install docker-compose
}

# Create symbolic link for 'docker compose' command (new syntax)
ln -sf /usr/local/bin/docker-compose /usr/local/bin/docker-compose-v1

# Install AWS CLI v2 (useful for ECR, S3, etc.)
echo "=== Installing AWS CLI v2 ==="
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws/

# Install pnpm (for Node.js projects)
echo "=== Installing pnpm package manager ==="
npm install -g pnpm

# Install additional development tools
echo "=== Installing additional development tools ==="
# Install docker-compose plugin if available
dnf install -y docker-compose-plugin || echo "docker-compose-plugin not available, using standalone version"

# Create docker directory structure
echo "=== Setting up Docker directory structure ==="
mkdir -p /home/<USER>/docker-projects
mkdir -p /home/<USER>/docker-data
mkdir -p /home/<USER>/docker-logs
chown -R ec2-user:ec2-user /home/<USER>/docker-*

# Create useful Docker aliases and environment setup
echo "=== Setting up Docker environment ==="
cat << 'EOF' >> /home/<USER>/.bashrc

# Docker aliases and functions
alias dps='docker ps'
alias dpsa='docker ps -a'
alias di='docker images'
alias drmi='docker rmi'
alias dex='docker exec -it'
alias dlog='docker logs -f'
alias dcp='docker-compose'
alias dcup='docker-compose up -d'
alias dcdown='docker-compose down'
alias dcps='docker-compose ps'
alias dclogs='docker-compose logs -f'

# Node.js and pnpm aliases
alias pn='pnpm'
alias pni='pnpm install'
alias pnd='pnpm dev'
alias pnb='pnpm build'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git pull'

# Docker cleanup function
docker-cleanup() {
    echo "Removing stopped containers..."
    docker container prune -f
    echo "Removing unused images..."
    docker image prune -f
    echo "Removing unused volumes..."
    docker volume prune -f
    echo "Removing unused networks..."
    docker network prune -f
    echo "Docker cleanup completed!"
}

# Docker system info function
docker-info() {
    echo "=== Docker System Information ==="
    docker version
    echo ""
    docker-compose version
    echo ""
    echo "=== System Resources ==="
    docker system df
    echo ""
    echo "=== Running Containers ==="
    docker ps
    echo ""
    echo "=== Node.js and pnpm versions ==="
    node --version
    npm --version
    pnpm --version
}

EOF

# Install ctop (container monitoring tool)
echo "=== Installing ctop for container monitoring ==="
wget https://github.com/bcicen/ctop/releases/download/v0.7.7/ctop-0.7.7-linux-amd64 -O /usr/local/bin/ctop
chmod +x /usr/local/bin/ctop

# Install lazydocker (Docker management TUI)
echo "=== Installing lazydocker ==="
curl https://raw.githubusercontent.com/jesseduffield/lazydocker/master/scripts/install_update_linux.sh | bash

# Create sample docker-compose.yml for testing
echo "=== Creating sample docker-compose.yml ==="
cat << 'EOF' > /home/<USER>/docker-projects/sample-docker-compose.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: sample-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./html:/usr/share/nginx/html:ro
    restart: unless-stopped

  redis:
    image: redis:alpine
    container_name: sample-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

volumes:
  redis-data:
EOF

# Create sample nginx configuration
cat << 'EOF' > /home/<USER>/docker-projects/nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ =404;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

# Create sample HTML file
mkdir -p /home/<USER>/docker-projects/html
cat << 'EOF' > /home/<USER>/docker-projects/html/index.html
<!DOCTYPE html>
<html>
<head>
    <title>Docker Instance Ready!</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background-color: #f4f4f4; 
        }
        .container { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            color: #28a745; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐳 Docker Instance is Ready!</h1>
        <p class="status">✅ Docker and Docker Compose are installed and running</p>
        <p><strong>Instance Details:</strong></p>
        <ul>
            <li>Docker version: Check with <code>docker --version</code></li>
            <li>Docker Compose version: Check with <code>docker-compose --version</code></li>
            <li>Sample project location: <code>/home/<USER>/docker-projects/</code></li>
        </ul>
        <p><strong>Useful Commands:</strong></p>
        <ul>
            <li><code>docker-info</code> - System information</li>
            <li><code>docker-cleanup</code> - Clean up unused resources</li>
            <li><code>ctop</code> - Container monitoring</li>
            <li><code>lazydocker</code> - Docker management TUI</li>
        </ul>
    </div>
</body>
</html>
EOF

# Set ownership for all created files
chown -R ec2-user:ec2-user /home/<USER>/docker-projects

# Configure log rotation for Docker
echo "=== Configuring Docker log rotation ==="
cat << 'EOF' > /etc/docker/daemon.json
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}
EOF

# Restart Docker to apply configuration
systemctl restart docker

# Enable automatic security updates
echo "=== Enabling automatic security updates ==="
dnf install -y dnf-automatic
systemctl enable --now dnf-automatic.timer

# Create startup script for optional services
cat << 'EOF' > /home/<USER>/startup-services.sh
#!/bin/bash
# Optional startup script for your docker-compose services
# Uncomment and modify as needed

# Example: Start sample services
# cd /home/<USER>/docker-projects
# docker-compose -f sample-docker-compose.yml up -d

echo "Startup script executed at $(date)" >> /home/<USER>/startup.log
EOF

chmod +x /home/<USER>/startup-services.sh
chown ec2-user:ec2-user /home/<USER>/startup-services.sh

# Final system verification
echo "=== Final system verification ==="
docker --version || echo "Docker installation issue detected"
docker-compose --version || echo "Docker Compose installation issue detected"
node --version || echo "Node.js installation issue detected"
npm --version || echo "npm installation issue detected"
pnpm --version || echo "pnpm installation issue detected"
git --version || echo "Git installation issue detected"
docker info
systemctl status docker --no-pager

# Create completion marker
echo "=== Docker installation completed successfully ==="
echo "Completion time: $(date)" > /home/<USER>/docker-setup-complete.txt
chown ec2-user:ec2-user /home/<USER>/docker-setup-complete.txt

echo "=== User data script completed successfully ==="
echo "The instance is ready for Docker workloads!"