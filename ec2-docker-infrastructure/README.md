# Docker-Ready EC2 Instance Infrastructure

This Terraform configuration provisions an AWS EC2 instance pre-configured with Dock<PERSON> and docker-compose, ready for your containerized applications.

## 🚀 Quick Start

### Prerequisites
- AWS CLI configured with appropriate credentials
- Terraform >= 1.0 installed
- SSH key pair generated

### 1. Generate SSH Key Pair
```bash
ssh-keygen -t ed25519 -f ~/.ssh/docker-instance-key -C "docker-instance"
```

### 2. Configure Variables
```bash
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values
```

### 3. Deploy Infrastructure

**Option A: Using the deployment script (Recommended)**
```bash
# Deploy everything automatically
./deploy.sh deploy

# Or just show the plan
./deploy.sh plan

# Check outputs
./deploy.sh output
```

**Option B: Manual Terraform commands**
```bash
# Initialize Terraform
terraform init

# Review the execution plan
terraform plan

# Apply the configuration
terraform apply
```

### 4. Connect to Your Instance
```bash
# Use the SSH command from terraform output
ssh -i ~/.ssh/docker-instance-key ec2-user@<PUBLIC_IP>

# Verify Docker installation
docker --version
docker-compose --version
```

## 📋 What's Included

### Software Stack
- **OS**: Amazon Linux 2023 (latest)
- **Docker**: Latest stable version
- **Docker Compose**: v2.25.0 (stable)
- **Node.js & npm**: Latest LTS version
- **pnpm**: Package manager for Node.js projects
- **AWS CLI v2**: For ECR and S3 integration
- **Monitoring Tools**: ctop, lazydocker
- **Development Tools**: git, curl, wget, jq, vim, tmux
- **Error Handling**: Retry logic and comprehensive logging

### Security Features
- ✅ Encrypted EBS root volume
- ✅ Security group with minimal required ports
- ✅ IAM role for AWS service integration
- ✅ Log rotation configured for Docker
- ✅ Automatic security updates enabled

### Pre-configured Features
- 🐳 Docker service auto-starts on boot
- 🔧 User `ec2-user` added to docker group
- 📁 Directory structure for Docker projects
- 🛠️ Useful Docker aliases and functions
- 📊 Container monitoring tools installed
- 🌐 Sample nginx + redis docker-compose setup

## 🔧 Configuration Options

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `aws_region` | AWS region | `us-east-1` | No |
| `project_name` | Project identifier | `docker-platform` | No |
| `environment` | Environment name | `dev` | No |
| `instance_type` | EC2 instance type | `t3.medium` | No |
| `root_volume_size` | Root volume size (GB) | `30` | No |
| `ssh_public_key` | SSH public key | - | **Yes** |
| `ssh_private_key` | SSH private key | - | **Yes** |
| `allowed_ssh_cidr` | SSH access CIDR | `0.0.0.0/0` | No |
| `app_port_start` | App port range start | `3000` | No |
| `app_port_end` | App port range end | `9999` | No |

## 🌐 Network & Security

### Security Group Rules
- **SSH (22)**: Configurable CIDR access
- **HTTP (80)**: Public access for web services
- **HTTPS (443)**: Public access for secure web services
- **Application Ports (3000-9999)**: Configurable range for Docker services

### Best Practices Applied
- Encrypted storage
- Least privilege IAM permissions
- Log rotation configured
- Automatic security updates
- Resource tagging for management

## 🛠️ Post-Deployment Usage

### Available Aliases
```bash
# Docker aliases
dps          # docker ps
dpsa         # docker ps -a
di           # docker images
dex          # docker exec -it
dcup         # docker-compose up -d
dcdown       # docker-compose down

# Node.js/pnpm aliases
pn           # pnpm
pni          # pnpm install
pnd          # pnpm dev
pnb          # pnpm build

# Git aliases
gs           # git status
ga           # git add
gc           # git commit
gp           # git push
gl           # git pull
```

### Management Functions
```bash
docker-info     # System information and status
docker-cleanup  # Clean unused containers, images, volumes
ctop           # Real-time container monitoring
lazydocker     # Docker management TUI
```

### Directory Structure
```
/home/<USER>/
├── docker-projects/     # Your docker-compose projects
├── docker-data/         # Persistent data volumes
├── docker-logs/         # Application logs
└── startup-services.sh  # Optional startup script
```

## 🧪 Testing Your Setup

### 1. Test Sample Application
```bash
cd /home/<USER>/docker-projects
docker-compose -f sample-docker-compose.yml up -d

# Check services
docker-compose ps

# Test nginx
curl http://localhost/health
```

### 2. Deploy Your Own Application
```bash
cd /home/<USER>/docker-projects
# Upload your docker-compose.yml
docker-compose up -d
```

## 📊 Monitoring & Maintenance

### System Monitoring
```bash
# Container resource usage
ctop

# Docker system information
docker system df
docker system events

# System resources
htop
df -h
```

### Maintenance Commands
```bash
# Update system packages
sudo dnf update -y

# Clean Docker resources
docker system prune -f

# View logs
docker-compose logs -f [service-name]
journalctl -u docker -f
```

## 🔄 Infrastructure Management

### Scaling
```bash
# Modify instance_type in terraform.tfvars
# Apply changes
terraform apply
```

### Backup Strategy
- EBS snapshots for data persistence
- Regular docker image backups to ECR
- Configuration files in version control

### Cleanup
```bash
# Destroy infrastructure when done
terraform destroy
```

## 🚨 Security Recommendations

### Production Hardening
1. **SSH Access**: Restrict `allowed_ssh_cidr` to your IP range
2. **Instance Profile**: Add only required AWS permissions
3. **Monitoring**: Enable CloudWatch monitoring
4. **Updates**: Regularly update system packages
5. **Secrets**: Use AWS Secrets Manager for sensitive data

### Network Security
```bash
# Example: Restrict SSH to office network
allowed_ssh_cidr = "***********/24"
```

## 📞 Troubleshooting

### Common Issues

**Docker not starting?**
```bash
sudo systemctl status docker
sudo journalctl -u docker -f
```

**Permission denied for docker commands?**
```bash
# Logout and login again, or:
sudo usermod -aG docker ec2-user
newgrp docker
```

**Package conflicts during installation?**
```bash
# The script uses --allowerasing to resolve conflicts
# Check installation logs:
sudo tail -100 /var/log/user-data.log
```

**Docker Compose not found?**
```bash
# Check if installation completed:
docker-compose --version
# If not, manual installation:
curl -L "https://github.com/docker/compose/releases/download/v2.25.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

**Instance not accessible?**
```bash
# Check security group rules in AWS Console
# Verify SSH key pair matches
aws ec2 describe-instances --instance-ids <INSTANCE_ID>
```

### Log Locations
- User data execution: `/var/log/user-data.log`
- Docker service: `journalctl -u docker`
- Application logs: `/home/<USER>/docker-logs/`

## 📝 Terraform Outputs

After deployment, Terraform provides:
- `instance_public_ip`: Connect via SSH
- `instance_public_dns`: Alternative connection method
- `ssh_connection_command`: Ready-to-use SSH command
- `docker_health_check_url`: Test endpoint for services

## 🤝 Contributing

This infrastructure-as-code follows DevOps best practices:
- Version controlled configuration
- Documented variables and outputs
- Security-first approach
- Monitoring and logging built-in
- Scalable and maintainable design

---

**Ready to deploy your Docker applications!** 🐳