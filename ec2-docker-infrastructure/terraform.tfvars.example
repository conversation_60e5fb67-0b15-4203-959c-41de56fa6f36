# Example Terraform variables file
# Copy this file to terraform.tfvars and customize with your values

# AWS Configuration
aws_region  = "us-east-1"
aws_profile = "your-profile-name"  # Optional: specify AWS profile

# Project Configuration
project_name = "my-docker-platform"
environment  = "dev"

# Instance Configuration
instance_type      = "t3.medium"  # Suitable for Docker workloads
root_volume_size   = 30           # GB - minimum 20GB recommended

# SSH Configuration
# Generate your SSH key pair:
# ssh-keygen -t ed25519 -f ~/.ssh/docker-instance-key -C "docker-instance"
ssh_public_key = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI... your-public-key-here"

# Network Security
allowed_ssh_cidr = "0.0.0.0/0"  # SECURITY: Restrict this to your IP range in production!

# Application Ports
app_port_start = 3000  # Starting port for your Docker services
app_port_end   = 9999  # Ending port for your Docker services

# Monitoring
enable_cloudwatch_monitoring = false  # Set to true for production

# Additional Security Group Rules (optional)
# Uncomment and customize if you need specific ports
# additional_security_group_rules = [
#   {
#     from_port   = 8080
#     to_port     = 8080
#     protocol    = "tcp"
#     cidr_blocks = ["0.0.0.0/0"]
#     description = "Custom application port"
#   }
# ]